# Android 集成指南

本文档介绍如何在 Android 原生应用中集成富文本编辑器并调用其功能。

## 1. 在 Android 中设置 WebView

首先，在 Android 应用中创建一个 WebView 并加载编辑器页面：

```java
// MainActivity.java
package com.work2502.richeditor;

import android.os.Bundle;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.webkit.WebSettings;
import android.webkit.JavascriptInterface;

import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {
    private WebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        webView = findViewById(R.id.webView);
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);

        // 添加 JavaScript 接口
        webView.addJavascriptInterface(new WebAppInterface(), "AndroidInterface");

        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // 页面加载完成后可以调用 JavaScript 方法
            }
        });

        // 加载编辑器页面
        webView.loadUrl("http://your-editor-url.com");
    }

    // JavaScript 接口类
    public class WebAppInterface {
        @JavascriptInterface
        public void onContentChanged(String content) {
            // 处理内容变化
            System.out.println("Editor content: " + content);
        }
    }
}
```

## 2. 调用编辑器功能

在 Android 中，您可以通过 WebView 的 `evaluateJavascript` 方法调用编辑器的功能：

```java
// 文本格式化方法

// 切换粗体
private void toggleBold() {
    webView.evaluateJavascript("window.editorBridge.toggleBold()", null);
}

// 切换斜体
private void toggleItalic() {
    webView.evaluateJavascript("window.editorBridge.toggleItalic()", null);
}

// 切换下划线
private void toggleUnderline() {
    webView.evaluateJavascript("window.editorBridge.toggleUnderline()", null);
}

// 标题方法

// 切换 H1 标题
private void toggleH1() {
    webView.evaluateJavascript("window.editorBridge.toggleH1()", null);
}

// 切换 H2 标题
private void toggleH2() {
    webView.evaluateJavascript("window.editorBridge.toggleH2()", null);
}

// 切换 H3 标题
private void toggleH3() {
    webView.evaluateJavascript("window.editorBridge.toggleH3()", null);
}

// 列表方法

// 切换项目符号列表
private void toggleBulletList() {
    webView.evaluateJavascript("window.editorBridge.toggleBulletList()", null);
}

// 切换有序列表
private void toggleOrderedList() {
    webView.evaluateJavascript("window.editorBridge.toggleOrderedList()", null);
}

// 切换任务列表
private void toggleTaskList() {
    webView.evaluateJavascript("window.editorBridge.toggleTaskList()", null);
}

// 文本对齐方法

// 设置文本左对齐
private void alignLeft() {
    webView.evaluateJavascript("window.editorBridge.alignLeft()", null);
}

// 设置文本居中对齐
private void alignCenter() {
    webView.evaluateJavascript("window.editorBridge.alignCenter()", null);
}

// 设置文本右对齐
private void alignRight() {
    webView.evaluateJavascript("window.editorBridge.alignRight()", null);
}

// 链接方法

// 添加链接
private void addLink(String url) {
    webView.evaluateJavascript("window.editorBridge.addLink('" + url + "')", null);
}

// 移除链接
private void removeLink() {
    webView.evaluateJavascript("window.editorBridge.removeLink()", null);
}

// 内容管理方法

// 获取编辑器内容
private void getContent() {
    webView.evaluateJavascript("window.editorBridge.getContent()", new ValueCallback<String>() {
        @Override
        public void onReceiveValue(String value) {
            // 处理返回的内容
            String content = value.substring(1, value.length() - 1); // 移除引号
            System.out.println("Editor content: " + content);
        }
    });
}

// 设置编辑器内容
private void setContent(String html) {
    webView.evaluateJavascript("window.editorBridge.setContent('" + html + "')", null);
}

// 清空编辑器内容
private void clearContent() {
    webView.evaluateJavascript("window.editorBridge.clearContent()", null);
}

// 撤销/重做方法

// 撤销上一步操作
private void undo() {
    webView.evaluateJavascript("window.editorBridge.undo()", null);
}

// 重做上一步操作
private void redo() {
    webView.evaluateJavascript("window.editorBridge.redo()", null);
}

// 状态检查方法

// 检查当前选中文本是否为粗体
private void isBold() {
    webView.evaluateJavascript("window.editorBridge.isBold()", new ValueCallback<String>() {
        @Override
        public void onReceiveValue(String value) {
            boolean isBold = Boolean.parseBoolean(value);
            System.out.println("Is bold: " + isBold);
        }
    });
}
```

## 3. 创建 UI 按钮

在 Android 布局中创建按钮，并将它们连接到上述方法：

```xml
<!-- activity_main.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnBold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="B"
                android:onClick="onBoldClick" />

            <Button
                android:id="@+id/btnItalic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="I"
                android:onClick="onItalicClick" />

            <!-- 添加更多按钮... -->

        </LinearLayout>
    </HorizontalScrollView>

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>
```

然后在 MainActivity 中实现这些点击处理方法：

```java
// 按钮点击处理方法
public void onBoldClick(View view) {
    toggleBold();
}

public void onItalicClick(View view) {
    toggleItalic();
}

// 添加更多点击处理方法...
```

## 4. 处理编辑器内容变化

要在内容变化时通知 Android 应用，可以在编辑器中添加事件监听器，并通过 JavaScript 接口调用 Android 方法：

```javascript
// 在 JavaScript 中添加内容变化监听器
editor.on('update', () => {
  const content = editor.getHTML();
  // 调用 Android 方法
  if (window.AndroidInterface && window.AndroidInterface.onContentChanged) {
    window.AndroidInterface.onContentChanged(content);
  }
});
```

## 5. 完整的方法列表

以下是可以从 Android 调用的所有编辑器方法：

### 文本格式化
- `toggleBold()` - 切换粗体
- `toggleItalic()` - 切换斜体
- `toggleUnderline()` - 切换下划线

### 标题
- `toggleH1()` - 切换 H1 标题
- `toggleH2()` - 切换 H2 标题
- `toggleH3()` - 切换 H3 标题

### 列表
- `toggleBulletList()` - 切换项目符号列表
- `toggleOrderedList()` - 切换有序列表
- `toggleTaskList()` - 切换任务列表

### 文本对齐
- `alignLeft()` - 设置文本左对齐
- `alignCenter()` - 设置文本居中对齐
- `alignRight()` - 设置文本右对齐

### 链接
- `addLink(url)` - 添加链接
- `removeLink()` - 移除链接

### 内容管理
- `getContent()` - 获取编辑器 HTML 内容
- `setContent(html)` - 设置编辑器 HTML 内容
- `clearContent()` - 清空编辑器内容

### 撤销/重做
- `undo()` - 撤销上一步操作
- `redo()` - 重做上一步操作

### 状态检查
- `isBold()` - 检查当前选中文本是否为粗体
- `isItalic()` - 检查当前选中文本是否为斜体
- `isUnderline()` - 检查当前选中文本是否有下划线
- `isH1()` - 检查当前是否为 H1 标题
- `isH2()` - 检查当前是否为 H2 标题
- `isH3()` - 检查当前是否为 H3 标题
- `isBulletList()` - 检查当前是否为项目符号列表
- `isOrderedList()` - 检查当前是否为有序列表
- `isTaskList()` - 检查当前是否为任务列表
- `getTextAlign()` - 获取当前文本对齐方式
- `isLink()` - 检查当前选中文本是否为链接
```mermaid
graph TD
    A[ComposerActivity] --> B[BaseComposerActivity]
    B --> C[BaseComposerFragment]
    C --> |Loads Layout| D[composer_fragment_main.xml]
    C --> |Contains Logic| E[View Logic Presenter]
    D --> F[comp_view_decor_layout.xml]
    F --> |Rich Text 装饰布局| G[Overlay, Progress Bar, Zoom Text]
    C --> H[ComposerViewContainer]
    H --> I[ComposerView]
    I --> |Loads Native Code| J[SO Files]

    I --> |Integrates SDK| K[SpenComposer]
    K --> L[SpenComposerImpl]
    L --> |Rendering| M[TextureView]
    L --> N[SpenDrawLoopTexture]
    M --> |Transparent Surface| O[SurfaceTexture]
    N --> |Local Rendering| P[Rendering Logic]
```