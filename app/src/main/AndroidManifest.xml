<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" android:minSdkVersion="33" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:name=".ComposeApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Compose25003"
        tools:targetApi="31">
        <activity
            android:name="com.work2502.htmltodolist.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Compose25003">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".richtext.SecondActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".carsh.FocusCrashActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".carsh.FocusCrashSolutionActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".carsh.PointerInputFocusCrashActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".carsh.PointerInputFocusCrashSolutionActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".carsh.CustomFocusLogicCrashActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>

        <activity
            android:name=".coil.CoilImageListActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".bottomsheet.BottomSheetTestActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".bottomsheet.TransparentBottomSheetActivity"
            android:exported="true"
            android:theme="@style/Theme.Translucent.NoTitleBar">
        </activity>
        <activity
            android:name=".richtext.QuillRichTextEditorActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".richtext.TinyMCEEditorActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".richtext.SlateEditorActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".richtext.TiptapEditorActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".richtext.RemirrorEditorActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateHidden"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name=".webview.FullscreenInputWebViewActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.htmltodolist.ScrollableEditTextActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.htmltodolist.TodoListActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.htmltodolist.TodoListComposeActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.syncscroll.SyncScrollActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.zoomtext.view.ScalableScrollViewExample"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.zoomtext.view.ZoomTextActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.multitouch.MultiTouchActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.multitouch.MultiTouchGroupActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.multitouch.MultiTouchComposeActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.multitouch.EnhancedMultiTouchComposeActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.multitouch.InfiniteCanvasActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.multitouch.NestedScrollActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003">
        </activity>
        <activity
            android:name="com.work2502.zoomtext.view.ScalableScrollViewActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003"/>
        <activity
            android:name="com.work2502.zoomtext.view.PdfLikeReaderActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003"/>
        <activity
            android:name="com.work2502.zoomtext.compose.PdfLikeReaderComposeActivity"
            android:exported="true"
            android:theme="@style/Theme.Compose25003"/>
    </application>

</manifest>