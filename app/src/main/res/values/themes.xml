<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Compose25003" parent="Theme.AppCompat.DayNight.DarkActionBar" />

    <!-- 透明主题，用于底部弹窗 Activity -->
    <style name="Theme.Translucent.NoTitleBar" parent="android:Theme.Translucent.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:backgroundDimEnabled">false</item>
<!--        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>-->
    </style>
</resources>