<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="0dp">

    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="无限画布示例"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:padding="8dp"
        android:background="#E0E0E0"
        app:layout_constraintTop_toTopOf="parent" />

    <com.work2502.multitouch.InfiniteCanvasViewGroup
        android:id="@+id/infiniteCanvasViewGroup"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#F5F5F5"
        app:layout_constraintTop_toBottomOf="@id/titleTextView"
        app:layout_constraintBottom_toTopOf="@id/controlPanel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <LinearLayout
        android:id="@+id/controlPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp"
        android:background="#E0E0E0"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/infoTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="偏移量: (0, 0)"
            android:textSize="14sp"
            android:gravity="center"
            android:padding="4dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/resetButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="重置视图"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/toggleBoundaryButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="启用边界限制" />

        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
