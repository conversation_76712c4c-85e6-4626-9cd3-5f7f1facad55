<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="0dp">

    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="嵌套滚动示例"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:padding="8dp"
        android:background="#E0E0E0"
        app:layout_constraintTop_toTopOf="parent" />

    <com.work2502.multitouch.CustomNestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@id/titleTextView"
        app:layout_constraintBottom_toTopOf="@id/controlPanel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.work2502.multitouch.CustomHorizontalScrollView
            android:id="@+id/horizontalScrollView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fillViewport="true">

            <LinearLayout
                android:id="@+id/contentLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical" />
        </com.work2502.multitouch.CustomHorizontalScrollView>

    </com.work2502.multitouch.CustomNestedScrollView>

    <LinearLayout
        android:id="@+id/controlPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp"
        android:background="#E0E0E0"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/infoTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="滚动位置: (0, 0)"
            android:textSize="14sp"
            android:gravity="center"
            android:padding="4dp" />

        <Button
            android:id="@+id/resetButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="重置位置"
            android:layout_gravity="center" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
