<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_500"
        android:gravity="center"
        android:padding="12dp"
        android:text="同步滚动示例"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:background="#CCCCCC"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleTextView" />

    <TextView
        android:id="@+id/leftTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#E0E0E0"
        android:gravity="center"
        android:padding="8dp"
        android:text="原文"
        android:textColor="#333333"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/divider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleTextView" />

    <TextView
        android:id="@+id/rightTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#E0E0E0"
        android:gravity="center"
        android:padding="8dp"
        android:text="译文"
        android:textColor="#333333"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/divider"
        app:layout_constraintTop_toBottomOf="@id/titleTextView" />

    <!-- 左侧滚动视图 -->
    <com.work2502.syncscroll.BaseTableScrollView
        android:id="@+id/leftScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/divider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/leftTitle">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
           >

            <TextView
                android:id="@+id/leftTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="6dp"
                android:textColor="#333333"
                android:textSize="16sp"
                tools:text="左侧内容" />

        </LinearLayout>
    </com.work2502.syncscroll.BaseTableScrollView>

    <!-- 右侧滚动视图 -->
    <com.work2502.syncscroll.BaseTableScrollView
        android:id="@+id/rightScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/divider"
        app:layout_constraintTop_toBottomOf="@id/rightTitle">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
           >

            <TextView
                android:id="@+id/rightTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="6dp"
                android:textColor="#333333"
                android:textSize="16sp"
                tools:text="右侧内容" />

        </LinearLayout>
    </com.work2502.syncscroll.BaseTableScrollView>

    <!-- 滚动指示器 -->
    <FrameLayout
        android:id="@+id/scrollIndicatorContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/scroll_indicator_background"
        android:padding="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/scrollIndicatorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            tools:text="50%" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
