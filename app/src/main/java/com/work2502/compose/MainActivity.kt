package com.work2502.compose

import android.os.Bundle
import android.content.Intent
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.work2502.compose.carsh.CustomFocusLogicCrashActivity
import com.work2502.compose.carsh.FocusCrashActivity
import com.work2502.compose.carsh.FocusCrashSolutionActivity
import com.work2502.compose.carsh.PointerInputFocusCrashActivity
import com.work2502.compose.carsh.PointerInputFocusCrashSolutionActivity
import com.work2502.compose.richtext.QuillRichTextEditorActivity
import com.work2502.compose.richtext.RemirrorEditorActivity
import com.work2502.compose.richtext.SlateEditorActivity
import com.work2502.compose.richtext.TinyMCEEditorActivity
import com.work2502.compose.richtext.TiptapEditorActivity
import com.work2502.compose.webview.FullscreenInputWebViewActivity
import com.work2502.compose.ui.theme.Compose25003Theme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Compose25003Theme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    val focusManager = LocalFocusManager.current

                    Greeting(
                        name = "Android",
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding)
                            .navigationBarsPadding(),
                        onButtonClick = {
                            focusManager.clearFocus()
                            // 展示选择对话框
                            val intent = Intent(this, PointerInputFocusCrashActivity::class.java)
                            startActivity(intent)
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun Greeting(name: String, modifier: Modifier = Modifier, onButtonClick: () -> Unit) {
    Box(modifier = modifier.fillMaxHeight()) {
        Column(
            modifier = Modifier
                .height(500.dp)
                .imePadding()
                .background(Color.Gray)
                .align(Alignment.BottomCenter)
        ) {
            Column(modifier = Modifier.fillMaxWidth().verticalScroll(rememberScrollState())) {
                Row(modifier = Modifier.fillMaxWidth().height(50.dp)) {
                    TextField(
                        modifier = Modifier.weight(1f),
                        value = "",
                        onValueChange = {},
                    )
                }

                // 添加多个按钮来启动不同的 Activity
                val context = LocalContext.current

                Button(
                    onClick = onButtonClick,
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("PointerInput 焦点崩溃演示")
                }

                Button(
                    onClick = {
                        context.startActivity(Intent(context, FocusCrashActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("焦点管理崩溃演示")
                }

                Button(
                    onClick = {
                        context.startActivity(Intent(context, FocusCrashSolutionActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("焦点管理解决方案")
                }

                Button(
                    onClick = {
                        context.startActivity(Intent(context, PointerInputFocusCrashSolutionActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("PointerInput 解决方案")
                }
                Button(
                    onClick = {
                        context.startActivity(Intent(context, CustomFocusLogicCrashActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("自定义焦点逻辑崩溃")
                }

                // LazyColumn 模式
                Button(
                    onClick = {
                        val intent = Intent(context, com.work2502.compose.coil.CoilImageListActivity::class.java)
                        intent.putExtra("display_mode", com.work2502.compose.coil.CoilImageListActivity.DISPLAY_MODE_LAZY_COLUMN)
                        context.startActivity(intent)
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("Coil图片加载(LazyColumn)")
                }

                // 普通 Column 模式
                Button(
                    onClick = {
                        val intent = Intent(context, com.work2502.compose.coil.CoilImageListActivity::class.java)
                        intent.putExtra("display_mode", com.work2502.compose.coil.CoilImageListActivity.DISPLAY_MODE_COLUMN)
                        context.startActivity(intent)
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("Coil图片加载(Column)")
                }

                // 底部弹窗测试
                Button(
                    onClick = {
                        context.startActivity(Intent(context, com.work2502.compose.bottomsheet.BottomSheetTestActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("底部弹窗响应延迟测试")
                }

                // Quill.js 富文本编辑器
                Button(
                    onClick = {
                        context.startActivity(Intent(context, QuillRichTextEditorActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("Quill.js 富文本编辑器")
                }

                // TinyMCE 富文本编辑器
                Button(
                    onClick = {
                        context.startActivity(Intent(context, TinyMCEEditorActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("TinyMCE 富文本编辑器")
                }

                // Slate.js 富文本编辑器
                Button(
                    onClick = {
                        context.startActivity(Intent(context, SlateEditorActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("Slate.js 富文本编辑器")
                }

                // Tiptap.js 富文本编辑器
                Button(
                    onClick = {
                        context.startActivity(Intent(context, TiptapEditorActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("Tiptap.js 富文本编辑器")
                }

                // Remirror.js 富文本编辑器
                Button(
                    onClick = {
                        context.startActivity(Intent(context, RemirrorEditorActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("Remirror.js 富文本编辑器")
                }

                // 全屏输入框 WebView 示例
                Button(
                    onClick = {
                        context.startActivity(Intent(context, FullscreenInputWebViewActivity::class.java))
                    },
                    modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
                ) {
                    Text("全屏输入框 WebView 示例")
                }
            }
            Column(
                modifier = Modifier
            ) {
                Text(
                    text = "Hello $name! 我是 谁 我在哪里".repeat(100),
                )
            }


        }
    }
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    Compose25003Theme {
        Greeting("Android", onButtonClick = {})
    }
}
