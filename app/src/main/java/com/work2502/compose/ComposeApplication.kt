package com.work2502.compose

import android.app.Application
import android.content.Context
import android.util.Log
import coil3.ImageLoader
import coil3.gif.AnimatedImageDecoder
import coil3.util.DebugLogger

/**
 * 自定义 Application 类，用于初始化全局组件
 */
class ComposeApplication : Application() {
    
    companion object {
        private const val TAG = "ComposeApplication"
        
        // ImageLoader 单例
        private var imageLoaderInstance: ImageLoader? = null
        
        /**
         * 获取 ImageLoader 单例
         */
        fun getImageLoader(context: Context): ImageLoader {
            if (imageLoaderInstance == null) {
                synchronized(ComposeApplication::class.java) {
                    if (imageLoaderInstance == null) {
                        Log.d(TAG, "Creating new ImageLoader instance")
                        imageLoaderInstance = ImageLoader.Builder(context.applicationContext)
                            .components {
                                // 添加 GIF 解码器
                                add(AnimatedImageDecoder.Factory())
                            }
                            .logger(DebugLogger()) // 在调试模式下记录日志
                            .build()
                    }
                }
            }
            return imageLoaderInstance!!
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        
        // 在应用启动时初始化 ImageLoader
        getImageLoader(this)
        
        Log.d(TAG, "Application initialized")
    }
}
