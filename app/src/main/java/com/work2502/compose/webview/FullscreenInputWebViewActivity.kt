package com.work2502.compose.webview

import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.ViewTreeObserver
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.work2502.compose.ui.theme.Compose25003Theme

private const val TAG = "FullscreenInputWebView"

/**
 * 设置软键盘检测
 */
private fun setupKeyboardDetection(webView: WebView) {
    val rootView = webView.rootView
    val rect = Rect()
    val visibilityThreshold = 200 // 像素阈值
    var isKeyboardVisible = false

    val listener = ViewTreeObserver.OnGlobalLayoutListener {
        rootView.getWindowVisibleDisplayFrame(rect)
        val heightDiff = rootView.height - rect.height()

        val isVisible = heightDiff > visibilityThreshold
        if (isVisible != isKeyboardVisible) {
            isKeyboardVisible = isVisible

            // 通知网页调整布局
            val script = "adjustForKeyboard($heightDiff);"
            webView.evaluateJavascript(script, null)

            Log.d(TAG, "软键盘状态变化: $isVisible, 高度: $heightDiff")
        }
    }

    rootView.viewTreeObserver.addOnGlobalLayoutListener(listener)
}

/**
 * 全屏输入框 WebView 示例 Activity
 * 演示软键盘遮挡问题及解决方案
 */
class FullscreenInputWebViewActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            Compose25003Theme {
                Scaffold(
                    modifier = Modifier.fillMaxSize()
                ) { innerPadding ->
                    Surface(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        FullscreenInputWebViewDemo()
                    }
                }
            }
        }
    }
}

@Composable
fun FullscreenInputWebViewDemo() {
    var enableKeyboardFix by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 开关控制是否启用软键盘修复
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "软键盘修复",
                style = MaterialTheme.typography.titleMedium
            )

            Column(
                modifier = Modifier.padding(vertical = 8.dp)
            ) {
                Switch(
                    checked = enableKeyboardFix,
                    onCheckedChange = { enableKeyboardFix = it }
                )

                Text(
                    text = if (enableKeyboardFix) "已启用软键盘修复" else "未启用软键盘修复",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }

        // WebView
        AndroidView(
            factory = { context ->
                WebView(context).apply {
                    // 设置布局参数
                    layoutParams = android.view.ViewGroup.LayoutParams(
                        android.view.ViewGroup.LayoutParams.MATCH_PARENT,
                        android.view.ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    settings.apply {
                        javaScriptEnabled = true
                        domStorageEnabled = true
                        allowFileAccess = true
                        useWideViewPort = true
                        loadWithOverviewMode = true
                    }

                    // 添加JavaScript接口
                    addJavascriptInterface(object {
                        @JavascriptInterface
                        fun isKeyboardFixEnabled(): Boolean {
                            return enableKeyboardFix
                        }

                        @JavascriptInterface
                        fun showToast(message: String) {
                            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                        }

                        @JavascriptInterface
                        fun logMessage(message: String) {
                            Log.d(TAG, message)
                        }
                    }, "Android")

                    // 设置WebViewClient以处理页面加载
                    webViewClient = object : android.webkit.WebViewClient() {
                        override fun onPageFinished(view: WebView?, url: String?) {
                            super.onPageFinished(view, url)
                            Log.d(TAG, "HTML页面加载完成")

                            // 通知页面更新键盘修复状态
                            view?.post {
                                view.evaluateJavascript("updateKeyboardFixIndicator();", null)
                            }

                            // 设置软键盘监听
                            if (view != null) {
                                setupKeyboardDetection(view)
                            }
                        }

                        override fun onReceivedError(view: WebView?, request: android.webkit.WebResourceRequest?, error: android.webkit.WebResourceError?) {
                            super.onReceivedError(view, request, error)
                            Log.e(TAG, "加载HTML页面出错: ${error?.description}")
                            Toast.makeText(context, "加载HTML页面出错: ${error?.description}", Toast.LENGTH_LONG).show()
                        }
                    }

                    // 加载HTML
                    loadUrl("file:///android_asset/fullscreen_input.html")
                    Log.d(TAG, "开始加载HTML页面")
                }
            },
            modifier = Modifier.weight(1f)
        )
    }
}
