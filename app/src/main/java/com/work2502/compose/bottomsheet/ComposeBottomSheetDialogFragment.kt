package com.work2502.compose.bottomsheet

import android.app.Dialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.work2502.compose.ui.theme.Compose25003Theme
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

/**
 * 使用 BottomSheetDialogFragment 实现的底部弹窗
 */
class ComposeBottomSheetDialogFragment : BottomSheetDialogFragment() {
    
    companion object {
        private const val TAG = "ComposeBottomSheet"
        
        // 记录弹窗关闭时间，用于测量响应延迟
        var dismissTimeMillis: Long = 0
        
        fun newInstance(): ComposeBottomSheetDialogFragment {
            return ComposeBottomSheetDialogFragment()
        }
    }
    
    private var onDismissListener: (() -> Unit)? = null
    
    fun setOnDismissListener(listener: () -> Unit) {
        onDismissListener = listener
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                Compose25003Theme {
                    BottomSheetContent(
                        onDismiss = {
                            // 记录关闭时间
                            dismissTimeMillis = System.currentTimeMillis()
                            Log.d(TAG, "Bottom sheet dismissed at: $dismissTimeMillis")
                            dismiss()
                        }
                    )
                }
            }
        }
    }
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.apply {
            // 设置弹窗背景为透明
            setBackgroundDrawableResource(android.R.color.transparent)
            // 设置弹窗动画
            attributes.windowAnimations = android.R.style.Animation_Dialog
            // 设置弹窗宽度为屏幕宽度
            setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)
        }
        return dialog
    }
    
    override fun onDismiss(dialog: android.content.DialogInterface) {
        super.onDismiss(dialog)
        onDismissListener?.invoke()
    }
}

@Composable
fun BottomSheetContent(onDismiss: () -> Unit) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
            .background(Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "BottomSheetDialogFragment 底部弹窗",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 16.dp)
            )
            
            Text(
                text = "这是使用 BottomSheetDialogFragment 实现的底部弹窗示例。关闭后将测量上一个页面响应用户操作的延迟时间。",
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onDismiss,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("关闭弹窗")
            }
            
            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}
