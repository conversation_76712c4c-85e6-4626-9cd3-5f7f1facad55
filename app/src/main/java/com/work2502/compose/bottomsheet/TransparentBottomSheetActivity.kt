package com.work2502.compose.bottomsheet

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.work2502.compose.ui.theme.Compose25003Theme
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 透明 Activity 实现的底部弹窗
 */
class TransparentBottomSheetActivity : ComponentActivity() {
    companion object {
        private const val TAG = "TransparentBottomSheet"

        // 记录弹窗关闭时间，用于测量响应延迟
        var dismissTimeMillis: Long = 0
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置透明主题
        setContent {
            Compose25003Theme {
                TransparentBottomSheet(
                    onDismiss = {
                        // 记录关闭时间
                        dismissTimeMillis = System.currentTimeMillis()
                        Log.d(TAG, "Bottom sheet dismissed at: $dismissTimeMillis")
                        finish()
                    }
                )
            }
        }
    }
}

@Composable
fun TransparentBottomSheet(onDismiss: () -> Unit) {
    // 控制底部弹窗的显示状态
    var isVisible by remember { mutableStateOf(false) }

    // 背景透明度动画
    val backgroundAlpha by animateFloatAsState(
        targetValue = if (isVisible) 0.5f else 0f,
        animationSpec = tween(
            durationMillis = 300,
            easing = FastOutSlowInEasing
        ),
        label = "backgroundAlpha"
    )

    // 底部弹窗位置动画 - 使用更明显的从底部弹出效果
    val sheetOffsetY by animateFloatAsState(
        targetValue = if (isVisible) 0f else 1f,
        animationSpec = tween(
            durationMillis = 400,
            delayMillis = 0,
            easing = FastOutSlowInEasing // 使用加速度曲线使动画更自然
        ),
        label = "sheetOffsetY"
    )

    // 显示弹窗
    LaunchedEffect(Unit) {
        delay(50) // 短暂延迟，确保动画流畅
        isVisible = true
    }

    // 监听返回键
    DisposableEffect(Unit) {
        onDispose {
            // 在组件销毁时执行清理操作
        }
    }

    // 整个屏幕的容器
    Box(
        modifier = Modifier
            .fillMaxSize()
            .alpha(backgroundAlpha)
            .background(Color.Black)
            .pointerInput(Unit) {
                detectTapGestures {
                    // 点击背景关闭弹窗
                    isVisible = false
                    // 短暂延迟后关闭，等待动画完成
                    kotlinx.coroutines.MainScope().launch {
                        delay(300) // 等待动画完成
                        onDismiss()
                    }
                }
            }
    ) {
        // 底部弹窗内容 - 改进底部弹出动画效果
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                .background(Color.White)
                // 使用偏移量创建从底部弹出的效果
                .padding(bottom = 0.dp)
                // 使用offset而不是padding来创建更好的动画效果
                .height(400.dp) // 设置固定高度，使动画效果更明显
                .offset(y = if (sheetOffsetY > 0) (sheetOffsetY * 400).dp else 0.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "透明 Activity 底部弹窗",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 16.dp)
                )

                Text(
                    text = "这是使用透明 Activity 实现的底部弹窗示例。关闭后将测量上一个页面响应用户操作的延迟时间。",
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))

                Button(
                    onClick = {
                        isVisible = false
                        // 短暂延迟后关闭，等待动画完成
                        kotlinx.coroutines.MainScope().launch {
                            delay(300) // 等待动画完成
                            onDismiss()
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("关闭弹窗")
                }

                Spacer(modifier = Modifier.height(32.dp))
            }
        }
    }
}
