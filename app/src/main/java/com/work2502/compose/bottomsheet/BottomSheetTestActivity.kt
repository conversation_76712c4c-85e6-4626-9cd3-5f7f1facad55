package com.work2502.compose.bottomsheet

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.FragmentActivity
import com.work2502.compose.ui.theme.Compose25003Theme
import kotlinx.coroutines.delay

/**
 * 底部弹窗测试页面
 */
class BottomSheetTestActivity : FragmentActivity() {
    
    companion object {
        private const val TAG = "BottomSheetTest"
    }
    
    // 记录按钮点击时间，用于测量响应延迟
    private var buttonClickTimeMillis: Long = 0
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            Compose25003Theme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    BottomSheetTestScreen(
                        onActivityBottomSheetClick = {
                            // 记录点击时间
                            buttonClickTimeMillis = System.currentTimeMillis()
                            Log.d(TAG, "Activity bottom sheet button clicked at: $buttonClickTimeMillis")
                            
                            // 启动透明 Activity 底部弹窗
                            val intent = Intent(this, TransparentBottomSheetActivity::class.java)
                            startActivity(intent)
                        },
                        onFragmentBottomSheetClick = {
                            // 记录点击时间
                            buttonClickTimeMillis = System.currentTimeMillis()
                            Log.d(TAG, "Fragment bottom sheet button clicked at: $buttonClickTimeMillis")
                            
                            // 显示 BottomSheetDialogFragment 底部弹窗
                            val bottomSheet = ComposeBottomSheetDialogFragment.newInstance()
                            bottomSheet.show(supportFragmentManager, "BottomSheetDialog")
                        },
                        getResponseDelay = { type ->
                            when (type) {
                                "activity" -> {
                                    val dismissTime = TransparentBottomSheetActivity.dismissTimeMillis
                                    if (dismissTime > 0) {
                                        val now = System.currentTimeMillis()
                                        val delay = now - dismissTime
                                        Log.d(TAG, "Activity bottom sheet response delay: $delay ms")
                                        delay
                                    } else {
                                        0
                                    }
                                }
                                "fragment" -> {
                                    val dismissTime = ComposeBottomSheetDialogFragment.dismissTimeMillis
                                    if (dismissTime > 0) {
                                        val now = System.currentTimeMillis()
                                        val delay = now - dismissTime
                                        Log.d(TAG, "Fragment bottom sheet response delay: $delay ms")
                                        delay
                                    } else {
                                        0
                                    }
                                }
                                else -> 0
                            }
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun BottomSheetTestScreen(
    onActivityBottomSheetClick: () -> Unit,
    onFragmentBottomSheetClick: () -> Unit,
    getResponseDelay: (String) -> Long
) {
    // 记录响应延迟
    var activityResponseDelay by remember { mutableLongStateOf(0L) }
    var fragmentResponseDelay by remember { mutableLongStateOf(0L) }
    
    // 定期检查响应延迟
    LaunchedEffect(Unit) {
        while (true) {
            val activityDelay = getResponseDelay("activity")
            if (activityDelay > 0) {
                activityResponseDelay = activityDelay
            }
            
            val fragmentDelay = getResponseDelay("fragment")
            if (fragmentDelay > 0) {
                fragmentResponseDelay = fragmentDelay
            }
            
            delay(100) // 每100毫秒检查一次
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "底部弹窗响应延迟测试",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 32.dp)
        )
        
        // 透明 Activity 底部弹窗
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "透明 Activity 底部弹窗",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Text(
                    text = "响应延迟: ${activityResponseDelay}ms",
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Button(
                    onClick = onActivityBottomSheetClick,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("显示 Activity 底部弹窗")
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // BottomSheetDialogFragment 底部弹窗
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "BottomSheetDialogFragment 底部弹窗",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Text(
                    text = "响应延迟: ${fragmentResponseDelay}ms",
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Button(
                    onClick = onFragmentBottomSheetClick,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("显示 Fragment 底部弹窗")
                }
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        Text(
            text = "说明: 关闭弹窗后，此页面会测量并显示响应延迟时间。\n" +
                   "点击按钮多次可以获得更准确的平均值。",
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(16.dp)
        )
    }
}
