package com.work2502.compose.coil

import android.Manifest
import android.content.ContentUris
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import coil3.ImageLoader
import coil3.compose.AsyncImage
import coil3.compose.rememberAsyncImagePainter
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.work2502.compose.ComposeApplication
import com.work2502.compose.ui.theme.Compose25003Theme
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.UUID
import kotlin.random.Random

class CoilImageListActivity : ComponentActivity() {
    companion object {
        private const val TAG = "CoilImageListActivity"

        // 显示模式
        const val DISPLAY_MODE_LAZY_COLUMN = 0
        const val DISPLAY_MODE_COLUMN = 1
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 从 Intent 中获取显示模式，默认为 LazyColumn
        val displayMode = intent.getIntExtra("display_mode", DISPLAY_MODE_LAZY_COLUMN)

        setContent {
            Compose25003Theme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    CoilImageListScreen(displayMode = displayMode)
                }
            }
        }
    }
}

// 列表项类型
sealed class ListItem(val id: String = UUID.randomUUID().toString()) {
    data class ImageItem(val uri: Uri, val isGif: Boolean = false) : ListItem()
    data class TextItem(val text: String) : ListItem()
    data class ColorItem(val color: Color) : ListItem()
}

@Composable
fun CoilImageListScreen(displayMode: Int = CoilImageListActivity.DISPLAY_MODE_LAZY_COLUMN) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // 使用 Application 中的 ImageLoader 单例
    val imageLoader = remember { ComposeApplication.getImageLoader(context) }

    // 列表状态
    val listState = rememberLazyListState()
    val rememberScrollState = rememberScrollState()
    // 列表项
    val items = remember { mutableStateListOf<ListItem>() }

    // 权限状态
    var hasStoragePermission by remember { mutableStateOf(false) }

    // 检查权限
    LaunchedEffect(Unit) {
        hasStoragePermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_MEDIA_IMAGES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }

        if (hasStoragePermission) {
            loadInitialItems(context, items)
        }
    }

    // 权限请求
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        hasStoragePermission = isGranted
        if (isGranted) {
            scope.launch {
                loadInitialItems(context, items)
            }
        }
    }

    // 请求权限（如果需要）
    LaunchedEffect(Unit) {
        if (!hasStoragePermission) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                permissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES)
            } else {
                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }
    }

    Scaffold(
        floatingActionButton = {
            Row {
                // 添加按钮
                FloatingActionButton(
                    onClick = {
                        scope.launch {
                            addRandomItem(context, items)
                        }
                    },
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Icon(Icons.Default.Add, contentDescription = "添加")
                }

                // 删除按钮
                FloatingActionButton(
                    onClick = {
                        if (items.isNotEmpty()) {
                            items.removeAt(0)
                        }
                    }
                ) {
                    Icon(Icons.Default.Delete, contentDescription = "删除")
                }
            }
        }
    ) { paddingValues ->
        if (!hasStoragePermission) {
            // 显示权限请求提示
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "需要存储权限来加载图片",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                permissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES)
                            } else {
                                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
                            }
                        }
                    ) {
                        Text("请求权限")
                    }
                }
            }
        } else {
            // 显示模式切换
            when (displayMode) {
                CoilImageListActivity.DISPLAY_MODE_LAZY_COLUMN -> {
                    // 使用 LazyColumn 显示列表
                    LazyColumnWithIV(listState, paddingValues, items, imageLoader)
                }
                CoilImageListActivity.DISPLAY_MODE_COLUMN -> {
                    // 使用普通 Column 显示列表
                    val scrollState = rememberScrollState()
                    ColumnWithIV(paddingValues, items.toList(), imageLoader, scrollState)
                }
            }
        }
    }
}

@Composable
private fun ColumnWithIV(
    paddingValues: PaddingValues,
    items: List<ListItem>,
    imageLoader: ImageLoader,
    scrollState: ScrollState
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues)
            .verticalScroll(scrollState)
    ) {
        items.forEach { item ->
            when (item) {
                is ListItem.ImageItem -> {
                    ImageCard(
                        item = item,
                        imageLoader = imageLoader,
                    )
                }

                is ListItem.TextItem -> {
                    TextCard(
                        item = item,
                    )
                }

                is ListItem.ColorItem -> {
                    ColorCard(
                        item = item,
                    )
                }
            }
        }
    }
}

@Composable
private fun LazyColumnWithIV(
    listState: LazyListState,
    paddingValues: PaddingValues,
    items: SnapshotStateList<ListItem>,
    imageLoader: ImageLoader
) {
    LazyColumn(
        state = listState,
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(
            items = items,
            key = { it.id } // 使用稳定的key避免重组时闪烁
        ) { item ->
            // 使用animateItemPlacement让添加/删除项目时有动画
            when (item) {
                is ListItem.ImageItem -> {
                    ImageCard(
                        item = item,
                        imageLoader = imageLoader,
                        modifier = Modifier.animateItem(
                                    fadeInSpec = null, fadeOutSpec = null, placementSpec = tween(durationMillis = 300)
                                )
                    )
                }

                is ListItem.TextItem -> {
                    TextCard(
                        item = item,
                        modifier = Modifier.animateItem(
                            fadeInSpec = null,
                            fadeOutSpec = null,
                            placementSpec = tween(durationMillis = 300)
                        )
                    )
                }

                is ListItem.ColorItem -> {
                    ColorCard(
                        item = item,
                        modifier = Modifier.animateItem(
                            fadeInSpec = null,
                            fadeOutSpec = null,
                            placementSpec = tween(durationMillis = 300)
                        )
                    )
                }
            }
        }
    }
}
@Composable
fun ImageCard2(
    item: ListItem.ImageItem,
    imageLoader: ImageLoader,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    // 使用 remember 记住图片请求，避免重组时重新创建
    val imageRequest = remember(item.uri) {
        ImageRequest.Builder(context)
            .data(item.uri)
            .crossfade(true)
            .memoryCachePolicy(CachePolicy.ENABLED)
            .diskCachePolicy(CachePolicy.ENABLED)
            // 添加唯一标识符，确保缓存命中
            .build()
    }

    // 使用 remember 记住图片状态，避免重组时重置
    val painter = rememberAsyncImagePainter(
        model = imageRequest,
        imageLoader = imageLoader
    )

    // 使用 key 确保组件在重组时保持状态
    key(item.id) {
        Card(
            modifier = modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                // 图片标题
                Text(
                    text = if (item.isGif) "GIF图片" else "静态图片",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // 图片内容
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(16f / 9f)
                        .clip(RoundedCornerShape(8.dp))
                        .background(Color.LightGray),
                    contentAlignment = Alignment.Center
                ) {
                    // 使用 AsyncImage 而不是 SubcomposeAsyncImage 可能会有更好的性能
                    Image(
                        painter = painter,
                        contentDescription = "图片",
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.fillMaxSize()
                    )

                }

                // 图片信息
                Text(
                    text = "ID: ${item.id.take(8)}...",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}
val TAG = "CoilImageListActivity"
@Composable
fun ImageCard(
    item: ListItem.ImageItem,
    imageLoader: ImageLoader,
    modifier: Modifier = Modifier
) {
    Log.d(TAG, "ImageCard: item.uri = ${item.uri} imageLoader = ${imageLoader.hashCode()}")

    // 使用 remember 记住图片请求，避免重组时重新创建
    val context = LocalContext.current
    val imageRequest = remember(item.id, item.uri) {
        ImageRequest.Builder(context)
            .data(item.uri)
            .crossfade(true)
            .memoryCachePolicy(CachePolicy.ENABLED)
            .diskCachePolicy(CachePolicy.ENABLED)
            .build()
    }

    // 使用 key 确保组件在重组时保持状态
    key(item.id) {
        Card(
            modifier = modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                // 图片标题
                Text(
                    text = if (item.isGif) "GIF图片" else "静态图片",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // 图片内容
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(16f / 9f)
                        .clip(RoundedCornerShape(8.dp))
                        .background(Color.LightGray),
                    contentAlignment = Alignment.Center
                ) {
                    // 使用 SubcomposeAsyncImage 以便自定义加载状态
                    AsyncImage(
                        model = imageRequest,
                        contentDescription = "图片",
                        imageLoader = imageLoader,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.fillMaxSize(),
//                        loading = {
//                            Box(
//                                modifier = Modifier.fillMaxSize(),
//                                contentAlignment = Alignment.Center
//                            ) {
//                                CircularProgressIndicator(
//                                    modifier = Modifier.size(40.dp)
//                                )
//                            }
//                        },
//                        error = {
//                            Box(
//                                modifier = Modifier.fillMaxSize(),
//                                contentAlignment = Alignment.Center
//                            ) {
//                                Text(
//                                    text = "加载失败",
//                                    color = Color.Red
//                                )
//                            }
//                        }
                    )
                }

                // 图片信息
                Text(
                    text = "ID: ${item.id.take(8)}...",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}

@Composable
fun TextCard(
    item: ListItem.TextItem,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "文本项",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Text(
                text = item.text,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(vertical = 16.dp)
            )

            Text(
                text = "ID: ${item.id.take(8)}...",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
    }
}

@Composable
fun ColorCard(
    item: ListItem.ColorItem,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "颜色项",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(100.dp)
                    .background(item.color, RoundedCornerShape(8.dp))
                    .border(1.dp, Color.LightGray, RoundedCornerShape(8.dp)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "颜色示例",
                    color = if (isColorDark(item.color)) Color.White else Color.Black,
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                )
            }

            Text(
                text = "ID: ${item.id.take(8)}...",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

// 判断颜色是否为深色
fun isColorDark(color: Color): Boolean {
    val darkness = 1 - (0.299 * color.red + 0.587 * color.green + 0.114 * color.blue)
    return darkness >= 0.5
}

// 加载初始项目
suspend fun loadInitialItems(context: android.content.Context, items: MutableList<ListItem>) {
    withContext(Dispatchers.IO) {
        try {
            // 添加一些文本和颜色项
            items.add(ListItem.TextItem("这是一个文本项，用于测试列表重组时的表现"))
            items.add(ListItem.ColorItem(Color(0xFF2196F3)))

            // 加载设备上的图片
            val imageUris = getImageUris(context, 3)
            imageUris.forEach { (uri, isGif) ->
                items.add(ListItem.ImageItem(uri, isGif))
            }

            // 再添加一些其他类型的项目
            items.add(ListItem.TextItem("另一个文本项，包含更多内容来测试不同高度的项目在滚动时的表现"))
            items.add(ListItem.ColorItem(Color(0xFFE91E63)))
        } catch (e: Exception) {
            Log.e("CoilImageList", "加载初始项目失败", e)
        }
    }
}

// 添加随机项目
suspend fun addRandomItem(context: android.content.Context, items: MutableList<ListItem>) {
    withContext(Dispatchers.IO) {
        try {
            val random = Random.nextInt(3)
            when (random) {
                0 -> {
                    // 添加随机图片
                    val imageUris = getImageUris(context, 1)
                    if (imageUris.isNotEmpty()) {
                        val (uri, isGif) = imageUris.first()
                        items.add(ListItem.ImageItem(uri, isGif))
                    } else {
                        // 如果没有图片，添加文本
                        items.add(ListItem.TextItem("无法加载更多图片，这是一个替代文本"))
                    }
                }

                1 -> {
                    // 添加随机文本
                    val texts = listOf(
                        "这是一个新添加的文本项",
                        "测试列表重组时的性能和视觉效果",
                        "Compose中的重组是否会导致图片闪烁",
                        "使用key和animateItemPlacement可以改善列表动画",
                        "Coil是Kotlin优先的图片加载库"
                    )
                    items.add(ListItem.TextItem(texts.random()))
                }

                else -> {
                    // 添加随机颜色
                    val colors = listOf(
                        Color(0xFF4CAF50),
                        Color(0xFFFFC107),
                        Color(0xFF9C27B0),
                        Color(0xFF009688),
                        Color(0xFFFF5722)
                    )
                    items.add(ListItem.ColorItem(colors.random()))
                }
            }
        } catch (e: Exception) {
            Log.e("CoilImageList", "添加随机项目失败", e)
        }
    }
}

// 获取设备上的图片URI
suspend fun getImageUris(context: android.content.Context, limit: Int): List<Pair<Uri, Boolean>> {
    return withContext(Dispatchers.IO) {
        val imageUris = mutableListOf<Pair<Uri, Boolean>>()
        try {
            val projection = arrayOf(
                MediaStore.Images.Media._ID,
                MediaStore.Images.Media.DISPLAY_NAME
            )

            val selection = "${MediaStore.Images.Media.SIZE} > ?"
            val selectionArgs = arrayOf("0") // 排除大小为0的文件

            val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC"

            context.contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                projection,
                selection,
                selectionArgs,
                sortOrder
            )?.use { cursor ->
                val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
                val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)

                var count = 0
                while (cursor.moveToNext() && count < limit) {
                    val id = cursor.getLong(idColumn)
                    val name = cursor.getString(nameColumn)
                    val contentUri = ContentUris.withAppendedId(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        id
                    )

                    // 检查是否为GIF
                    val isGif = name.endsWith(".gif", ignoreCase = true)

                    imageUris.add(contentUri to isGif)
                    count++
                }
            }
        } catch (e: Exception) {
            Log.e("CoilImageList", "获取图片URI失败", e)
        }

        imageUris
    }
}
