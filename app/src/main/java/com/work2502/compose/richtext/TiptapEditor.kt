package com.work2502.compose.richtext

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.graphics.Rect
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.speech.RecognizerIntent
import android.util.Log
import android.view.ViewTreeObserver
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

private const val TAG = "TiptapEditor"

/**
 * 处理选择的图片
 */
private fun handleSelectedImage(context: Context, uri: Uri, webView: WebView?) {
    try {
        // 获取图片路径
        val imagePath = uri.toString()
        Log.d(TAG, "选择的图片路径: $imagePath")

        // 将图片插入到编辑器
        webView?.post {
            val script = "insertImage('$imagePath');"
            webView.evaluateJavascript(script, null)
        }
    } catch (e: Exception) {
        Log.e(TAG, "处理图片失败", e)
        Toast.makeText(context, "无法加载选择的图片", Toast.LENGTH_SHORT).show()
    }
}

/**
 * 录音管理器
 */
class VoiceRecordManager(private val context: Context) {
    private var mediaRecorder: MediaRecorder? = null
    private var mediaPlayer: MediaPlayer? = null
    private var isRecording = false
    private var isPaused = false
    private var isPlaying = false
    private var outputFile: File? = null
    private var startTime: Long = 0
    private var pausedTime: Long = 0

    /**
     * 开始录音
     */
    fun startRecording(): Boolean {
        if (isRecording) return false

        try {
            // 创建输出文件
            val outputDir = context.cacheDir
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            outputFile = File(outputDir, "voice_${timestamp}.3gp")

            // 初始化 MediaRecorder
            mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                MediaRecorder(context)
            } else {
                @Suppress("DEPRECATION")
                MediaRecorder()
            }

            mediaRecorder?.apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
                setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)
                setOutputFile(outputFile?.absolutePath)
                prepare()
                start()
            }

            isRecording = true
            isPaused = false
            startTime = System.currentTimeMillis()
            pausedTime = 0
            return true
        } catch (e: IOException) {
            Log.e(TAG, "开始录音失败", e)
            return false
        }
    }

    /**
     * 暂停录音
     */
    fun pauseRecording(): Boolean {
        if (!isRecording || isPaused) return false

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                mediaRecorder?.pause()
                isPaused = true
                pausedTime += System.currentTimeMillis() - startTime
                return true
            }
            return false
        } catch (e: Exception) {
            Log.e(TAG, "暂停录音失败", e)
            return false
        }
    }

    /**
     * 继续录音
     */
    fun resumeRecording(): Boolean {
        if (!isRecording || !isPaused) return false

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                mediaRecorder?.resume()
                isPaused = false
                startTime = System.currentTimeMillis()
                return true
            }
            return false
        } catch (e: Exception) {
            Log.e(TAG, "继续录音失败", e)
            return false
        }
    }

    /**
     * 停止录音
     */
    fun stopRecording(): VoiceRecordResult? {
        if (!isRecording) return null

        try {
            // 计算总时长
            val totalDuration = if (isPaused) {
                pausedTime
            } else {
                pausedTime + (System.currentTimeMillis() - startTime)
            }

            mediaRecorder?.apply {
                stop()
                release()
            }
            mediaRecorder = null
            isRecording = false
            isPaused = false

            // 格式化时长
            val minutes = TimeUnit.MILLISECONDS.toMinutes(totalDuration)
            val seconds = TimeUnit.MILLISECONDS.toSeconds(totalDuration) % 60
            val formattedDuration = String.format("%02d:%02d", minutes, seconds)

            return outputFile?.let { file ->
                VoiceRecordResult(
                    file = file,
                    duration = formattedDuration,
                    durationMillis = totalDuration
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败", e)
            return null
        }
    }

    /**
     * 播放录音
     */
    fun playAudio(audioPath: String, onCompletion: () -> Unit = {}) {
        try {
            // 停止当前正在播放的录音
            stopPlayback()

            mediaPlayer = MediaPlayer().apply {
                setDataSource(audioPath)
                prepare()
                setOnCompletionListener {
                    <EMAIL> = false
                    onCompletion()
                }
                start()
            }
            isPlaying = true
        } catch (e: Exception) {
            Log.e(TAG, "播放录音失败", e)
            Toast.makeText(context, "播放录音失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 停止播放
     */
    fun stopPlayback() {
        try {
            if (isPlaying) {
                mediaPlayer?.stop()
            }
            mediaPlayer?.release()
            mediaPlayer = null
            isPlaying = false
        } catch (e: Exception) {
            Log.e(TAG, "停止播放失败", e)
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            mediaRecorder?.release()
            mediaRecorder = null
            stopPlayback()
            isRecording = false
            isPaused = false
        } catch (e: Exception) {
            Log.e(TAG, "释放录音资源失败", e)
        }
    }
}

/**
 * 录音结果数据类
 */
data class VoiceRecordResult(
    val file: File,
    val duration: String,
    val durationMillis: Long
)

/**
 * 开始录音
 */
private fun startRecording(context: Context, voiceRecordManager: VoiceRecordManager, webView: WebView?) {
    if (voiceRecordManager.startRecording()) {
        // 录音开始成功
        Toast.makeText(context, "录音已开始", Toast.LENGTH_SHORT).show()
    } else {
        // 录音开始失败
        Toast.makeText(context, "录音开始失败", Toast.LENGTH_SHORT).show()
        // 通知前端隐藏录音控制栏
        webView?.post {
            webView.evaluateJavascript("hideRecordingBar();", null)
        }
    }
}

/**
 * 将录音文件路径转换为可用的URI
 */
private fun getAudioFileUri(file: File): String {
    return file.absolutePath
}

/**
 * 为 WebView 设置手写笔委托
 */
@android.annotation.SuppressLint("NewApi")
private fun WebView.setHandwritingDelegatesForWebView() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        try {
            // 启用手写笔支持
            val handwritingClass = Class.forName("android.view.inputmethod.HandwritingInitiator")
            val getInstance = handwritingClass.getMethod("getInstance")
            val handwritingInstance = getInstance.invoke(null)

            val setHandwritingEnabled = handwritingClass.getMethod("setHandwritingEnabled", android.view.View::class.java, Boolean::class.java)
            setHandwritingEnabled.invoke(handwritingInstance, this, true)
        } catch (e: Exception) {
            Log.e(TAG, "设置手写笔委托失败", e)
        }
    }
}

/**
 * 显示背景选项对话框
 */
private fun showBackgroundOptionsDialog(
    context: Context,
    backgroundImagePickerLauncher: androidx.activity.result.ActivityResultLauncher<Intent>,
    webView: WebView?,
    backgroundManager: BackgroundManager
) {
    val options = arrayOf("选择图片", "选择颜色", "重置背景")

    android.app.AlertDialog.Builder(context)
        .setTitle("选择背景类型")
        .setItems(options) { dialog, which ->
            when (which) {
                0 -> { // 选择图片
                    try {
                        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                        backgroundImagePickerLauncher.launch(intent)
                    } catch (e: Exception) {
                        Log.e(TAG, "打开图片选择器失败", e)
                        Toast.makeText(context, "无法打开图片选择器", Toast.LENGTH_SHORT).show()
                    }
                }
                1 -> { // 选择颜色
                    showColorPickerDialog(context, webView, backgroundManager)
                }
                2 -> { // 重置背景
                    webView?.post {
                        webView.evaluateJavascript("setBackground('reset', '');", null)
                    }
                    backgroundManager.saveBackgroundSetting("reset", "")
                }
            }
        }
        .show()
}

/**
 * 显示颜色选择器对话框
 */
private fun showColorPickerDialog(
    context: Context,
    webView: WebView?,
    backgroundManager: BackgroundManager
) {
    val colors = arrayOf(
        Pair("白色", "#ffffff"),
        Pair("浅灰", "#f5f5f5"),
        Pair("浅绿", "#e8f5e9"),
        Pair("浅蓝", "#e3f2fd"),
        Pair("浅黄", "#fff8e1"),
        Pair("浅红", "#ffebee"),
        Pair("浅紫", "#f3e5f5"),
        Pair("浅粉", "#fce4ec"),
        Pair("浅青", "#e0f7fa")
    )

    val colorNames = colors.map { it.first }.toTypedArray()

    android.app.AlertDialog.Builder(context)
        .setTitle("选择背景颜色")
        .setItems(colorNames) { dialog, which ->
            val selectedColor = colors[which].second

            // 应用颜色到编辑器
            webView?.post {
                webView.evaluateJavascript("setBackground('color', '$selectedColor');", null)
            }

            // 保存颜色设置
            backgroundManager.saveBackgroundSetting("color", selectedColor)
        }
        .show()
}

/**
 * 处理选择的背景图片
 */
private fun handleSelectedBackgroundImage(context: Context, uri: Uri, webView: WebView?, backgroundManager: BackgroundManager) {
    try {
        // 获取图片路径
        val imagePath = uri.toString()
        Log.d(TAG, "选择的背景图片路径: $imagePath")

        // 将背景图片应用到编辑器
        webView?.post {
            val script = "setBackground('image', '$imagePath');"
            webView.evaluateJavascript(script, null)
        }

        // 保存背景设置
        backgroundManager.saveBackgroundSetting("image", imagePath)
    } catch (e: Exception) {
        Log.e(TAG, "处理背景图片失败", e)
        Toast.makeText(context, "无法加载选择的背景图片", Toast.LENGTH_SHORT).show()
    }
}

/**
 * 背景设置管理器
 */
class BackgroundManager(private val context: Context) {
    private val prefs: SharedPreferences = context.getSharedPreferences("tiptap_editor_prefs", Context.MODE_PRIVATE)

    /**
     * 保存背景设置
     */
    fun saveBackgroundSetting(type: String, value: String) {
        val settings = JSONObject().apply {
            put("type", type)
            put("value", value)
        }

        prefs.edit().putString(BACKGROUND_SETTINGS_KEY, settings.toString()).apply()
    }

    /**
     * 获取保存的背景设置
     */
    fun getSavedBackgroundSetting(): Pair<String, String>? {
        val settingsStr = prefs.getString(BACKGROUND_SETTINGS_KEY, null) ?: return null

        return try {
            val settings = JSONObject(settingsStr)
            val type = settings.getString("type")
            val value = settings.getString("value")

            // 如果是重置操作，返回null
            if (type == "reset") null else Pair(type, value)
        } catch (e: Exception) {
            Log.e(TAG, "解析背景设置失败", e)
            null
        }
    }

    companion object {
        private const val BACKGROUND_SETTINGS_KEY = "background_settings"
    }
}

/**
 * 软键盘状态监听器
 */
class KeyboardVisibilityDetector(private val webView: WebView) {
    private var isKeyboardVisible = false
    private val rootView = webView.rootView
    private val rect = Rect()
    private val visibilityThreshold = 200 // 像素阈值

    private val listener = ViewTreeObserver.OnGlobalLayoutListener {
        rootView.getWindowVisibleDisplayFrame(rect)
        val heightDiff = rootView.height - rect.height()

        val isVisible = heightDiff > visibilityThreshold
        if (isVisible != isKeyboardVisible) {
            isKeyboardVisible = isVisible
            notifyKeyboardVisibilityChanged(isVisible, heightDiff)
        }
    }

    fun start() {
        rootView.viewTreeObserver.addOnGlobalLayoutListener(listener)
    }

    fun stop() {
        rootView.viewTreeObserver.removeOnGlobalLayoutListener(listener)
    }

    private fun notifyKeyboardVisibilityChanged(isVisible: Boolean, keyboardHeight: Int) {
        val script = if (isVisible) {
            "adjustEditorForKeyboard($keyboardHeight);"
        } else {
            "resetEditorLayout();"
        }
        webView.evaluateJavascript(script, null)
    }
}

/**
 * Tiptap.js 富文本编辑器 Composable
 */
@SuppressLint("SetJavaScriptEnabled")
@Composable
fun TiptapEditor(
    onContentSaved: (String) -> Unit = {},
    onHtmlContentReceived: (String) -> Unit = {},
    onTextContentReceived: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val composeView = LocalView.current
    val coroutineScope = rememberCoroutineScope()

    // 保存WebView实例的引用
    val webViewRef = remember { mutableStateOf<WebView?>(null) }

    // 创建录音管理器
    val voiceRecordManager = remember { VoiceRecordManager(context) }

    // 创建背景设置管理器
    val backgroundManager = remember { BackgroundManager(context) }

    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                // 处理选择的图片
                handleSelectedImage(context, uri, webViewRef.value)
            }
        }
    }

    // 背景图片选择器
    val backgroundImagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                // 处理选择的背景图片
                handleSelectedBackgroundImage(context, uri, webViewRef.value, backgroundManager)
            }
        }
    }

    // 保存最后录制的音频路径和时长
    val lastRecordedAudioPath = remember { mutableStateOf<String?>(null) }
    val lastRecordedAudioDuration = remember { mutableStateOf<String?>(null) }

    // 语音识别器
    val speechRecognizerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val spokenText = result.data?.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS)?.get(0) ?: ""
            val audioPath = lastRecordedAudioPath.value
            val duration = lastRecordedAudioDuration.value ?: "00:00"

            // 将语音识别结果和录音文件路径插入到编辑器
            if (audioPath != null) {
                webViewRef.value?.post {
                    val script = "insertVoiceText('$spokenText', '$audioPath', '$duration');"
                    webViewRef.value?.evaluateJavascript(script, null)
                }
            } else if (spokenText.isNotEmpty()) {
                // 如果没有录音文件，只插入文字
                webViewRef.value?.post {
                    val script = "insertVoiceText('$spokenText');"
                    webViewRef.value?.evaluateJavascript(script, null)
                }
            }

            // 清除临时变量
            lastRecordedAudioPath.value = null
            lastRecordedAudioDuration.value = null
        }
    }

    // 清理软键盘监听器
    DisposableEffect(Unit) {
        onDispose {
            webViewRef.value = null
            voiceRecordManager.release()
        }
    }

    // 请求录音权限
    val requestPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // 权限已授予，开始录音
            startRecording(context, voiceRecordManager, webViewRef.value)
        } else {
            // 权限被拒绝
            Toast.makeText(context, "需要录音权限才能使用录音功能", Toast.LENGTH_SHORT).show()
        }
    }

    AndroidView(
        modifier = Modifier.fillMaxSize(),
        factory = { ctx ->
            WebView(ctx).apply {
                // 设置布局参数
                layoutParams = android.view.ViewGroup.LayoutParams(
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT,
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT
                )

                // 配置WebView设置
                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    allowFileAccess = true
                    setSupportZoom(false) // 禁用缩放以避免布局问题
                    builtInZoomControls = false
                    displayZoomControls = false
                    useWideViewPort = true
                    loadWithOverviewMode = true
                    textZoom = 100 // 确保文本大小正常
                    setGeolocationEnabled(false) // 禁用地理位置
                    javaScriptCanOpenWindowsAutomatically = false // 禁止自动弹窗
                    useWideViewPort = true // 使用宽视口
                    layoutAlgorithm = android.webkit.WebSettings.LayoutAlgorithm.NORMAL // 使用正常布局算法
                }

                // 支持手写笔输入
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    importantForAutofill = android.view.View.IMPORTANT_FOR_AUTOFILL_YES
                }

                // 启用高级手写笔支持
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    setHandwritingDelegatesForWebView()
                }

                // 设置全屏幕显示
                systemUiVisibility = android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                        android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN

                // 设置背景透明
                setBackgroundColor(android.graphics.Color.TRANSPARENT)

                // 设置WebViewClient以处理页面加载
                webViewClient = object : android.webkit.WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        // 页面加载完成后执行的操作
                        view?.postDelayed({
                            // 执行 JavaScript 来调整高度
                            view.evaluateJavascript(
                                "(function() { " +
                                "  const event = new Event('resize');" +
                                "  window.dispatchEvent(event);" +
                                "  return true;" +
                                "})()",
                                null
                            )

                        }, 300) // 等待一些时间确保页面已经渲染
                    }
                }

                // 添加JavaScript接口
                addJavascriptInterface(object {
                    @JavascriptInterface
                    fun saveContent(content: String) {
                        Log.d(TAG, "保存内容: $content")
                        onContentSaved(content)
                        Toast.makeText(context, "内容已保存", Toast.LENGTH_SHORT).show()
                    }

                    @JavascriptInterface
                    fun getHtmlContent(html: String) {
                        Log.d(TAG, "HTML内容: $html")
                        onHtmlContentReceived(html)
                        Toast.makeText(context, "已获取HTML内容", Toast.LENGTH_SHORT).show()
                    }

                    @JavascriptInterface
                    fun getTextContent(text: String) {
                        Log.d(TAG, "纯文本内容: $text")
                        onTextContentReceived(text)
                        Toast.makeText(context, "已获取纯文本内容", Toast.LENGTH_SHORT).show()
                    }

                    @JavascriptInterface
                    fun selectImage() {
                        // 在UI线程上打开图片选择器
                        coroutineScope.launch {
                            try {
                                val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                                imagePickerLauncher.launch(intent)
                            } catch (e: Exception) {
                                Log.e(TAG, "打开图片选择器失败", e)
                                Toast.makeText(context, "无法打开图片选择器", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }

                    @JavascriptInterface
                    fun startRecording() {
                        // 检查录音权限
                        if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)
                            == PackageManager.PERMISSION_GRANTED) {
                            // 已有权限，开始录音
                            startRecording(context, voiceRecordManager, webViewRef.value)
                        } else {
                            // 请求权限
                            if (context is Activity) {
                                requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
                            } else {
                                Toast.makeText(context, "无法请求录音权限", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }

                    @JavascriptInterface
                    fun pauseRecording() {
                        // 暂停录音
                        if (!voiceRecordManager.pauseRecording()) {
                            Toast.makeText(context, "暂停录音失败", Toast.LENGTH_SHORT).show()
                        }
                    }

                    @JavascriptInterface
                    fun resumeRecording() {
                        // 继续录音
                        if (!voiceRecordManager.resumeRecording()) {
                            Toast.makeText(context, "继续录音失败", Toast.LENGTH_SHORT).show()
                        }
                    }

                    @JavascriptInterface
                    fun stopRecording() {
                        // 停止录音
                        voiceRecordManager.stopRecording()
                    }

                    @JavascriptInterface
                    fun convertVoiceToText() {
                        // 停止录音并转换为文字
                        val recordResult = voiceRecordManager.stopRecording()

                        if (recordResult != null) {
                            // 保存录音文件路径
                            val audioPath = getAudioFileUri(recordResult.file)

                            // 启动语音识别
                            try {
                                val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                                    putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                                    putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
                                    putExtra(RecognizerIntent.EXTRA_PROMPT, "请说话")
                                }

                                // 在启动语音识别前先保存录音路径
                                speechRecognizerLauncher.launch(intent)

                                // 在语音识别结果返回时，会将这个路径与识别结果一起传递给编辑器
                                // 将录音路径保存到临时变量中
                                lastRecordedAudioPath.value = audioPath
                                lastRecordedAudioDuration.value = recordResult.duration
                            } catch (e: Exception) {
                                Log.e(TAG, "启动语音识别失败", e)
                                Toast.makeText(context, "无法启动语音识别", Toast.LENGTH_SHORT).show()

                                // 如果语音识别失败，仍然插入录音块，但没有文字
                                webViewRef.value?.post {
                                    val script = "insertVoiceText('', '$audioPath', '${recordResult.duration}');"
                                    webViewRef.value?.evaluateJavascript(script, null)
                                }
                            }
                        } else {
                            Toast.makeText(context, "录音文件保存失败", Toast.LENGTH_SHORT).show()
                        }
                    }

                    @JavascriptInterface
                    fun playAudio(audioPath: String) {
                        // 播放录音
                        try {
                            voiceRecordManager.playAudio(audioPath) {
                                // 播放完成后更新按钮状态
                                webViewRef.value?.post {
                                    val script = "updatePlayButtonState('$audioPath', false);"
                                    webViewRef.value?.evaluateJavascript(script, null)
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "播放录音失败", e)
                            Toast.makeText(context, "播放录音失败", Toast.LENGTH_SHORT).show()
                        }
                    }

                    @JavascriptInterface
                    fun selectBackground() {
                        // 在UI线程上打开背景选择器
                        coroutineScope.launch {
                            try {
                                // 显示选择对话框，让用户选择图片或颜色
                                showBackgroundOptionsDialog(context, backgroundImagePickerLauncher, webViewRef.value, backgroundManager)
                            } catch (e: Exception) {
                                Log.e(TAG, "打开背景选择器失败", e)
                                Toast.makeText(context, "无法打开背景选择器", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }

                    @JavascriptInterface
                    fun saveBackgroundSetting(type: String, value: String) {
                        // 保存背景设置
                        backgroundManager.saveBackgroundSetting(type, value)
                    }

                    @JavascriptInterface
                    fun announceForAccessibility(message: String) {
                        // 使用无障碍服务播报消息
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                            val manager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as? android.view.accessibility.AccessibilityManager
                            if (manager != null && manager.isEnabled) {
                                val event = android.view.accessibility.AccessibilityEvent.obtain(android.view.accessibility.AccessibilityEvent.TYPE_ANNOUNCEMENT)
                                event.text.add(message)
                                event.className = context.javaClass.name
                                event.packageName = context.packageName
                                manager.sendAccessibilityEvent(event)
                            }
                        }
                    }

                    @JavascriptInterface
                    fun goBack() {
                        // 如果当前上下文是Activity，则调用finish()
                        if (context is Activity) {
                            (context as Activity).finish()
                        }
                    }
                }, "Android")


                // 保存WebView实例
                webViewRef.value = this

                // 设置WebViewClient以处理页面加载完成事件
                webViewClient = object : android.webkit.WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)

                        // 应用保存的背景设置
                        val savedBackground = backgroundManager.getSavedBackgroundSetting()
                        if (savedBackground != null) {
                            val (type, value) = savedBackground
                            view?.post {
                                view.evaluateJavascript("setBackground('$type', '$value');", null)
                            }
                        }
                    }
                }

                // 从assets目录加载HTML文件
                loadUrl("file:///android_asset/index.html")
            }
        },
    )
}
