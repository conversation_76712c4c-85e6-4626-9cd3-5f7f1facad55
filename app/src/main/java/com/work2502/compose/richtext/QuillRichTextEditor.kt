package com.work2502.compose.richtext

import android.annotation.SuppressLint
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.widget.Toast
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView

private const val TAG = "QuillRichTextEditor"

/**
 * Quill.js 富文本编辑器 Composable
 */
@SuppressLint("SetJavaScriptEnabled")
@Composable
fun QuillRichTextEditor(
    onContentSaved: (String) -> Unit = {},
    onHtmlContentReceived: (String) -> Unit = {},
    onTextContentReceived: (String) -> Unit = {}
) {
    val context = LocalContext.current
    
    AndroidView(
        factory = { ctx ->
            WebView(ctx).apply {
                // 设置布局参数
                layoutParams = android.view.ViewGroup.LayoutParams(
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT,
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT
                )
                
                // 配置WebView设置
                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    allowFileAccess = true
                    setSupportZoom(false) // 禁用缩放以避免布局问题
                    builtInZoomControls = false
                    displayZoomControls = false
                    useWideViewPort = true
                    loadWithOverviewMode = true
                    textZoom = 100 // 确保文本大小正常
                    setGeolocationEnabled(false) // 禁用地理位置
                    javaScriptCanOpenWindowsAutomatically = false // 禁止自动弹窗
                    useWideViewPort = true // 使用宽视口
                    layoutAlgorithm = android.webkit.WebSettings.LayoutAlgorithm.NORMAL // 使用正常布局算法
                }
                
                // 设置WebViewClient以处理页面加载
                webViewClient = object : android.webkit.WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        // 页面加载完成后执行的操作
                        view?.postDelayed({
                            // 执行 JavaScript 来调整高度
                            view.evaluateJavascript(
                                "(function() { " +
                                "  const event = new Event('resize');" +
                                "  window.dispatchEvent(event);" +
                                "  return true;" +
                                "})()",
                                null
                            )
                        }, 300) // 等待一些时间确保页面已经渲染
                    }
                }
                
                // 添加JavaScript接口
                addJavascriptInterface(object {
                    @JavascriptInterface
                    fun saveContent(content: String) {
                        Log.d(TAG, "保存内容: $content")
                        onContentSaved(content)
                        Toast.makeText(context, "内容已保存", Toast.LENGTH_SHORT).show()
                    }
                    
                    @JavascriptInterface
                    fun getHtmlContent(html: String) {
                        Log.d(TAG, "HTML内容: $html")
                        onHtmlContentReceived(html)
                        Toast.makeText(context, "已获取HTML内容", Toast.LENGTH_SHORT).show()
                    }
                    
                    @JavascriptInterface
                    fun getTextContent(text: String) {
                        Log.d(TAG, "纯文本内容: $text")
                        onTextContentReceived(text)
                        Toast.makeText(context, "已获取纯文本内容", Toast.LENGTH_SHORT).show()
                    }
                }, "Android")
                
                // 从assets目录加载HTML文件
                loadUrl("file:///android_asset/quill_editor.html")
            }
        },
        modifier = Modifier.fillMaxSize()
    )
}
