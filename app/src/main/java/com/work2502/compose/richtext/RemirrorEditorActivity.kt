package com.work2502.compose.richtext

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.work2502.compose.ui.theme.Compose25003Theme

private const val TAG = "RemirrorEditorActivity"

/**
 * Remirror.js 富文本编辑器 Activity
 */
class RemirrorEditorActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 配置窗口，使其能够适应软键盘
//        WindowCompat.setDecorFitsSystemWindows(window, false)
//        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        
        setContent {
            Compose25003Theme {
                Scaffold(
                    modifier = Modifier
                        .fillMaxSize()
                ) { innerPadding ->
                    Surface(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        RemirrorEditor(
                            onContentSaved = { content ->
                                Log.d(TAG, "保存的内容: $content")
                                // 这里可以将内容保存到数据库或文件
                            },
                            onHtmlContentReceived = { html ->
                                Log.d(TAG, "HTML内容: $html")
                                // 处理HTML内容
                            },
                            onTextContentReceived = { text ->
                                Log.d(TAG, "纯文本内容: $text")
                                // 处理纯文本内容
                            }
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun RemirrorEditorPreview() {
    Compose25003Theme {
        Scaffold { innerPadding ->
            Surface(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding),
                color = MaterialTheme.colorScheme.background
            ) {
                RemirrorEditor()
            }
        }
    }
}
