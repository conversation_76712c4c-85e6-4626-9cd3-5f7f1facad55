package com.work2502.compose.richtext

import android.annotation.SuppressLint
import android.os.Handler
import android.view.ViewGroup
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun WebViewMultiSelectableTextFields() {
    AndroidView(
        factory = { context ->
            WebView(context).apply {
                // 设置布局参数
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )

                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    allowFileAccess = true
                    setSupportZoom(false) // 禁用缩放以避免布局问题
                    builtInZoomControls = false
                    displayZoomControls = false
                    useWideViewPort = true
                    loadWithOverviewMode = true
                    textZoom = 100 // 确保文本大小正常
                    setGeolocationEnabled(false) // 禁用地理位置
                    javaScriptCanOpenWindowsAutomatically = false // 禁止自动弹窗
                    useWideViewPort = true // 使用宽视口
                    layoutAlgorithm = WebSettings.LayoutAlgorithm.NORMAL // 使用正常布局算法
                }

                // 设置WebViewClient以处理页面加载
                webViewClient = object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        // 页面加载完成后执行的操作
                        view?.postDelayed({
                            // 执行 JavaScript 来调整高度
                            view.evaluateJavascript(
                                "(function() { " +
                                "  const event = new Event('resize');" +
                                "  window.dispatchEvent(event);" +
                                "  return true;" +
                                "})()",
                                null
                            )
                        }, 300) // 等待一些时间确保页面已经渲染
                    }
                }

                // 从assets目录加载HTML文件
                loadUrl("file:///android_asset/todo_note.html")

                // 添加JavaScript接口来获取编辑后的内容
                addJavascriptInterface(object {
                    @JavascriptInterface
                    fun updateContent(content: String) {
                        // 更新内容
                    }

                    @JavascriptInterface
                    fun notifySelectionComplete(message: String) {
                        // 显示复制成功的提示消息
                        Handler(context.mainLooper).post {
                            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                        }
                    }
                }, "Android")
            }
        },
        modifier = Modifier.fillMaxSize()
    )
}