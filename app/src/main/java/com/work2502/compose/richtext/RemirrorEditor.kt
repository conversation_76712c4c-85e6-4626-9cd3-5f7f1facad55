package com.work2502.compose.richtext

import android.annotation.SuppressLint
import android.graphics.Rect
import android.util.Log
import android.view.ViewTreeObserver
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.webkit.WebView
import android.widget.Toast
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.viewinterop.AndroidView

private const val TAG = "RemirrorEditor"

/**
 * 软键盘状态监听器
 */
class KeyboardVisibilityDetector2(private val webView: WebView) {
    private var isKeyboardVisible = false
    private val rootView = webView.rootView
    private val rect = Rect()
    private val visibilityThreshold = 200 // 像素阈值

    private val listener = ViewTreeObserver.OnGlobalLayoutListener {
        rootView.getWindowVisibleDisplayFrame(rect)
        val heightDiff = rootView.height - rect.height()

        val isVisible = heightDiff > visibilityThreshold
        if (isVisible != isKeyboardVisible) {
            isKeyboardVisible = isVisible
            notifyKeyboardVisibilityChanged(isVisible, heightDiff)
        }
    }

    fun start() {
        rootView.viewTreeObserver.addOnGlobalLayoutListener(listener)
    }

    fun stop() {
        rootView.viewTreeObserver.removeOnGlobalLayoutListener(listener)
    }

    private fun notifyKeyboardVisibilityChanged(isVisible: Boolean, keyboardHeight: Int) {
        val script = if (isVisible) {
            "adjustEditorForKeyboard($keyboardHeight);"
        } else {
            "adjustEditorForKeyboard(0);"
        }
        webView.evaluateJavascript(script, null)
    }
}



/**
 * Remirror.js 富文本编辑器 Composable
 */
@SuppressLint("SetJavaScriptEnabled")
@Composable
fun RemirrorEditor(
    onContentSaved: (String) -> Unit = {},
    onHtmlContentReceived: (String) -> Unit = {},
    onTextContentReceived: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val composeView = LocalView.current

    // 记录WebView实例以便于稍后使用
    val webViewRef = remember { mutableListOf<WebView?>() }

    // 创建软键盘监听器
    val keyboardDetectorRef = remember { mutableListOf<KeyboardVisibilityDetector2?>() }

    // 清理软键盘监听器
    DisposableEffect(Unit) {
        onDispose {
            keyboardDetectorRef.firstOrNull()?.stop()
            keyboardDetectorRef.clear()
            webViewRef.clear()
        }
    }

    AndroidView(
        factory = { ctx ->
            WebView(ctx).apply {
                // 设置布局参数
                layoutParams = android.view.ViewGroup.LayoutParams(
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT,
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT
                )

                // 配置WebView设置
                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    allowFileAccess = true
                    setSupportZoom(false) // 禁用缩放以避免布局问题
                    builtInZoomControls = false
                    displayZoomControls = false
                    useWideViewPort = true
                    loadWithOverviewMode = true
                    textZoom = 100 // 确保文本大小正常
                    setGeolocationEnabled(false) // 禁用地理位置
                    javaScriptCanOpenWindowsAutomatically = false // 禁止自动弹窗
                    useWideViewPort = true // 使用宽视口
                    layoutAlgorithm = WebSettings.LayoutAlgorithm.NORMAL
                }

                // 设置WebViewClient以处理页面加载
                webViewClient = object : android.webkit.WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        // 页面加载完成后执行的操作
                        view?.postDelayed({
                            // 执行 JavaScript 来调整高度
                            view.evaluateJavascript(
                                "(function() { " +
                                "  const event = new Event('resize');" +
                                "  window.dispatchEvent(event);" +
                                "  return true;" +
                                "})()",
                                null
                            )

                            // 创建并启动软键盘监听器
                            val keyboardDetector = KeyboardVisibilityDetector2(view)
                            keyboardDetector.start()
                            keyboardDetectorRef.add(keyboardDetector)
                        }, 300) // 等待一些时间确保页面已经渲染
                    }
                }

                // 添加JavaScript接口
                addJavascriptInterface(object {
                    @JavascriptInterface
                    fun saveContent(content: String) {
                        Log.d(TAG, "保存内容: $content")
                        onContentSaved(content)
                        Toast.makeText(context, "内容已保存", Toast.LENGTH_SHORT).show()
                    }

                    @JavascriptInterface
                    fun getHtmlContent(html: String) {
                        Log.d(TAG, "HTML内容: $html")
                        onHtmlContentReceived(html)
                        Toast.makeText(context, "已获取HTML内容", Toast.LENGTH_SHORT).show()
                    }

                    @JavascriptInterface
                    fun getTextContent(text: String) {
                        Log.d(TAG, "纯文本内容: $text")
                        onTextContentReceived(text)
                        Toast.makeText(context, "已获取纯文本内容", Toast.LENGTH_SHORT).show()
                    }
                }, "Android")

                // 保存WebView实例以便于稍后使用
                webViewRef.add(this)

                // 从assets目录加载HTML文件
                loadUrl("file:///android_asset/remirror_editor_simple.html")
            }
        },
        modifier = Modifier.fillMaxSize().imePadding() // 添加软键盘适配
    )
}
