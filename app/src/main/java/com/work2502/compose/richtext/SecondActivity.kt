package com.work2502.compose.richtext

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.work2502.compose.ui.theme.Compose25003Theme

class SecondActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            Compose25003Theme {
                WebViewMultiSelectableTextFields()
            }
        }
    }
}



@Preview(showBackground = true)
@Composable
fun SecondScreenPreview() {
    Compose25003Theme {
        Scaffold { innerPadding ->
            Box(modifier = Modifier.fillMaxSize().padding(innerPadding)) {
                WebViewMultiSelectableTextFields()
            }
        }
    }
}
