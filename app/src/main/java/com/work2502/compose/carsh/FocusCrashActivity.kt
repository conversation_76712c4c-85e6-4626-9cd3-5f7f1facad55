package com.work2502.compose.carsh

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import android.content.Intent
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.work2502.compose.ui.theme.Compose25003Theme

/**
 * 专门用于复现 FocusTargetNode.requireActiveChild() 崩溃的 Activity
 * 崩溃信息: "ActiveParent with no focused child"
 */
class FocusCrashActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Compose25003Theme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    Surface(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        FocusCrashDemo()
                    }
                }
            }
        }
    }
}

@Composable
fun FocusCrashDemo() {
    // 状态
    var showFirstTextField by remember { mutableStateOf(true) }
    var showSecondTextField by remember { mutableStateOf(true) }
    var text1 by remember { mutableStateOf("") }
    var text2 by remember { mutableStateOf("") }
    var crashCount by remember { mutableStateOf(0) }

    // 焦点管理
    val focusManager = LocalFocusManager.current
    val focusRequester1 = remember { FocusRequester() }
    val focusRequester2 = remember { FocusRequester() }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = "焦点崩溃复现演示",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 崩溃计数
        Text(
            text = "崩溃尝试次数: $crashCount",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 添加一个按钮来查看解决方案
        val context = LocalContext.current
        Button(
            onClick = {
                context.startActivity(Intent(context, FocusCrashSolutionActivity::class.java))
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("查看解决方案")
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 场景1: 快速移除有焦点的组件
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "场景1: 移除有焦点的输入框",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // 第一个输入框 (可以被移除)
                AnimatedVisibility(
                    visible = showFirstTextField,
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    OutlinedTextField(
                        value = text1,
                        onValueChange = { text1 = it },
                        label = { Text("输入框 1") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester1)
                            .padding(vertical = 8.dp),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Next
                        ),
                        keyboardActions = KeyboardActions(
                            onNext = { focusManager.moveFocus(FocusDirection.Down) }
                        )
                    )
                }

                // 触发崩溃的按钮
                Button(
                    onClick = {
                        // 尝试触发崩溃: 先请求焦点，然后立即移除输入框
                        try {
                            crashCount++
                            focusRequester1.requestFocus()
                            // 立即移除有焦点的组件 (这会导致崩溃)
                            showFirstTextField = false
                        } catch (e: Exception) {
                            Log.e("FocusCrash", "场景1崩溃: ${e.message}", e)
                        }
                    },
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("触发场景1崩溃")
                }

                Button(
                    onClick = { showFirstTextField = true },
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("恢复输入框")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 场景2: 快速连续清除焦点
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "场景2: 快速连续清除焦点",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // 第二个输入框
                AnimatedVisibility(
                    visible = showSecondTextField,
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    OutlinedTextField(
                        value = text2,
                        onValueChange = { text2 = it },
                        label = { Text("输入框 2") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester2)
                            .padding(vertical = 8.dp)
                    )
                }

                // 触发崩溃的按钮
                Button(
                    onClick = {
                        // 尝试触发崩溃: 快速连续清除焦点
                        try {
                            crashCount++
                            focusRequester2.requestFocus()
                            // 快速连续清除焦点 (可能导致崩溃)
                            focusManager.clearFocus()
                            focusManager.clearFocus()
                            // 再次尝试操作焦点
                            Handler(Looper.getMainLooper()).postDelayed({
                                try {
                                    focusManager.clearFocus()
                                } catch (e: Exception) {
                                    Log.e("FocusCrash", "延迟清除焦点崩溃: ${e.message}", e)
                                }
                            }, 10)
                        } catch (e: Exception) {
                            Log.e("FocusCrash", "场景2崩溃: ${e.message}", e)
                        }
                    },
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("触发场景2崩溃")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 场景3: 焦点转移后立即移除目标
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "场景3: 焦点转移后立即移除目标",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 源输入框
                    TextField(
                        value = "",
                        onValueChange = {},
                        label = { Text("源输入框") },
                        modifier = Modifier
                            .weight(1f)
                            .padding(end = 8.dp)
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    // 目标输入框 (可以被移除)
                    if (showSecondTextField) {
                        TextField(
                            value = "",
                            onValueChange = {},
                            label = { Text("目标输入框") },
                            modifier = Modifier.weight(1f)
                        )
                    }
                }

                // 触发崩溃的按钮
                Button(
                    onClick = {
                        // 尝试触发崩溃: 焦点转移后立即移除目标
                        try {
                            crashCount++
                            // 移动焦点到右侧
                            focusManager.moveFocus(FocusDirection.Right)
                            // 立即移除目标输入框
                            showSecondTextField = false
                        } catch (e: Exception) {
                            Log.e("FocusCrash", "场景3崩溃: ${e.message}", e)
                        }
                    },
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("触发场景3崩溃")
                }

                Button(
                    onClick = { showSecondTextField = true },
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("恢复目标输入框")
                }
            }
        }
    }
}
