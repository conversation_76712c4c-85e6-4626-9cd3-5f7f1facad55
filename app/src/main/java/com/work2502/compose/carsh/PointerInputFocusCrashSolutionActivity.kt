package com.work2502.compose.carsh

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.PointerType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalViewConfiguration
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.work2502.compose.ui.theme.Compose25003Theme
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * 解决 pointerInput 与焦点管理冲突导致的崩溃
 */
class PointerInputFocusCrashSolutionActivity : ComponentActivity() {
    companion object {
        private const val TAG = "PointerInputFocusSolution"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Compose25003Theme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    Surface(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        PointerInputFocusCrashSolution()
                    }
                }
            }
        }
    }
}

val TAG = "PointerInputFocusSolution"

@Composable
fun PointerInputFocusCrashSolution() {
    // 状态
    var editMode by remember { mutableStateOf(false) }
    var text by remember { mutableStateOf(TextFieldValue("点击此区域进入编辑模式")) }
    var operationCount by remember { mutableStateOf(0) }
    var bottomMenuType by remember { mutableStateOf("") }
    
    // 使用安全的焦点管理器
    SafeFocusManager { focusManager ->
        val focusRequester = remember { FocusRequester() }
        val scope = rememberCoroutineScope()
        var awaitTapEventJob by remember { mutableStateOf<Job?>(null) }
        val viewConfiguration = LocalViewConfiguration.current
        
        // 互斥锁，用于协调焦点操作
        val focusLock = remember { Mutex() }
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 标题
            Text(
                text = "PointerInput 与焦点冲突解决方案",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 状态显示
            Text(
                text = "编辑模式: ${if (editMode) "开启" else "关闭"}",
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Text(
                text = "安全操作次数: $operationCount",
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Button(
                    onClick = {
                        // 安全地切换编辑模式
                        scope.launch {
                            focusLock.withLock {
                                operationCount++
                                editMode = !editMode
                                if (!editMode) {
                                    // 退出编辑模式时安全地清除焦点
                                    try {
                                        focusManager.clearFocus()
                                    } catch (e: Exception) {
                                        Log.e(TAG, "清除焦点失败: ${e.message}")
                                    }
                                } else {
                                    // 进入编辑模式时安全地请求焦点
                                    try {
                                        // 延迟请求焦点，确保UI已更新
                                        delay(50)
                                        focusRequester.requestFocus()
                                    } catch (e: Exception) {
                                        Log.e(TAG, "请求焦点失败: ${e.message}")
                                    }
                                }
                            }
                        }
                    }
                ) {
                    Text(if (editMode) "退出编辑" else "进入编辑")
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Button(
                    onClick = {
                        // 安全地模拟快速操作
                        scope.launch {
                            focusLock.withLock {
                                operationCount++
                                try {
                                    // 进入编辑模式
                                    editMode = true
                                    // 延迟请求焦点
                                    delay(50)
                                    focusRequester.requestFocus()
                                    // 延迟后退出编辑模式
                                    delay(100)
                                    editMode = false
                                    // 安全地清除焦点
                                    focusManager.clearFocus()
                                } catch (e: Exception) {
                                    Log.e(TAG, "安全操作失败: ${e.message}")
                                }
                            }
                        }
                    }
                ) {
                    Text("安全快速操作")
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 编辑区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp)
                    .background(Color.LightGray.copy(alpha = 0.3f))
                    .padding(16.dp)
                    // 改进的 pointerInput 实现
                    .pointerInput(editMode) {
                        // 安全地取消之前的作业
                        awaitTapEventJob?.cancel()
                        if (editMode) return@pointerInput
                        
                        awaitTapEventJob = scope.launch {
                            try {
                                awaitEachGesture {
                                    try {
                                        val down = awaitFirstDown()
                                        Log.v(TAG, "awaitEachGesture, down: $down")
                                        val downTime = System.currentTimeMillis()
                                        var tapTimeout = viewConfiguration.longPressTimeoutMillis
                                        var touchSlop = viewConfiguration.touchSlop
                                        
                                        if (down.type == PointerType.Stylus) {
                                            tapTimeout *= 3
                                            touchSlop *= 3
                                        }
                                        
                                        val tapPosition = down.position
                                        do {
                                            val event = awaitPointerEvent()
                                            val currentTime = System.currentTimeMillis()
                                            if (event.changes.size != 1) break
                                            if (currentTime - downTime > tapTimeout) break
                                            
                                            val change = event.changes[0]
                                            val distance = (change.position - tapPosition).getDistance()
                                            if (distance > touchSlop) break
                                            
                                            if (change.id == down.id && !change.pressed) {
                                                val tapType = change.type
                                                
                                                // 使用互斥锁安全地更新状态
//                                                focusLock.withLock {
//                                                    when (tapType) {
//                                                        PointerType.Stylus -> {
//                                                            bottomMenuType = "BRUSH"
//                                                            editMode = true
//                                                        }
//                                                        else -> {
//                                                            if (bottomMenuType == "BRUSH") {
//                                                                bottomMenuType = ""
//                                                            }
//                                                            editMode = true
//                                                        }
//                                                    }
//                                                }
                                                
                                                // 安全地取消作业
                                                try {
                                                    awaitTapEventJob?.cancel()
                                                } catch (e: CancellationException) {
                                                    // 忽略取消异常
                                                }
                                            }
                                        } while (event.changes.any { it.id == down.id && it.pressed })
                                    } catch (e: CancellationException) {
                                        // 忽略取消异常
                                        Log.d(TAG, "手势处理被取消")
                                    } catch (e: Exception) {
                                        Log.e(TAG, "手势处理错误: ${e.message}")
                                    }
                                }
                            } catch (ex: Exception) {
                                Log.e(TAG, "awaitTapEventJob error: ${ex.message}")
                            }
                        }
                    }
            ) {
                if (editMode) {
                    // 编辑模式下显示文本输入框
                    BasicTextField(
                        value = text,
                        onValueChange = { text = it },
                        modifier = Modifier
                            .fillMaxSize()
                            .focusRequester(focusRequester),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Done
                        )
                    )
                    
                    // 安全地处理焦点
                    LaunchedEffect(editMode) {
                        if (editMode) {
                            focusLock.withLock {
                                try {
                                    // 延迟请求焦点，确保UI已更新
                                    delay(50)
                                    focusRequester.requestFocus()
                                } catch (e: Exception) {
                                    Log.e(TAG, "LaunchedEffect 中请求焦点失败: ${e.message}")
                                }
                            }
                        }
                    }
                    
                    // 当组件被移除前，确保清除焦点
                    DisposableEffect(Unit) {
                        onDispose {
                            scope.launch {
                                focusLock.withLock {
                                    try {
                                        focusManager.clearFocus()
                                    } catch (e: Exception) {
                                        Log.e(TAG, "DisposableEffect 中清除焦点失败: ${e.message}")
                                    }
                                }
                            }
                        }
                    }
                } else {
                    // 非编辑模式下显示提示文本
                    Text(
                        text = text.text,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 解决方案说明
            Text(
                text = "解决方案：",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Text(
                text = "1. 使用互斥锁(Mutex)协调焦点操作，确保不会同时进行多个焦点操作\n" +
                       "2. 使用 try-catch 块捕获所有可能的异常，特别是 CancellationException\n" +
                       "3. 使用 SafeFocusManager 包装原始焦点管理器，添加防抖和异常处理\n" +
                       "4. 使用 DisposableEffect 确保组件被移除前清除焦点\n" +
                       "5. 延迟焦点操作，确保UI状态已更新",
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}
