package com.work2502.compose.carsh//package com.example.compose25003
//
//import android.os.Bundle
//import android.util.Log
//import androidx.activity.ComponentActivity
//import androidx.activity.compose.setContent
//import androidx.activity.enableEdgeToEdge
//import androidx.compose.foundation.background
//import androidx.compose.foundation.border
//import androidx.compose.foundation.layout.Box
//import androidx.compose.foundation.layout.Column
//import androidx.compose.foundation.layout.Row
//import androidx.compose.foundation.layout.Spacer
//import androidx.compose.foundation.layout.fillMaxSize
//import androidx.compose.foundation.layout.fillMaxWidth
//import androidx.compose.foundation.layout.height
//import androidx.compose.foundation.layout.padding
//import androidx.compose.foundation.layout.width
//import androidx.compose.foundation.text.BasicTextField
//import androidx.compose.foundation.text.KeyboardActions
//import androidx.compose.foundation.text.KeyboardOptions
//import androidx.compose.material3.Button
//import androidx.compose.material3.Card
//import androidx.compose.material3.CardDefaults
//import androidx.compose.material3.MaterialTheme
//import androidx.compose.material3.Scaffold
//import androidx.compose.material3.Surface
//import androidx.compose.material3.Text
//import androidx.compose.material3.TextField
//import androidx.compose.runtime.Composable
//import androidx.compose.runtime.DisposableEffect
//import androidx.compose.runtime.LaunchedEffect
//import androidx.compose.runtime.getValue
//import androidx.compose.runtime.mutableStateOf
//import androidx.compose.runtime.remember
//import androidx.compose.runtime.rememberCoroutineScope
//import androidx.compose.runtime.setValue
//import androidx.compose.ui.Alignment
//import androidx.compose.ui.ExperimentalComposeUiApi
//import androidx.compose.ui.Modifier
//import androidx.compose.ui.focus.FocusDirection
//import androidx.compose.ui.focus.FocusManager
//import androidx.compose.ui.focus.FocusRequester
//import androidx.compose.ui.focus.focusRequester
//import androidx.compose.ui.focus.onFocusChanged
//import androidx.compose.ui.graphics.Color
//import androidx.compose.ui.platform.LocalContext
//import androidx.compose.ui.platform.LocalFocusManager
//import androidx.compose.ui.text.input.ImeAction
//import androidx.compose.ui.text.input.KeyboardType
//import androidx.compose.ui.text.input.TextFieldValue
//import androidx.compose.ui.unit.dp
//import com.example.compose25003.ui.theme.Compose25003Theme
//import kotlinx.coroutines.CancellationException
//import kotlinx.coroutines.Job
//import kotlinx.coroutines.delay
//import kotlinx.coroutines.launch
//import kotlinx.coroutines.sync.Mutex
//import kotlinx.coroutines.sync.withLock
//import java.lang.ref.WeakReference
//import java.util.concurrent.atomic.AtomicBoolean
//import kotlin.math.sqrt
//
///**
// * 解决自定义焦点清除逻辑导致的崩溃
// */
//class CustomFocusLogicSolutionActivity : ComponentActivity() {
//    companion object {
//        private const val TAG = "CustomFocusLogicSolution"
//    }
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        enableEdgeToEdge()
//        setContent {
//            Compose25003Theme {
//                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
//                    Surface(
//                        modifier = Modifier
//                            .fillMaxSize()
//                            .padding(innerPadding),
//                        color = MaterialTheme.colorScheme.background
//                    ) {
//                        CustomFocusLogicSolution()
//                    }
//                }
//            }
//        }
//    }
//}
//
///**
// * 安全的自定义焦点管理器，避免崩溃
// */
//class SafeCustomFocusManager(originalFocusManager: FocusManager) : FocusManager {
//    // 使用弱引用避免内存泄漏
//    private val originalFocusManagerRef = WeakReference(originalFocusManager)
//
//    // 使用原子布尔值确保线程安全
//    private val allowClearFocus = AtomicBoolean(true)
//    private val componentExists = AtomicBoolean(true)
//    private val asyncOperationInProgress = AtomicBoolean(false)
//
//    // 互斥锁，用于协调焦点操作
//    private val focusLock = Mutex()
//
//    // 设置是否允许清除焦点
//    fun setAllowClearFocus(allow: Boolean) {
//        allowClearFocus.set(allow)
//    }
//
//    // 设置组件是否存在
//    fun setComponentExists(exists: Boolean) {
//        componentExists.set(exists)
//    }
//
//    // 设置异步操作状态
//    fun setAsyncOperationInProgress(inProgress: Boolean) {
//        asyncOperationInProgress.set(inProgress)
//    }
//
//    // 安全地执行焦点操作
//    suspend fun safeOperation(operation: suspend () -> Unit) {
//        focusLock.withLock {
//            operation()
//        }
//    }
//
//    override fun clearFocus(force: Boolean) {
//        Log.d(TAG, "clearFocus called, allowClearFocus=${allowClearFocus.get()}, componentExists=${componentExists.get()}")
//
//        // 如果组件不存在，直接返回，不执行任何操作
//        if (!componentExists.get()) {
//            Log.d(TAG, "组件不存在，忽略清除焦点操作")
//            return
//        }
//
//        // 如果不允许清除焦点，记录日志并返回
//        if (!allowClearFocus.get()) {
//            Log.d(TAG, "不允许清除焦点，阻止操作")
//            return
//        }
//
//        // 安全地获取原始焦点管理器并执行操作
//        val originalFocusManager = originalFocusManagerRef.get()
//        if (originalFocusManager != null) {
//            try {
//                originalFocusManager.clearFocus()
//            } catch (e: Exception) {
//                Log.e(TAG, "清除焦点时出错: ${e.message}")
//            }
//        } else {
//            Log.e(TAG, "原始焦点管理器已被回收")
//        }
//    }
//
//    override fun clearFocus(force: Boolean) {
//        TODO("Not yet implemented")
//    }
//
//    override fun moveFocus(direction: FocusDirection): Boolean {
//        Log.d(TAG, "moveFocus called, direction=$direction, componentExists=${componentExists.get()}")
//
//        // 如果组件不存在，直接返回，不执行任何操作
//        if (!componentExists.get()) {
//            Log.d(TAG, "组件不存在，忽略移动焦点操作")
//            return
//        }
//
//        // 安全地获取原始焦点管理器并执行操作
//        val originalFocusManager = originalFocusManagerRef.get()
//        if (originalFocusManager != null) {
//            try {
//                originalFocusManager.moveFocus(direction)
//            } catch (e: Exception) {
//                Log.e(TAG, "移动焦点时出错: ${e.message}")
//            }
//        } else {
//            Log.e(TAG, "原始焦点管理器已被回收")
//        }
//    }
//}
//
//@Composable
//fun CustomFocusLogicSolution() {
//    // 状态
//    var showTextField by remember { mutableStateOf(true) }
//    var text by remember { mutableStateOf("") }
//    var operationCount by remember { mutableStateOf(0) }
//
//    // 焦点管理
//    val originalFocusManager = LocalFocusManager.current
//    val safeCustomFocusManager = remember { SafeCustomFocusManager(originalFocusManager) }
//    val focusRequester = remember { FocusRequester() }
//    val scope = rememberCoroutineScope()
//
//    // 记录当前焦点状态
//    var hasFocus by remember { mutableStateOf(false) }
//
//    // 当组件显示状态变化时，更新焦点管理器中的组件存在状态
//    LaunchedEffect(showTextField) {
//        safeCustomFocusManager.setComponentExists(showTextField)
//    }
//
//    Column(
//        modifier = Modifier
//            .fillMaxSize()
//            .padding(16.dp)
//    ) {
//        // 标题
//        Text(
//            text = "自定义焦点逻辑解决方案",
//            style = MaterialTheme.typography.headlineMedium,
//            modifier = Modifier.padding(bottom = 16.dp)
//        )
//
//        // 状态显示
//        Text(
//            text = "文本框显示: ${if (showTextField) "是" else "否"}",
//            style = MaterialTheme.typography.bodyLarge,
//            modifier = Modifier.padding(bottom = 8.dp)
//        )
//
//        Text(
//            text = "焦点状态: ${if (hasFocus) "有焦点" else "无焦点"}",
//            style = MaterialTheme.typography.bodyLarge,
//            modifier = Modifier.padding(bottom = 8.dp)
//        )
//
//        Text(
//            text = "安全操作次数: $operationCount",
//            style = MaterialTheme.typography.bodyLarge,
//            modifier = Modifier.padding(bottom = 16.dp)
//        )
//
//        // 控制按钮
//        Row(
//            modifier = Modifier.fillMaxWidth(),
//            verticalAlignment = Alignment.CenterVertically
//        ) {
//            Button(
//                onClick = {
//                    // 安全地切换文本框显示状态
//                    scope.launch {
//                        // 如果要隐藏文本框，先确保清除焦点
//                        if (showTextField) {
//                            safeCustomFocusManager.setAllowClearFocus(true)
//                            safeCustomFocusManager.clearFocus()
//                            delay(50) // 给焦点清除一些时间
//                        }
//                        showTextField = !showTextField
//                    }
//                }
//            ) {
//                Text(if (showTextField) "隐藏文本框" else "显示文本框")
//            }
//
//            Spacer(modifier = Modifier.width(8.dp))
//
//            Button(
//                onClick = {
//                    // 安全地请求焦点
//                    if (showTextField) {
//                        try {
//                            focusRequester.requestFocus()
//                        } catch (e: Exception) {
//                            Log.e(TAG, "请求焦点失败: ${e.message}")
//                        }
//                    }
//                }
//            ) {
//                Text("请求焦点")
//            }
//
//            Spacer(modifier = Modifier.width(8.dp))
//
//            Button(
//                onClick = {
//                    // 安全地尝试清除焦点
//                    safeCustomFocusManager.clearFocus()
//                }
//            ) {
//                Text("尝试清除焦点")
//            }
//        }
//
//        Spacer(modifier = Modifier.height(16.dp))
//
//        // 安全操作按钮
//        Button(
//            onClick = {
//                operationCount++
//                // 安全地模拟复杂场景
//                scope.launch {
//                    try {
//                        // 1. 确保文本框显示并有焦点
//                        showTextField = true
//                        delay(50) // 给UI更新一些时间
//                        focusRequester.requestFocus()
//
//                        // 2. 设置不允许清除焦点，并标记异步操作开始
//                        safeCustomFocusManager.safeOperation {
//                            safeCustomFocusManager.setAllowClearFocus(false)
//                            safeCustomFocusManager.setAsyncOperationInProgress(true)
//                        }
//
//                        // 3. 尝试清除焦点（会被阻止）
//                        safeCustomFocusManager.clearFocus()
//
//                        // 4. 模拟异步操作
//                        Log.d(TAG, "开始异步操作...")
//                        delay(100)
//
//                        // 5. 在异步操作过程中，安全地移除组件
//                        Log.d(TAG, "安全地移除组件...")
//                        safeCustomFocusManager.safeOperation {
//                            // 先标记组件不存在，然后再实际移除组件
//                            safeCustomFocusManager.setComponentExists(false)
//                            showTextField = false
//                        }
//
//                        // 6. 继续异步操作
//                        delay(100)
//
//                        // 7. 异步操作完成，安全地更新状态
//                        Log.d(TAG, "异步操作完成，更新状态")
//                        safeCustomFocusManager.safeOperation {
//                            safeCustomFocusManager.setAllowClearFocus(true)
//                            safeCustomFocusManager.setAsyncOperationInProgress(false)
//                        }
//
//                        // 8. 再次尝试清除焦点（此时已经安全，不会导致崩溃）
//                        Log.d(TAG, "安全地尝试清除焦点")
//                        safeCustomFocusManager.clearFocus()
//                    } catch (e: Exception) {
//                        Log.e(TAG, "安全操作过程中出错: ${e.message}")
//                    }
//                }
//            },
//            modifier = Modifier.fillMaxWidth()
//        ) {
//            Text("安全模拟复杂场景")
//        }
//
//        Spacer(modifier = Modifier.height(24.dp))
//
//        // 文本框区域
//        Card(
//            modifier = Modifier
//                .fillMaxWidth()
//                .height(200.dp),
//            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
//        ) {
//            Box(
//                modifier = Modifier
//                    .fillMaxSize()
//                    .padding(16.dp)
//            ) {
//                if (showTextField) {
//                    // 显示文本框
//                    TextField(
//                        value = text,
//                        onValueChange = { text = it },
//                        label = { Text("请输入文本") },
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .focusRequester(focusRequester)
//                            .onFocusChanged { focusState ->
//                                hasFocus = focusState.hasFocus
//                                Log.d(TAG, "焦点状态变化: $hasFocus")
//                            },
//                        keyboardOptions = KeyboardOptions(
//                            keyboardType = KeyboardType.Text,
//                            imeAction = ImeAction.Done
//                        ),
//                        keyboardActions = KeyboardActions(
//                            onDone = {
//                                // 使用安全的焦点管理器清除焦点
//                                safeCustomFocusManager.clearFocus()
//                            }
//                        )
//                    )
//
//                    // 监听组件生命周期，安全地处理组件移除
//                    DisposableEffect(Unit) {
//                        onDispose {
//                            Log.d(TAG, "TextField 被移除")
//                            scope.launch {
//                                safeCustomFocusManager.safeOperation {
//                                    // 确保组件被标记为不存在
//                                    safeCustomFocusManager.setComponentExists(false)
//
//                                    // 如果异步操作仍在进行中，记录日志但不执行可能导致崩溃的操作
//                                    if (safeCustomFocusManager.asyncOperationInProgress.get()) {
//                                        Log.d(TAG, "组件被移除时异步操作仍在进行中，但已安全处理")
//                                    }
//                                }
//                            }
//                        }
//                    }
//                } else {
//                    // 显示提示文本
//                    Text(
//                        text = "文本框已隐藏",
//                        modifier = Modifier.align(Alignment.Center)
//                    )
//                }
//            }
//        }
//
//        Spacer(modifier = Modifier.height(24.dp))
//
//        // 解决方案说明
//        Text(
//            text = "解决方案：",
//            style = MaterialTheme.typography.titleMedium,
//            modifier = Modifier.padding(bottom = 8.dp)
//        )
//
//        Text(
//            text = "1. 使用 SafeCustomFocusManager 跟踪组件的存在状态\n" +
//                   "2. 在组件被移除前，先标记组件不存在，然后再实际移除\n" +
//                   "3. 在焦点操作前检查组件是否存在，如果不存在则忽略操作\n" +
//                   "4. 使用互斥锁和原子变量确保线程安全\n" +
//                   "5. 使用弱引用避免内存泄漏\n" +
//                   "6. 在 DisposableEffect 中安全地处理组件移除",
//            style = MaterialTheme.typography.bodyMedium
//        )
//    }
//}
