package com.work2502.compose.carsh

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.PointerType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalViewConfiguration
import android.content.Intent
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.work2502.compose.ui.theme.Compose25003Theme
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.sqrt

/**
 * 模拟 pointerInput 与焦点管理冲突导致的崩溃
 * 崩溃信息: "ActiveParent with no focused child"
 */
class PointerInputFocusCrashActivity : ComponentActivity() {
    companion object {
        private const val TAG = "PointerInputFocusCrash"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Compose25003Theme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    Surface(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        PointerInputFocusCrashDemo()
                    }
                }
            }
        }
    }
}

// 扩展函数，计算两点之间的距离
fun androidx.compose.ui.geometry.Offset.getDistance(): Float {
    return sqrt(x * x + y * y)
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun PointerInputFocusCrashDemo() {
    // 状态
    var editMode by remember { mutableStateOf(false) }
    var text by remember { mutableStateOf(TextFieldValue("点击此区域进入编辑模式")) }
    var crashCount by remember { mutableStateOf(0) }
    var bottomMenuType by remember { mutableStateOf("") }

    // 焦点管理
    val focusManager = LocalFocusManager.current
    val focusRequester = remember { FocusRequester() }
    val scope = rememberCoroutineScope()
    var awaitTapEventJob by remember { mutableStateOf<Job?>(null) }
    val viewConfiguration = LocalViewConfiguration.current

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = "PointerInput 与焦点冲突崩溃演示",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 状态显示
        Text(
            text = "编辑模式: ${if (editMode) "开启" else "关闭"}",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Text(
            text = "崩溃尝试次数: $crashCount",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 控制按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Button(
                onClick = {
                    // 切换编辑模式
                    editMode = !editMode
                    if (!editMode) {
                        // 退出编辑模式时清除焦点
                        focusManager.clearFocus()
                    } else {
                        // 进入编辑模式时请求焦点
                        try {
                            focusRequester.requestFocus()
                        } catch (e: Exception) {
                            Log.e(TAG, "请求焦点失败: ${e.message}")
                        }
                    }
                }
            ) {
                Text(if (editMode) "退出编辑" else "进入编辑")
            }

            Spacer(modifier = Modifier.width(8.dp))

            Button(
                onClick = {
                    // 模拟崩溃场景：快速切换编辑模式并清除焦点
                    crashCount++
                    scope.launch {
                        try {
                            // 请求焦点
                            focusRequester.requestFocus()
                            // 进入编辑模式
                            editMode = true
                            // 短暂延迟
                            delay(50)
                            // 退出编辑模式并清除焦点
                            editMode = false
                            focusManager.clearFocus()
                            // 再次短暂延迟
                            delay(50)
                            // 再次请求焦点
                            focusRequester.requestFocus()
                        } catch (e: Exception) {
                            Log.e(TAG, "模拟崩溃失败: ${e.message}")
                        }
                    }
                }
            ) {
                Text("模拟崩溃")
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 编辑区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
                .background(Color.LightGray.copy(alpha = 0.3f))
                .padding(16.dp)
                // 这里是关键：模拟原代码中的 pointerInput 逻辑
                .pointerInput(editMode) {
                    awaitTapEventJob?.cancel()
                    if (editMode) return@pointerInput

                    awaitTapEventJob = scope.launch(Dispatchers.IO) {
                        try {
                            awaitEachGesture {
                                val down = awaitFirstDown()
                                Log.v(TAG, "awaitEachGesture, down: $down")
                                val downTime = System.currentTimeMillis()
                                var tapTimeout = viewConfiguration.longPressTimeoutMillis
                                var touchSlop = viewConfiguration.touchSlop

                                if (down.type == PointerType.Stylus) {
                                    // 扩大触控笔有效范围
                                    tapTimeout *= 3
                                    touchSlop *= 3
                                }

                                val tapPosition = down.position
                                do {
                                    val event = awaitPointerEvent()
                                    val currentTime = System.currentTimeMillis()
                                    if (event.changes.size != 1) break
                                    if (currentTime - downTime > tapTimeout) break

                                    val change = event.changes[0]
                                    val distance = (change.position - tapPosition).getDistance()
                                    if (distance > touchSlop) break

                                    if (change.id == down.id && !change.pressed) {
                                        val tapType = change.type
                                        when (tapType) {
                                            PointerType.Stylus -> {
                                                // 模拟更新底部菜单类型
                                                bottomMenuType = "BRUSH"
                                                // 进入编辑模式
                                                editMode = true
                                            }
                                            else -> {
                                                if (bottomMenuType == "BRUSH") {
                                                    bottomMenuType = ""
                                                }
                                                // 进入编辑模式
                                                editMode = true
                                            }
                                        }
                                        awaitTapEventJob?.cancel()
                                    }
                                } while (event.changes.any { it.id == down.id && it.pressed })
                            }
                        } catch (ex: Exception) {
                            Log.e(TAG, "awaitTapEventJob error: ${ex.message}")
                        }
                    }
                }
        ) {
            if (editMode) {
                // 编辑模式下显示文本输入框
                BasicTextField(
                    value = text,
                    onValueChange = { text = it },
                    modifier = Modifier
                        .fillMaxSize()
                        .focusRequester(focusRequester),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Done
                    )
                )

                // 这里添加一个 LaunchedEffect 来模拟焦点冲突
                LaunchedEffect(editMode) {
                    if (editMode) {
                        try {
                            // 请求焦点
                            focusRequester.requestFocus()

                            // 模拟在另一个协程中清除焦点，这可能导致冲突
                            launch {
                                delay(100)
                                try {
                                    // 这里可能导致崩溃
                                    focusManager.clearFocus()
                                } catch (e: Exception) {
                                    Log.e(TAG, "清除焦点失败: ${e.message}")
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "LaunchedEffect 中请求焦点失败: ${e.message}")
                        }
                    }
                }
            } else {
                // 非编辑模式下显示提示文本
                Text(
                    text = text.text,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 添加一个按钮来查看解决方案
        val context = LocalContext.current
        Button(
            onClick = {
                context.startActivity(Intent(context, PointerInputFocusCrashSolutionActivity::class.java))
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("查看解决方案")
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 崩溃原因说明
        Text(
            text = "崩溃原因：",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Text(
            text = "当用户与屏幕交互时，pointerInput 块中的协程尝试处理触摸事件，同时可能有其他地方（如 focusManager.clearFocus()）在修改焦点状态。当协程被取消但仍尝试访问焦点系统时，就会导致 \"ActiveParent with no focused child\" 异常。",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
