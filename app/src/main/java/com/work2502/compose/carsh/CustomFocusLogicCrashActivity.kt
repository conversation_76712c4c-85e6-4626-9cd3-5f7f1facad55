package com.work2502.compose.carsh

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.work2502.compose.ui.theme.Compose25003Theme
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 模拟复杂的自定义焦点清除逻辑导致的崩溃
 * 崩溃信息: "ActiveParent with no focused child"
 */
class CustomFocusLogicCrashActivity : ComponentActivity() {
    companion object {
        private const val TAG = "CustomFocusLogicCrash"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Compose25003Theme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    Surface(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        CustomFocusLogicCrashDemo()
                    }
                }
            }
        }
    }
}

/**
 * 自定义焦点管理器，实现阻止焦点离开的逻辑
 */
class CustomFocusManager(private val originalFocusManager: FocusManager) : FocusManager {
    // 标记是否允许清除焦点
    var allowClearFocus = false
    
    // 模拟复杂的异步操作
    var asyncOperationInProgress = false

    override fun clearFocus(force: Boolean) {
        Log.d("CustomFocusManager", "clearFocus called, allowClearFocus=$allowClearFocus")
        if (allowClearFocus) {
            originalFocusManager.clearFocus()
        } else {
            // 阻止焦点离开，模拟复杂的自定义逻辑
            Log.d("CustomFocusManager", "Blocking focus clear")
            // 这里不调用原始的 clearFocus 方法，从而阻止焦点离开
        }
    }

    override fun moveFocus(focusDirection: FocusDirection): Boolean {
        Log.d("CustomFocusManager", "moveFocus called, direction=$focusDirection")
        return originalFocusManager.moveFocus(focusDirection)
    }
}

@Composable
fun CustomFocusLogicCrashDemo() {
    // 状态
    var showTextField by remember { mutableStateOf(true) }
    var text by remember { mutableStateOf("") }
    var crashCount by remember { mutableStateOf(0) }
    
    // 焦点管理
    val originalFocusManager = LocalFocusManager.current
    val customFocusManager = remember { CustomFocusManager(originalFocusManager) }
    val focusRequester = remember { FocusRequester() }
    val scope = rememberCoroutineScope()
    
    // 记录当前焦点状态
    var hasFocus by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = "自定义焦点逻辑崩溃演示",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 状态显示
        Text(
            text = "文本框显示: ${if (showTextField) "是" else "否"}",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Text(
            text = "焦点状态: ${if (hasFocus) "有焦点" else "无焦点"}",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Text(
            text = "崩溃尝试次数: $crashCount",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 控制按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Button(
                onClick = {
                    // 切换文本框显示状态
                    showTextField = !showTextField
                }
            ) {
                Text(if (showTextField) "隐藏文本框" else "显示文本框")
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Button(
                onClick = {
                    // 请求焦点
                    if (showTextField) {
                        try {
                            focusRequester.requestFocus()
                        } catch (e: Exception) {
                            Log.e(TAG, "请求焦点失败: ${e.message}")
                        }
                    }
                }
            ) {
                Text("请求焦点")
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Button(
                onClick = {
                    // 尝试清除焦点
                    try {
                        customFocusManager.clearFocus()
                    } catch (e: Exception) {
                        Log.e(TAG, "清除焦点失败: ${e.message}")
                    }
                }
            ) {
                Text("尝试清除焦点")
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 模拟崩溃按钮
        Button(
            onClick = {
                crashCount++
                // 模拟崩溃场景：阻止焦点离开，然后在异步操作过程中移除组件
                scope.launch {
                    try {
                        // 1. 确保文本框显示并有焦点
                        showTextField = true
                        focusRequester.requestFocus()
                        
                        // 2. 设置不允许清除焦点
                        customFocusManager.allowClearFocus = false
                        customFocusManager.asyncOperationInProgress = true
                        
                        // 3. 尝试清除焦点（会被阻止）
                        customFocusManager.clearFocus()
                        
                        // 4. 模拟异步操作
                        Log.d(TAG, "开始异步操作...")
                        delay(100)
                        
                        // 5. 在异步操作过程中移除组件
                        Log.d(TAG, "移除组件...")
                        showTextField = false
                        
                        // 6. 继续异步操作
                        delay(100)
                        
                        // 7. 异步操作完成，尝试允许清除焦点
                        Log.d(TAG, "异步操作完成，允许清除焦点")
                        customFocusManager.allowClearFocus = true
                        customFocusManager.asyncOperationInProgress = false
                        
                        // 8. 再次尝试清除焦点（此时组件已被移除，可能导致崩溃）
                        Log.d(TAG, "再次尝试清除焦点")
                        try {
                            customFocusManager.clearFocus()
                        } catch (e: Exception) {
                            Log.e(TAG, "清除焦点时崩溃: ${e.message}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "模拟崩溃过程中出错: ${e.message}")
                    }
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("模拟崩溃场景")
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 文本框区域
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                if (showTextField) {
                    // 显示文本框
                    TextField(
                        value = text,
                        onValueChange = { text = it },
                        label = { Text("请输入文本") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester)
                            .onFocusChanged { focusState ->
                                hasFocus = focusState.hasFocus
                                Log.d(TAG, "焦点状态变化: $hasFocus")
                            },
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Done
                        ),
                        keyboardActions = KeyboardActions(
                            onDone = {
                                // 使用自定义焦点管理器清除焦点
                                customFocusManager.clearFocus()
                            }
                        )
                    )
                    
                    // 监听组件生命周期
                    DisposableEffect(Unit) {
                        onDispose {
                            Log.d(TAG, "TextField 被移除")
                            // 如果异步操作仍在进行中，这里可能导致问题
                            if (customFocusManager.asyncOperationInProgress) {
                                Log.d(TAG, "警告：组件被移除时异步操作仍在进行中")
                            }
                        }
                    }
                } else {
                    // 显示提示文本
                    Text(
                        text = "文本框已隐藏",
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 崩溃原因说明
        Text(
            text = "崩溃原因：",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Text(
            text = "当一个组件实现了自定义的焦点清除逻辑（阻止焦点离开），但在这个逻辑执行过程中，组件被重组或移除，就会导致焦点树状态不一致。焦点系统中存在一个标记为 ActiveParent 的节点，但它的活跃子节点已经不存在，从而引发 \"ActiveParent with no focused child\" 异常。",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
