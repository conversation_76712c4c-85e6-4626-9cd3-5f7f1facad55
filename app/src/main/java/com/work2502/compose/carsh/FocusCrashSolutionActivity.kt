package com.work2502.compose.carsh

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.work2502.compose.ui.theme.Compose25003Theme

/**
 * 展示如何解决 FocusTargetNode.requireActiveChild() 崩溃的 Activity
 */
class FocusCrashSolutionActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Compose25003Theme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    Surface(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        FocusCrashSolution()
                    }
                }
            }
        }
    }
}

/**
 * 安全的焦点管理器，防止崩溃
 */
@Composable
fun SafeFocusManager(content: @Composable (FocusManager) -> Unit) {
    val originalFocusManager = LocalFocusManager.current
    val safeFocusManager = remember {
        object : FocusManager {
            private var lastClearTime = 0L
            private val minTimeBetweenClears = 100L // 最小间隔时间（毫秒）

            override fun clearFocus(force: Boolean) {
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastClearTime > minTimeBetweenClears) {
                    try {
                        originalFocusManager.clearFocus()
                    } catch (e: Exception) {
                        Log.e("SafeFocusManager", "Error clearing focus: ${e.message}")
                    }
                    lastClearTime = currentTime
                } else {
                    Log.d("SafeFocusManager", "Skipped clearFocus() call - too soon after previous call")
                }
            }

            override fun moveFocus(focusDirection: FocusDirection): Boolean {
                try {
                    originalFocusManager.moveFocus(focusDirection)
                } catch (e: Exception) {
                    Log.e("SafeFocusManager", "Error moving focus: ${e.message}")
                }
                return true
            }
        }
    }
    
    content(safeFocusManager)
}

@Composable
fun FocusCrashSolution() {
    // 状态
    var showFirstTextField by remember { mutableStateOf(true) }
    var showSecondTextField by remember { mutableStateOf(true) }
    var text1 by remember { mutableStateOf("") }
    var text2 by remember { mutableStateOf("") }
    var operationCount by remember { mutableStateOf(0) }
    
    // 使用安全的焦点管理器
    SafeFocusManager { focusManager ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 标题
            Text(
                text = "焦点崩溃解决方案演示",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 操作计数
            Text(
                text = "安全操作次数: $operationCount",
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 解决方案1: 延迟移除有焦点的组件
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "解决方案1: 延迟移除有焦点的输入框",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    // 第一个输入框 (可以被移除)
                    AnimatedVisibility(
                        visible = showFirstTextField,
                        enter = fadeIn(),
                        exit = fadeOut()
                    ) {
                        val focusRequester = remember { FocusRequester() }
                        
                        OutlinedTextField(
                            value = text1,
                            onValueChange = { text1 = it },
                            label = { Text("输入框 1") },
                            modifier = Modifier
                                .fillMaxWidth()
                                .focusRequester(focusRequester)
                                .padding(vertical = 8.dp),
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Text,
                                imeAction = ImeAction.Next
                            ),
                            keyboardActions = KeyboardActions(
                                onNext = { focusManager.moveFocus(FocusDirection.Down) }
                            )
                        )
                        
                        // 当组件被移除前，确保清除焦点
                        DisposableEffect(Unit) {
                            onDispose {
                                try {
                                    focusManager.clearFocus()
                                } catch (e: Exception) {
                                    Log.e("FocusSolution", "Error in DisposableEffect: ${e.message}")
                                }
                            }
                        }
                    }
                    
                    // 安全操作按钮
                    Button(
                        onClick = {
                            operationCount++
                            // 安全方式: 先清除焦点，然后延迟移除组件
                            focusManager.clearFocus()
                            Handler(Looper.getMainLooper()).postDelayed({
                                showFirstTextField = false
                            }, 100)
                        },
                        modifier = Modifier.align(Alignment.End)
                    ) {
                        Text("安全移除输入框")
                    }
                    
                    Button(
                        onClick = { showFirstTextField = true },
                        modifier = Modifier.align(Alignment.End)
                    ) {
                        Text("恢复输入框")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 解决方案2: 防止快速连续清除焦点
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "解决方案2: 防止快速连续清除焦点",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    // 第二个输入框
                    val focusRequester = remember { FocusRequester() }
                    OutlinedTextField(
                        value = text2,
                        onValueChange = { text2 = it },
                        label = { Text("输入框 2") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester)
                            .padding(vertical = 8.dp)
                    )
                    
                    // 安全操作按钮
                    Button(
                        onClick = {
                            operationCount++
                            // 安全方式: 使用 SafeFocusManager 防止快速连续清除焦点
                            focusRequester.requestFocus()
                            focusManager.clearFocus() // 这里使用的是安全的焦点管理器
                            focusManager.clearFocus() // 这个调用会被忽略，因为时间间隔太短
                            
                            // 延迟后再次清除焦点是安全的
                            Handler(Looper.getMainLooper()).postDelayed({
                                focusManager.clearFocus()
                            }, 150)
                        },
                        modifier = Modifier.align(Alignment.End)
                    ) {
                        Text("安全清除焦点")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 解决方案3: 安全的焦点转移
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "解决方案3: 安全的焦点转移",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 源输入框
                        TextField(
                            value = "",
                            onValueChange = {},
                            label = { Text("源输入框") },
                            modifier = Modifier
                                .weight(1f)
                                .padding(end = 8.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        // 目标输入框 (可以被移除)
                        AnimatedVisibility(
                            visible = showSecondTextField,
                            enter = fadeIn(),
                            exit = fadeOut()
                        ) {
                            TextField(
                                value = "",
                                onValueChange = {},
                                label = { Text("目标输入框") },
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                    
                    // 安全操作按钮
                    Button(
                        onClick = {
                            operationCount++
                            // 安全方式: 先检查目标是否存在，然后延迟移除
                            if (showSecondTextField) {
                                // 先移动焦点
                                focusManager.moveFocus(FocusDirection.Right)
                                // 然后延迟移除目标
                                Handler(Looper.getMainLooper()).postDelayed({
                                    showSecondTextField = false
                                }, 100)
                            }
                        },
                        modifier = Modifier.align(Alignment.End)
                    ) {
                        Text("安全移除目标")
                    }
                    
                    Button(
                        onClick = { showSecondTextField = true },
                        modifier = Modifier.align(Alignment.End)
                    ) {
                        Text("恢复目标输入框")
                    }
                }
            }
        }
    }
}
