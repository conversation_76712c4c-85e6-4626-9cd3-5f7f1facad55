package com.work2502.zoomtext.compose

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.ui.viewinterop.AndroidView
import android.widget.EditText
import android.text.InputType
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalView
import com.work2502.multitouch.EnhancedZoomableBox
import com.work2502.multitouch.rememberEnhancedZoomableState
import kotlinx.coroutines.delay
import kotlin.math.roundToInt

/**
 * PDF阅读器Compose主屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PdfLikeReaderEditComposeScreen() {
    // 创建可缩放状态
    val zoomableState = rememberEnhancedZoomableState(
        minScale = 0.8f,
        maxScale = 1.3f,
        enableRotation = false
    )

    // 获取协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 控制面板显示状态
    var showControls by remember { mutableStateOf(true) }

    // 内容类型状态
    var contentType by remember { mutableStateOf(ContentType.DOCUMENT) }

    // 自动隐藏控制面板
    LaunchedEffect(showControls) {
        if (showControls) {
            delay(5000)
            showControls = false
        }
    }

    Scaffold(
        topBar = {
            AnimatedVisibility(
                visible = showControls,
                enter = slideInVertically() + fadeIn(),
                exit = slideOutVertically() + fadeOut()
            ) {
                TopAppBar(
                    title = {
                        Column {
                            Text(
                                text = "PDF阅读器 Compose版",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = "缩放: ${(zoomableState.scale * 100).roundToInt()}%",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                )
            }
        },
        bottomBar = {
        }
    ) { paddingValues ->
        // 主内容区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color.Gray.copy(alpha = 0.1f))
                .pointerInput(Unit) {
                    // 点击显示/隐藏控制面板
                    detectTapGestures(
                        onTap = { showControls = !showControls }
                    )
                }
        ) {
            // 使用增强版ZoomableBox组件
            EnhancedZoomableBox(
                modifier = Modifier.fillMaxSize(),
                state = zoomableState,
                enableInertia = true,
                enableDoubleTapToZoom = false,
                onTap = {
                    // 点击时切换控制面板显示状态
                    showControls = !showControls
                }
            ) {
                MixedEditContent()
            }
        }
    }
}


/**
 * 混合内容
 */
@Composable
fun MixedEditContent() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 使用改进的可缩放EditText组件
        ScalableEditText(
            modifier = Modifier.fillMaxWidth()
        )

        // 其他内容...
    }
}

/**
 * 可缩放的EditText组件
 * 专门处理缩放环境下的光标位置问题
 */
@Composable
fun ScalableEditText(
    modifier: Modifier = Modifier,
    initialText: String = "这里是传统View的EditText\n\n支持所有原生EditText功能：\n• 文本选择\n• 复制粘贴\n• 输入法支持\n• 光标控制\n• 长按菜单\n\n缩放后光标位置已优化！".repeat(50)
) {
    // 获取根视图用于监听变换
    val rootView = LocalView.current

    // 记住EditText实例
    var editTextRef by remember { mutableStateOf<EditText?>(null) }

    // 记住上次的选择位置
    var lastSelectionStart by remember { mutableStateOf(-1) }
    var lastSelectionEnd by remember { mutableStateOf(-1) }

    AndroidView(
        factory = { context ->
            object : EditText(context) {
                override fun onSelectionChanged(selStart: Int, selEnd: Int) {
                    super.onSelectionChanged(selStart, selEnd)
                    // 跟踪选择位置变化
                    lastSelectionStart = selStart
                    lastSelectionEnd = selEnd
                }
            }.apply {
                setText(initialText)
                inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_FLAG_MULTI_LINE
                minLines = 3
                setPadding(20, 20, 20, 20)
                setBackgroundColor(android.graphics.Color.parseColor("#F5F5F5"))
                textSize = 14f
                setTextColor(android.graphics.Color.BLACK)
                hint = "在这里输入文本..."

                // 设置布局参数
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )

                // 禁用过度滚动
                overScrollMode = View.OVER_SCROLL_NEVER

                // 设置IME选项
                imeOptions = imeOptions or EditorInfo.IME_FLAG_NO_EXTRACT_UI


                // 监听焦点变化
                setOnFocusChangeListener { _, hasFocus ->
                    if (hasFocus) {
                        // 获得焦点时，确保光标可见
                        post {
                            if (lastSelectionStart >= 0 && lastSelectionEnd >= 0) {
                                setSelection(lastSelectionStart, lastSelectionEnd)
                            }
                        }
                    }
                }

                // 监听布局变化（缩放时会触发）
                addOnLayoutChangeListener { _, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
                    // 如果布局发生变化且有焦点
                    if (hasFocus() && (left != oldLeft || top != oldTop || right != oldRight || bottom != oldBottom)) {
                        post {
                            // 强制重新设置光标位置
                            if (lastSelectionStart >= 0 && lastSelectionEnd >= 0) {
                                setSelection(lastSelectionStart, lastSelectionEnd)
                            }
                            // 强制重绘
                            invalidate()
                        }
                    }
                }

                // 保存引用
                editTextRef = this
            }
        },
        update = { editText ->
            // 当组件重组时，确保光标位置正确
            if (editText.hasFocus() && lastSelectionStart >= 0 && lastSelectionEnd >= 0) {
                editText.post {
                    editText.setSelection(lastSelectionStart, lastSelectionEnd)
                }
            }
        },
        modifier = modifier
    )
}