package com.work2502.zoomtext.compose

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动PdfLikeReaderComposeActivity的辅助类
 */
object PdfLikeReaderComposeLauncher {

    /**
     * 创建一个启动PdfLikeReaderComposeActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): Button {
        val button = Button(context)
        button.text = "启动PDF阅读器Compose版"
        button.setOnClickListener {
            val intent = Intent(context, PdfLikeReaderComposeActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动PdfLikeReaderComposeActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
        val intent = Intent(activity, PdfLikeReaderComposeActivity::class.java)
        activity.startActivity(intent)
    }
}
