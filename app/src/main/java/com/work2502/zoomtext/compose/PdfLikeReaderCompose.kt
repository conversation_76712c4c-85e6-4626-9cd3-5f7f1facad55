package com.work2502.zoomtext.compose

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.TextFieldValue
import com.work2502.multitouch.EnhancedZoomableBox
import com.work2502.multitouch.EnhancedZoomableState
import com.work2502.multitouch.rememberEnhancedZoomableState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

/**
 * PDF阅读器Compose主屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PdfLikeReaderComposeScreen() {
    // 创建可缩放状态
    val zoomableState = rememberEnhancedZoomableState(
        minScale = 0.8f,
        maxScale = 1.3f,
        enableRotation = false
    )

    // 获取协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 控制面板显示状态
    var showControls by remember { mutableStateOf(true) }

    // 内容类型状态
    var contentType by remember { mutableStateOf(ContentType.DOCUMENT) }

    // 自动隐藏控制面板
    LaunchedEffect(showControls) {
        if (showControls) {
            delay(5000)
            showControls = false
        }
    }

    Scaffold(
        topBar = {
            AnimatedVisibility(
                visible = showControls,
                enter = slideInVertically() + fadeIn(),
                exit = slideOutVertically() + fadeOut()
            ) {
                TopAppBar(
                    title = {
                        Column {
                            Text(
                                text = "PDF阅读器 Compose版",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = "缩放: ${(zoomableState.scale * 100).roundToInt()}%",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                )
            }
        },
        bottomBar = {
            AnimatedVisibility(
                visible = showControls,
                enter = slideInVertically { it } + fadeIn(),
                exit = slideOutVertically { it } + fadeOut()
            ) {
                PdfReaderBottomBar(
                    zoomableState = zoomableState,
                    contentType = contentType,
                    onContentTypeChange = { contentType = it },
                    onResetView = {
                        coroutineScope.launch {
                            zoomableState.reset()
                        }
                    }
                )
            }
        }
    ) { paddingValues ->
        // 主内容区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color.Gray.copy(alpha = 0.1f))
                .pointerInput(Unit) {
                    // 点击显示/隐藏控制面板
                    detectTapGestures(
                        onTap = { showControls = !showControls }
                    )
                }
        ) {
            // 使用增强版ZoomableBox组件
            EnhancedZoomableBox(
                modifier = Modifier.fillMaxSize(),
                state = zoomableState,
                enableInertia = true,
                enableDoubleTapToZoom = false,
                onTap = {
                    // 点击时切换控制面板显示状态
                    showControls = !showControls
                }
            ) {
                // 根据内容类型显示不同内容
                when (contentType) {
                    ContentType.DOCUMENT -> DocumentContent()
                    ContentType.IMAGE -> ImageContent()
                    ContentType.MIXED -> MixedContent()
                }
            }

            // 使用提示
            AnimatedVisibility(
                visible = !showControls,
                enter = fadeIn(),
                exit = fadeOut(),
                modifier = Modifier.align(Alignment.BottomCenter)
            ) {
                Card(
                    modifier = Modifier
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Black.copy(alpha = 0.7f)
                    )
                ) {
                    Text(
                        text = "双指缩放 • 拖拽滚动 • 双击快速缩放 • 点击显示控制面板",
                        modifier = Modifier.padding(12.dp),
                        color = Color.White,
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

/**
 * 内容类型枚举
 */
enum class ContentType(val displayName: String, val icon: ImageVector) {
    DOCUMENT("文档", Icons.Default.Description),
    IMAGE("图片", Icons.Default.Image),
    MIXED("混合", Icons.Default.ViewModule)
}

/**
 * 底部控制栏
 */
@Composable
fun PdfReaderBottomBar(
    zoomableState: EnhancedZoomableState,
    contentType: ContentType,
    onContentTypeChange: (ContentType) -> Unit,
    onResetView: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 缩放控制
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = {
                        coroutineScope.launch {
                            zoomableState.updateScale(zoomableState.scale * 0.8f)
                        }
                    }
                ) {
                    Icon(Icons.Default.ZoomOut, contentDescription = "缩小")
                }

                Button(
                    onClick = onResetView,
                    modifier = Modifier.padding(horizontal = 8.dp)
                ) {
                    Text("重置")
                }

                IconButton(
                    onClick = {
                        coroutineScope.launch {
                            zoomableState.updateScale(zoomableState.scale * 1.25f)
                        }
                    }
                ) {
                    Icon(Icons.Default.ZoomIn, contentDescription = "放大")
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 内容类型切换
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                ContentType.values().forEach { type ->
                    FilterChip(
                        onClick = { onContentTypeChange(type) },
                        label = { Text(type.displayName) },
                        selected = contentType == type,
                        leadingIcon = {
                            Icon(
                                imageVector = type.icon,
                                contentDescription = type.displayName,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    )
                }
            }
        }
    }
}

/**
 * 文档内容
 */
@Composable
fun DocumentContent() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(24.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "PDF阅读器示例文档",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )

        Divider()

        // 段落内容
        DocumentParagraph(
            title = "功能特点",
            content = """
                • 双指缩放：支持0.5x到5.0x的缩放范围
                • 拖拽滚动：支持全方向滚动浏览
                • 双击快速缩放：在默认大小和放大状态间切换
                • 惯性滚动：快速滑动后的惯性效果
                • 边界约束：自动限制滚动范围
                • 平滑动画：流畅的缩放和滚动动画
            """.trimIndent()
        )

        DocumentParagraph(
            title = "使用方法",
            content = """
                1. 使用双指捏合手势进行缩放
                2. 使用单指拖拽进行滚动
                3. 双击屏幕快速缩放
                4. 点击屏幕显示/隐藏控制面板
                5. 使用底部按钮进行精确控制
            """.trimIndent()
        )

        DocumentParagraph(
            title = "技术实现",
            content = """
                本示例使用Jetpack Compose实现，基于以下核心技术：
                • detectTransformGestures：处理多点触控手势
                • graphicsLayer：应用变换效果
                • Animatable：实现平滑动画
                • pointerInput：处理触摸事件
                • LaunchedEffect：管理协程和副作用
            """.trimIndent()
        )

        // 可编辑文本区域 - 使用传统View的EditText
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "可编辑区域 (缩放感知的TextField)",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // 使用缩放感知的TextField组件
                ZoomAwareTextField(
                    modifier = Modifier.fillMaxWidth(),
                    initialText = """这是一个缩放感知的TextField组件！

在缩放环境下，普通的TextField会出现光标位置错误的问题。
这个组件解决了这个问题：

• 监听焦点状态变化
• 在缩放时重新设置焦点
• 使用TextFieldValue管理状态
• 优化光标位置计算

请尝试缩放后点击编辑，光标位置应该是正确的！

您可以在这里输入任何文本来测试功能。"""
                )
            }
        }

        // 添加更多内容用于测试滚动
        repeat(5) { index ->
            DocumentParagraph(
                title = "测试段落 ${index + 1}",
                content = "这是第${index + 1}个测试段落，用于测试滚动功能。当内容超出屏幕范围时，您可以通过拖拽手势来滚动查看。滚动支持惯性效果，让操作更加自然流畅。"
            )
        }
    }
}

/**
 * 文档段落组件
 */
@Composable
fun DocumentParagraph(
    title: String,
    content: String
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = title,
            fontSize = 20.sp,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.primary
        )

        Text(
            text = content,
            fontSize = 16.sp,
            lineHeight = 24.sp,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

/**
 * 图片内容
 */
@Composable
fun ImageContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .size(400.dp, 600.dp)
                .border(2.dp, MaterialTheme.colorScheme.outline, RoundedCornerShape(8.dp)),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Image,
                    contentDescription = "图片",
                    modifier = Modifier.size(120.dp),
                    tint = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(24.dp))

                Text(
                    text = "模拟图片内容",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "尺寸: 400 × 600 dp",
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "支持缩放和滚动",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(24.dp))

                // 模拟图片网格
                Column(
                    modifier = Modifier.size(200.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    repeat(3) { row ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            repeat(3) { col ->
                                Box(
                                    modifier = Modifier
                                        .weight(1f)
                                        .aspectRatio(1f)
                                        .background(
                                            MaterialTheme.colorScheme.primary.copy(alpha = 0.3f),
                                            RoundedCornerShape(4.dp)
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "${row * 3 + col + 1}",
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 混合内容
 */
@Composable
fun MixedContent() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "混合内容示例",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )

        // 图片卡片
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    repeat(3) { index ->
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1f)
                                .background(
                                    MaterialTheme.colorScheme.primaryContainer,
                                    RoundedCornerShape(8.dp)
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Image,
                                contentDescription = "图片 ${index + 1}",
                                modifier = Modifier.size(32.dp)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = "图片网格示例",
                    fontWeight = FontWeight.SemiBold
                )

                Text(
                    text = "这里展示了三张模拟图片，演示混合内容的布局效果。",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 文本内容卡片 - 使用传统View EditText
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "文本内容区域 (传统View EditText)",
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                // 使用缩放感知的TextField组件
                ZoomAwareTextField(
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        // 按钮组
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "交互元素",
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = { },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("按钮 1")
                    }

                    OutlinedButton(
                        onClick = { },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("按钮 2")
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                var sliderValue by remember { mutableFloatStateOf(0.5f) }

                Text(
                    text = "滑块控件: ${(sliderValue * 100).roundToInt()}%",
                    fontSize = 14.sp
                )

                Slider(
                    value = sliderValue,
                    onValueChange = { sliderValue = it },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * 缩放感知的TextField组件
 * 解决缩放后光标位置不正确的问题
 */
@Composable
fun ZoomAwareTextField(
    modifier: Modifier = Modifier,
    initialText: String = """这是一个缩放感知的TextField组件示例。

在缩放环境下，普通的TextField会出现光标位置错误的问题。
这个组件通过以下方式解决了这个问题：

• 监听焦点状态变化
• 在缩放时重新设置焦点
• 使用TextFieldValue管理状态
• 优化光标位置计算

请尝试缩放后点击编辑，光标位置应该是正确的！

您可以在这里输入任何文本来测试功能。"""
) {
    // 文本状态
    var textFieldValue by remember {
        mutableStateOf(TextFieldValue(text = initialText))
    }

    // 焦点管理
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    var isFocused by remember { mutableStateOf(false) }

    // 协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 监听焦点变化，在缩放时重新处理焦点
    LaunchedEffect(isFocused) {
        if (isFocused) {
            // 延迟一下确保布局稳定
            delay(100)
            // 重新请求焦点以确保光标位置正确
            focusRequester.requestFocus()
        }
    }

    TextField(
        value = textFieldValue,
        onValueChange = { newValue ->
            textFieldValue = newValue
        },
        modifier = modifier
            .focusRequester(focusRequester)
            .onFocusChanged { focusState ->
                val wasFocused = isFocused
                isFocused = focusState.isFocused

                // 如果刚获得焦点，延迟处理以确保光标位置正确
                if (!wasFocused && focusState.isFocused) {
                    coroutineScope.launch {
                        delay(50)
                        // 保存当前光标位置
                        val currentSelection = textFieldValue.selection
                        // 重新设置文本和光标位置
                        textFieldValue = textFieldValue.copy(
                            selection = currentSelection
                        )
                    }
                }
            },
        placeholder = {
            Text("在这里输入文本...")
        },
        colors = TextFieldDefaults.colors(
            focusedContainerColor = MaterialTheme.colorScheme.surface,
            unfocusedContainerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(8.dp),
        maxLines = 8
    )
}

/**
 * 另一种解决方案：使用BasicTextField的缩放感知组件
 */
@Composable
fun ZoomAwareBasicTextField(
    modifier: Modifier = Modifier,
    initialText: String = "这是使用BasicTextField实现的缩放感知文本输入框..."
) {
    var textFieldValue by remember {
        mutableStateOf(TextFieldValue(text = initialText))
    }

    val focusRequester = remember { FocusRequester() }
    var isFocused by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        BasicTextField(
            value = textFieldValue,
            onValueChange = { newValue ->
                textFieldValue = newValue
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .focusRequester(focusRequester)
                .onFocusChanged { focusState ->
                    val wasFocused = isFocused
                    isFocused = focusState.isFocused

                    if (!wasFocused && focusState.isFocused) {
                        coroutineScope.launch {
                            delay(100)
                            // 强制重新设置光标位置
                            val currentSelection = textFieldValue.selection
                            textFieldValue = textFieldValue.copy(
                                selection = currentSelection
                            )
                        }
                    }
                },
            textStyle = TextStyle(
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurface
            ),
            decorationBox = { innerTextField ->
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            MaterialTheme.colorScheme.surface,
                            RoundedCornerShape(4.dp)
                        )
                        .padding(12.dp)
                ) {
                    if (textFieldValue.text.isEmpty()) {
                        Text(
                            text = "在这里输入文本...",
                            style = TextStyle(
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        )
                    }
                    innerTextField()
                }
            }
        )
    }
}