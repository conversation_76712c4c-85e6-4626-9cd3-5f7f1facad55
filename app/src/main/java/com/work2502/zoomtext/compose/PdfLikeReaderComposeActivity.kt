package com.work2502.zoomtext.compose

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.work2502.compose.ui.theme.Compose25003Theme

/**
 * PDF阅读器Compose版本Activity
 * 展示使用Jetpack Compose实现的PDF阅读器功能
 */
class PdfLikeReaderComposeActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Compose25003Theme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
//                    PdfLikeReaderEditComposeScreen()
                    PdfLikeReaderComposeScreen()
                }
            }
        }
    }
}

/**
 * 预览
 */
@Preview(showBackground = true)
@Composable
fun PdfLikeReaderComposeScreenPreview() {
    Compose25003Theme {
        PdfLikeReaderEditComposeScreen()
    }
}
