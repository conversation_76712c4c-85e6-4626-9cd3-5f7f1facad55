# PdfLikeReaderCompose 使用说明

## 概述

`PdfLikeReaderCompose` 是使用Jetpack Compose实现的PDF阅读器功能组件，提供与传统View版本相同的缩放、滚动和多点触控功能，但采用了现代化的Compose声明式UI架构。

## 主要功能

### 1. 缩放功能
- **双指缩放（Pinch-to-Zoom）**：支持双指捏合手势进行缩放
- **缩放范围**：0.5x - 5.0x
- **双击快速缩放**：在默认大小和放大状态间切换
- **平滑动画**：基于Compose Animatable的流畅动画效果

### 2. 滚动功能
- **全方向滚动**：支持垂直和水平方向的滚动
- **惯性滚动**：支持惯性滚动效果
- **边界约束**：自动约束滚动边界
- **智能居中**：内容小于视图时自动居中显示

### 3. 触摸交互
- **多点触控**：基于detectTransformGestures的手势处理
- **手势区分**：智能区分缩放和滚动操作
- **触摸反馈**：点击显示/隐藏控制面板

### 4. UI特性
- **Material Design 3**：采用最新的Material Design设计语言
- **动态主题**：支持系统主题切换
- **响应式布局**：适配不同屏幕尺寸
- **无障碍支持**：完整的无障碍功能支持

## 技术实现

### 1. 核心组件
- **EnhancedZoomableBox**：基于现有的增强版缩放组件
- **detectTransformGestures**：处理多点触控手势
- **graphicsLayer**：应用变换效果
- **Animatable**：实现平滑动画

### 2. 状态管理
- **EnhancedZoomableState**：管理缩放和偏移状态
- **remember**：保持组件状态
- **LaunchedEffect**：处理副作用和协程

### 3. UI架构
- **Scaffold**：提供标准的Material Design布局结构
- **AnimatedVisibility**：控制UI元素的显示/隐藏动画
- **Card**：提供卡片式布局容器

## 内容类型

### 1. 文档内容（Document）
- 模拟PDF文档的文本内容
- 包含标题、段落、可编辑文本区域
- 支持长文档的滚动浏览

### 2. 图片内容（Image）
- 模拟图片查看器功能
- 固定尺寸的图片容器
- 支持缩放查看图片细节

### 3. 混合内容（Mixed）
- 包含文本、图片、按钮、滑块等多种UI元素
- 演示复杂布局的缩放和滚动
- 展示交互式元素的处理

## 使用方法

### 1. 基本使用

```kotlin
@Composable
fun MyPdfReader() {
    PdfLikeReaderComposeScreen()
}
```

### 2. 自定义内容

```kotlin
@Composable
fun CustomPdfReader() {
    val zoomableState = rememberEnhancedZoomableState(
        minScale = 0.5f,
        maxScale = 5f
    )
    
    EnhancedZoomableBox(
        state = zoomableState,
        enableInertia = true,
        enableDoubleTapToZoom = true
    ) {
        // 您的自定义内容
        YourCustomContent()
    }
}
```

### 3. 状态控制

```kotlin
@Composable
fun ControlledPdfReader() {
    val zoomableState = rememberEnhancedZoomableState()
    val coroutineScope = rememberCoroutineScope()
    
    Column {
        // 控制按钮
        Row {
            Button(
                onClick = {
                    coroutineScope.launch {
                        zoomableState.updateScale(2f)
                    }
                }
            ) {
                Text("放大到2倍")
            }
            
            Button(
                onClick = {
                    coroutineScope.launch {
                        zoomableState.reset()
                    }
                }
            ) {
                Text("重置")
            }
        }
        
        // PDF阅读器
        EnhancedZoomableBox(state = zoomableState) {
            YourContent()
        }
    }
}
```

## 手势操作

### 1. 缩放手势
- **双指捏合**：放大或缩小内容
- **双击**：在默认大小和放大状态之间切换

### 2. 滚动手势
- **单指拖拽**：滚动浏览内容
- **惯性滚动**：快速滑动后的惯性效果

### 3. 交互手势
- **单击**：显示/隐藏控制面板
- **长按**：可扩展的长按功能

## 控制面板功能

### 1. 顶部栏
- 显示当前缩放比例
- 应用标题和状态信息

### 2. 底部控制栏
- **缩放控制**：放大、缩小、重置按钮
- **内容切换**：文档、图片、混合内容切换
- **快速操作**：常用功能的快速访问

### 3. 自动隐藏
- 5秒后自动隐藏控制面板
- 点击屏幕重新显示
- 平滑的显示/隐藏动画

## 与传统View版本的对比

### 优势
1. **声明式UI**：更简洁的代码结构
2. **状态管理**：更好的状态管理机制
3. **动画效果**：更流畅的动画表现
4. **主题支持**：完整的Material Design 3支持
5. **性能优化**：Compose的重组优化

### 适用场景
1. **新项目**：推荐使用Compose版本
2. **现代化改造**：将传统View迁移到Compose
3. **复杂UI**：需要复杂布局和动画的场景
4. **主题适配**：需要动态主题的应用

## 扩展功能

### 1. 自定义内容类型
可以轻松添加新的内容类型：

```kotlin
enum class ContentType(val displayName: String, val icon: ImageVector) {
    DOCUMENT("文档", Icons.Default.Description),
    IMAGE("图片", Icons.Default.Image),
    MIXED("混合", Icons.Default.ViewModule),
    CUSTOM("自定义", Icons.Default.Extension) // 新增类型
}
```

### 2. 自定义手势
可以扩展手势处理：

```kotlin
.pointerInput(Unit) {
    detectDragGestures { change, dragAmount ->
        // 自定义拖拽处理
    }
}
```

### 3. 自定义动画
可以自定义动画效果：

```kotlin
val animatedScale by animateFloatAsState(
    targetValue = targetScale,
    animationSpec = spring(
        dampingRatio = Spring.DampingRatioMediumBouncy,
        stiffness = Spring.StiffnessLow
    )
)
```

## 性能优化

1. **重组优化**：使用稳定的参数避免不必要的重组
2. **状态提升**：合理的状态管理避免过度重组
3. **懒加载**：大内容使用LazyColumn等懒加载组件
4. **内存管理**：及时清理不需要的资源

这个Compose版本提供了与传统View版本相同的功能，但采用了更现代化的架构和更好的用户体验。
