package com.work2502.zoomtext.view

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动ZoomTextActivity的辅助类
 */
object ZoomTextLauncher {

    /**
     * 创建一个启动ZoomTextActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): But<PERSON> {
        val button = Button(context)
        button.text = "启动TextView缩放示例"
        button.setOnClickListener {
            val intent = Intent(context, ZoomTextActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动ZoomTextActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
        val intent = Intent(activity, ScalableScrollViewExample::class.java)
        activity.startActivity(intent)
    }
}
