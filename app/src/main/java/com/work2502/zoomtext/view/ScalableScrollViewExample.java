package com.work2502.zoomtext.view;

import android.app.Activity;
import android.os.Bundle;
import android.text.InputType;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Button;

/**
 * ScalableScrollView使用示例
 * 展示如何正确使用改进后的ScalableScrollView来解决缩放后光标位置问题
 */
public class ScalableScrollViewExample extends Activity {

    private ScalableScrollView scalableScrollView;
    private EditText editText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 创建主布局
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setLayoutParams(new ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ));

        // 创建控制按钮布局
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setLayoutParams(new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ));

        // 缩小按钮
        Button zoomOutButton = new Button(this);
        zoomOutButton.setText("缩小");
        zoomOutButton.setOnClickListener(v -> {
            float currentScale = scalableScrollView.getScaleFactor();
            scalableScrollView.setScaleFactor(currentScale * 0.9f);
        });

        // 重置按钮
        Button resetButton = new Button(this);
        resetButton.setText("重置");
        resetButton.setOnClickListener(v -> scalableScrollView.resetView());

        // 放大按钮
        Button zoomInButton = new Button(this);
        zoomInButton.setText("放大");
        zoomInButton.setOnClickListener(v -> {
            float currentScale = scalableScrollView.getScaleFactor();
            scalableScrollView.setScaleFactor(currentScale * 1.1f);
        });

        // 刷新光标按钮
        Button refreshCursorButton = new Button(this);
        refreshCursorButton.setText("刷新光标");
        refreshCursorButton.setOnClickListener(v -> scalableScrollView.refreshCursor());

        buttonLayout.addView(zoomOutButton);
        buttonLayout.addView(resetButton);
        buttonLayout.addView(zoomInButton);
        buttonLayout.addView(refreshCursorButton);

        // 创建第二行按钮布局（宽度控制）
        LinearLayout widthButtonLayout = new LinearLayout(this);
        widthButtonLayout.setOrientation(LinearLayout.HORIZONTAL);
        widthButtonLayout.setLayoutParams(new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ));

        // 全屏宽度按钮
        Button fullWidthButton = new Button(this);
        fullWidthButton.setText("全屏宽度");
        fullWidthButton.setOnClickListener(v -> {
            float density = getResources().getDisplayMetrics().density;
            int height = (int) (2000 * density);
            scalableScrollView.setEditTextFullWidth(editText, height);
        });

        // 90%宽度按钮
        Button width90Button = new Button(this);
        width90Button.setText("90%宽度");
        width90Button.setOnClickListener(v -> {
            float density = getResources().getDisplayMetrics().density;
            int height = (int) (2000 * density);
            scalableScrollView.setEditTextWithWidthPercent(editText, 0.9f, height);
        });

        // 70%宽度按钮
        Button width70Button = new Button(this);
        width70Button.setText("70%宽度");
        width70Button.setOnClickListener(v -> {
            float density = getResources().getDisplayMetrics().density;
            int height = (int) (2000 * density);
            scalableScrollView.setEditTextWithWidthPercent(editText, 0.7f, height);
        });

        // 显示信息按钮
        Button infoButton = new Button(this);
        infoButton.setText("显示信息");
        infoButton.setOnClickListener(v -> {
            int screenWidth = scalableScrollView.getScreenWidth();
            int editTextWidth = scalableScrollView.getEditTextWidth();
            int editTextHeight = scalableScrollView.getEditTextHeight();

            String info = String.format("屏幕宽度: %dpx\nEditText宽度: %dpx\nEditText高度: %dpx\n宽度比例: %.1f%%",
                screenWidth, editTextWidth, editTextHeight, (float)editTextWidth / screenWidth * 100);

            android.widget.Toast.makeText(this, info, android.widget.Toast.LENGTH_LONG).show();
        });

        widthButtonLayout.addView(fullWidthButton);
        widthButtonLayout.addView(width90Button);
        widthButtonLayout.addView(width70Button);
        widthButtonLayout.addView(infoButton);

        // 创建ScalableScrollView
        scalableScrollView = new ScalableScrollView(this);
        scalableScrollView.setLayoutParams(new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            0,
            1.0f // weight
        ));

        // 创建EditText
        editText = new EditText(this);
        editText.setText(getExampleText());
        editText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_FLAG_MULTI_LINE);
        editText.setBackgroundColor(0xFFF5F5F5);
//        editText.setPadding(32, 32, 32, 32);
        editText.setTextSize(16f);
        editText.setTextColor(0xFF000000);
        editText.setHint("在这里输入文本...");

        // 设置EditText到ScalableScrollView中
        // 方法1：使用默认方式（自动限制宽度不超过屏幕宽度）
        float density = getResources().getDisplayMetrics().density;
        int editTextHeight = (int) (2000 * density); // dp转换为px

        // 这里尝试设置一个很大的宽度，但会被自动限制为屏幕宽度
        int editTextWidth = (int) (1200 * density); // 这个宽度可能超过屏幕宽度
        scalableScrollView.setEditText(editText, editTextWidth, editTextHeight);

        // 方法2：使用全屏宽度（注释掉的替代方案）
        // scalableScrollView.setEditTextFullWidth(editText, editTextHeight);

        // 方法3：使用屏幕宽度的90%（注释掉的替代方案）
        // scalableScrollView.setEditTextWithWidthPercent(editText, 0.9f, editTextHeight);

        // 添加到主布局
        mainLayout.addView(buttonLayout);
        mainLayout.addView(widthButtonLayout);
        mainLayout.addView(scalableScrollView);

        setContentView(mainLayout);
    }

    private String getExampleText() {
        return "这是一个ScalableScrollView的示例！\n\n" +
               "改进后的功能：\n" +
               "• 默认缩放设置为0.8倍\n" +
               "• 缩放以中心点为基准\n" +
               "• 缩放后自动修正光标位置\n" +
               "• 支持手势缩放和按钮缩放\n" +
               "• 优化了触摸事件处理\n\n" +
               "使用方法：\n" +
               "1. 双指捏合进行缩放\n" +
               "2. 单指拖拽进行滚动\n" +
               "3. 双击快速缩放\n" +
               "4. 单击设置光标位置\n" +
               "5. 使用顶部按钮进行精确控制\n\n" +
               "光标位置修正：\n" +
               "• 缩放结束后自动刷新光标\n" +
               "• 点击时精确计算光标位置\n" +
               "• 考虑缩放和平移变换\n" +
               "• 异常处理确保稳定性\n\n" +
               "请尝试以下操作：\n" +
               "1. 先点击文本设置光标位置\n" +
               "2. 然后进行缩放操作\n" +
               "3. 观察光标位置是否正确\n" +
               "4. 如果有问题，点击'刷新光标'按钮\n\n" +
               "这个示例展示了如何解决缩放环境下EditText光标位置不正确的问题。\n\n" +
               "您可以继续在这里输入更多文本来测试功能...\n\n" +
               "支持的功能包括：\n" +
               "• 多行文本编辑\n" +
               "• 文本选择\n" +
               "• 复制粘贴\n" +
               "• 撤销重做\n" +
               "• 输入法支持\n" +
               "• 长按菜单\n\n" +
               "这是一个很长的文档，用于测试滚动功能。您可以滚动到任何位置，然后进行缩放操作，光标位置应该保持正确。\n\n" +
               "继续添加更多内容...\n\n" +
               "第一段：这里是第一段内容，用于测试文本编辑功能。\n\n" +
               "第二段：这里是第二段内容，包含更多的文字来测试滚动和缩放。\n\n" +
               "第三段：这里是第三段内容，继续测试各种功能的组合使用。\n\n" +
               "第四段：这里是第四段内容，验证在不同位置的光标设置是否正确。\n\n" +
               "第五段：这里是第五段内容，测试长文档的处理能力。\n\n" +
               "结束：感谢您测试这个改进后的ScalableScrollView组件！";
    }
}
