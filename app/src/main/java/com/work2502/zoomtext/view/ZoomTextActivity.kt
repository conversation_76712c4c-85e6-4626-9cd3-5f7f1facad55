package com.work2502.zoomtext.view

import android.os.Bundle
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.work2502.compose.R

/**
 * 演示TextView缩放功能的Activity
 */
class ZoomTextActivity : AppCompatActivity() {

    private lateinit var zoomableTextView: ZoomableTextView
    private lateinit var resetButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_zoom_text)

        // 初始化视图
        zoomableTextView = findViewById(R.id.zoomableTextView)
        resetButton = findViewById(R.id.resetButton)

        // 设置文本内容
        setupTextContent()

        // 设置文本大小范围
        zoomableTextView.setTextSizeRange(8f, 72f)

        // 设置重置按钮点击事件
        resetButton.setOnClickListener {
            zoomableTextView.resetTextSize()
            Toast.makeText(this, "文本大小已重置", Toast.LENGTH_SHORT).show()
        }

        // 显示使用提示
        Toast.makeText(this, "使用双指捏合手势可以放大缩小文本", Toast.LENGTH_LONG).show()
    }

    /**
     * 设置文本内容
     */
    private fun setupTextContent() {
        val sampleText = """
            这是一个可缩放的TextView示例。
            
            您可以使用双指捏合手势来放大或缩小此文本。
            
            放大缩小功能在阅读长文本时非常有用，可以帮助用户更好地查看内容细节。
            
            这个功能特别适合：
            - 阅读小字体文本
            - 查看详细信息
            - 帮助视力不佳的用户
            - 提高阅读体验
            
            您还可以点击下方的"重置大小"按钮，将文本大小恢复到默认值。
            
            尝试一下吧！
        """.trimIndent()

        zoomableTextView.text = sampleText
    }
}
