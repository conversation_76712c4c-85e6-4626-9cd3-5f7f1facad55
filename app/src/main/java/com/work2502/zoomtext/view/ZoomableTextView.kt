package com.work2502.zoomtext.view

import android.R
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import androidx.appcompat.widget.AppCompatTextView

/**
 * 可缩放的TextView
 * 支持通过捏合手势放大缩小文本
 */
class ZoomableTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = R.attr.textViewStyle
) : AppCompatTextView(context, attrs, defStyleAttr) {

    // 缩放检测器
    private val scaleGestureDetector: ScaleGestureDetector

    // 最小和最大文本大小
    private var minTextSize = 8f
    private var maxTextSize = 72f

    // 初始文本大小
    private var defaultTextSize = textSize / resources.displayMetrics.scaledDensity

    // 当前缩放比例
    private var scaleFactor = 1.0f

    init {
        // 初始化缩放检测器
        scaleGestureDetector = ScaleGestureDetector(context, ScaleListener())
    }

    /**
     * 设置文本大小范围
     * @param minSize 最小文本大小（SP）
     * @param maxSize 最大文本大小（SP）
     */
    fun setTextSizeRange(minSize: Float, maxSize: Float) {
        minTextSize = minSize
        maxTextSize = maxSize
    }

    /**
     * 重置文本大小到默认值
     */
    fun resetTextSize() {
        scaleFactor = 1.0f
        setTextSize(defaultTextSize)
    }

    /**
     * 处理触摸事件
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 将事件传递给缩放检测器
        scaleGestureDetector.onTouchEvent(event)
        return true
    }

    /**
     * 缩放监听器
     */
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            // 计算新的缩放比例
            scaleFactor *= detector.scaleFactor

            // 限制缩放比例在合理范围内
            scaleFactor = scaleFactor.coerceIn(
                minTextSize / defaultTextSize,
                maxTextSize / defaultTextSize
            )

            // 应用新的文本大小
            val newSize = defaultTextSize * scaleFactor
            textSize = newSize

            return true
        }
    }

    /**
     * 设置文本大小（SP）
     */
    override fun setTextSize(size: Float) {
        super.setTextSize(size)
    }
}