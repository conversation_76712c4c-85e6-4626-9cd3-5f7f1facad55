package com.work2502.zoomtext.view;

import android.content.Context;
import android.graphics.PointF;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.ViewConfiguration;
import android.widget.EditText;
import android.widget.FrameLayout;

/**
 * 支持缩放和滚动的自定义ViewGroup，专门为放置单个高度很大的EditText优化
 */
public class ScalableScrollView extends FrameLayout {

    private static final float MIN_SCALE = 0.8f;
    private static final float MAX_SCALE = 1.3f;
    private static final float DEFAULT_SCALE = 1f;

    private ScaleGestureDetector scaleDetector;
    private GestureDetector gestureDetector;

    private float scaleFactor = DEFAULT_SCALE;
    private float translateX = 0f;
    private float translateY = 0f;

    private PointF lastTouchPoint = new PointF();
    private boolean isScaling = false;
    private boolean isDragging = false;

    // EditText相关
    private EditText editText;
    private int editTextWidth = 0;
    private int editTextHeight = 0;

    // 触摸相关
    private int touchSlop;
    private float totalDx = 0f;
    private float totalDy = 0f;

    public ScalableScrollView(Context context) {
        super(context);
        init();
    }

    public ScalableScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ScalableScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        scaleDetector = new ScaleGestureDetector(getContext(), new ScaleListener());
        gestureDetector = new GestureDetector(getContext(), new GestureListener());

        ViewConfiguration vc = ViewConfiguration.get(getContext());
        touchSlop = vc.getScaledTouchSlop();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (editText != null) {
            centerEditText();
        }
    }

    /**
     * 设置EditText并添加到容器中
     * 宽度会自动限制为不超过屏幕宽度
     */
    public void setEditText(EditText editText, int width, int height) {
        setEditText(editText, width, height, true);
    }

    /**
     * 设置EditText并添加到容器中
     * @param editText EditText实例
     * @param width 期望的宽度
     * @param height 期望的高度
     * @param constrainWidth 是否限制宽度不超过屏幕宽度
     */
    public void setEditText(EditText editText, int width, int height, boolean constrainWidth) {
        // 移除之前的EditText
        if (this.editText != null) {
            removeView(this.editText);
        }

        int finalWidth = width;
        if (constrainWidth) {
            // 限制宽度不能超过屏幕宽度
            int screenWidth = getResources().getDisplayMetrics().widthPixels;
            finalWidth = Math.min(width, screenWidth);
        }

        this.editText = editText;
        this.editTextWidth = finalWidth;
        this.editTextHeight = height;

        // 设置EditText属性，禁用内部滚动
        editText.setVerticalScrollBarEnabled(false);
        editText.setHorizontalScrollBarEnabled(false);
        editText.setOverScrollMode(OVER_SCROLL_NEVER);

        // 添加到容器，使用最终的宽度
        LayoutParams params = new LayoutParams(finalWidth, height);
        addView(editText, params);

        // 初始居中显示
        centerEditText();
    }

    /**
     * 让EditText居中显示
     */
    private void centerEditText() {
        if (editText == null || getWidth() == 0 || getHeight() == 0) {
            return;
        }

        float viewWidth = getWidth();
        float viewHeight = getHeight();
        float scaledWidth = editTextWidth * scaleFactor;
        float scaledHeight = editTextHeight * scaleFactor;

        // 水平居中
        translateX = (viewWidth - scaledWidth) / 2f;

        // 垂直位置：如果内容比屏幕小则居中，否则从顶部开始
        if (scaledHeight <= viewHeight) {
            translateY = (viewHeight - scaledHeight) / 2f;
        } else {
            translateY = 0f;
        }

        updateTransform();
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (editText == null) {
            return super.onInterceptTouchEvent(ev);
        }

        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                lastTouchPoint.set(ev.getX(), ev.getY());
                totalDx = 0f;
                totalDy = 0f;
                isDragging = false;

                // 检查是否在EditText区域内
                if (isTouchInEditText(ev.getX(), ev.getY())) {
                    // 在EditText内，先不拦截
                    return false;
                }
                break;

            case MotionEvent.ACTION_MOVE:
                if (ev.getPointerCount() > 1) {
                    // 多点触摸，拦截用于缩放
                    return true;
                }

                float dx = ev.getX() - lastTouchPoint.x;
                float dy = ev.getY() - lastTouchPoint.y;
                totalDx += Math.abs(dx);
                totalDy += Math.abs(dy);

                // 如果移动距离超过阈值，拦截事件
                if (totalDx > touchSlop || totalDy > touchSlop) {
                    // 如果主要是垂直移动且内容可以滚动，拦截事件
                    if (canScroll()) {
                        return true;
                    }
                }
                break;
        }

        return super.onInterceptTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (editText == null) {
            return super.onTouchEvent(event);
        }

        boolean scaleHandled = scaleDetector.onTouchEvent(event);
        boolean gestureHandled = gestureDetector.onTouchEvent(event);

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                lastTouchPoint.set(event.getX(), event.getY());
                isDragging = false;
                return true;

            case MotionEvent.ACTION_MOVE:
                if (!isScaling && event.getPointerCount() == 1) {
                    float dx = event.getX() - lastTouchPoint.x;
                    float dy = event.getY() - lastTouchPoint.y;

                    if (!isDragging) {
                        // 检查是否开始拖拽
                        if (Math.abs(dx) > touchSlop || Math.abs(dy) > touchSlop) {
                            isDragging = true;
                        }
                    }

                    if (isDragging) {
                        // 应用移动
                        translateX += dx;
                        translateY += dy;

                        // 约束边界
                        constrainTranslation();
                        updateTransform();

                        lastTouchPoint.set(event.getX(), event.getY());
                    }
                }
                return true;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                isDragging = false;
                isScaling = false;
                break;
        }

        return scaleHandled || gestureHandled || true;
    }

    /**
     * 检查内容是否可以滚动
     */
    private boolean canScroll() {
        if (editText == null) return false;

        float scaledHeight = editTextHeight * scaleFactor;
        float viewHeight = getHeight();

        return scaledHeight > viewHeight;
    }

    /**
     * 检查触摸点是否在EditText区域内
     */
    private boolean isTouchInEditText(float x, float y) {
        if (editText == null) {
            return false;
        }

        float left = translateX;
        float top = translateY;
        float right = left + editTextWidth * scaleFactor;
        float bottom = top + editTextHeight * scaleFactor;

        return x >= left && x <= right && y >= top && y <= bottom;
    }

    private class ScaleListener extends ScaleGestureDetector.SimpleOnScaleGestureListener {
        @Override
        public boolean onScaleBegin(ScaleGestureDetector detector) {
            isScaling = true;
            return true;
        }

        @Override
        public boolean onScale(ScaleGestureDetector detector) {
            float prevScale = scaleFactor;
            scaleFactor *= detector.getScaleFactor();
            scaleFactor = Math.max(MIN_SCALE, Math.min(scaleFactor, MAX_SCALE));

            if (scaleFactor != prevScale) {
                // 以手势中心点为基准进行缩放
                float focusX = detector.getFocusX();
                float focusY = detector.getFocusY();

                // 计算缩放后的平移调整
                float scaleChange = scaleFactor / prevScale;
                translateX = focusX + (translateX - focusX) * scaleChange;
                translateY = focusY + (translateY - focusY) * scaleChange;

                constrainTranslation();
                updateTransform();
            }
            return true;
        }

        @Override
        public void onScaleEnd(ScaleGestureDetector detector) {
            isScaling = false;
            // 缩放结束后刷新光标位置
            refreshCursor();
        }
    }

    private class GestureListener extends GestureDetector.SimpleOnGestureListener {
        @Override
        public boolean onDoubleTap(MotionEvent e) {
            // 双击切换缩放级别
            float targetScale = scaleFactor < 1.5f ? 2.0f : 1.0f;
            animateScale(targetScale, e.getX(), e.getY());
            return true;
        }

        @Override
        public boolean onSingleTapUp(MotionEvent e) {
            // 单击时让EditText获得焦点
            if (isTouchInEditText(e.getX(), e.getY()) && editText != null) {
                // 先让EditText获得焦点
                editText.requestFocus();

                // 延迟设置光标位置，确保焦点已经设置完成
                editText.post(new Runnable() {
                    @Override
                    public void run() {
                        setCursorPosition(e.getX(), e.getY());
                    }
                });

                return true;
            }
            return false;
        }

        @Override
        public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
            if (!isScaling) {
                translateX -= distanceX;
                translateY -= distanceY;
                constrainTranslation();
                updateTransform();
                return true;
            }
            return false;
        }
    }

    private void animateScale(float targetScale, float focusX, float focusY) {
        float prevScale = scaleFactor;
        scaleFactor = targetScale;

        float scaleChange = scaleFactor / prevScale;
        translateX = focusX + (translateX - focusX) * scaleChange;
        translateY = focusY + (translateY - focusY) * scaleChange;

        constrainTranslation();
        updateTransform();

        // 缩放动画完成后刷新光标位置
        refreshCursor();
    }

    /**
     * 约束平移，确保EditText不会完全移出屏幕
     */
    private void constrainTranslation() {
        if (editText == null) {
            return;
        }

        float scaledWidth = editTextWidth * scaleFactor;
        float scaledHeight = editTextHeight * scaleFactor;
        float viewWidth = getWidth();
        float viewHeight = getHeight();

        // 水平边界约束
        if (scaledWidth <= viewWidth) {
            // 如果内容宽度小于视图宽度，保持居中
            translateX = (viewWidth - scaledWidth) / 2f;
        } else {
            // 如果内容宽度大于视图宽度，限制边界
            translateX = Math.min(0, translateX);
            translateX = Math.max(viewWidth - scaledWidth, translateX);
        }

        // 垂直边界约束
        if (scaledHeight <= viewHeight) {
            // 如果内容高度小于视图高度，保持居中
            translateY = (viewHeight - scaledHeight) / 2f;
        } else {
            // 如果内容高度大于视图高度，允许滚动但限制边界
            translateY = Math.min(0, translateY);
            translateY = Math.max(viewHeight - scaledHeight, translateY);
        }
    }

    /**
     * 更新变换
     */
    private void updateTransform() {
        if (editText == null) {
            return;
        }

        // 设置EditText的位置和缩放
        editText.setTranslationX(translateX);
        editText.setTranslationY(translateY);
        editText.setScaleX(scaleFactor);
        editText.setScaleY(scaleFactor);

        // 确保重绘
        invalidate();
    }

    /**
     * 获取当前缩放比例
     */
    public float getScaleFactor() {
        return scaleFactor;
    }

    /**
     * 设置缩放比例
     */
    public void setScaleFactor(float scale) {
        scaleFactor = Math.max(MIN_SCALE, Math.min(scale, MAX_SCALE));
        centerEditText();
        // 设置缩放后刷新光标位置
        refreshCursor();
    }

    /**
     * 滚动到顶部
     */
    public void scrollToTop() {
        if (editText != null) {
            translateY = 0f;
            constrainTranslation();
            updateTransform();
        }
    }

    /**
     * 滚动到底部
     */
    public void scrollToBottom() {
        if (editText != null) {
            float scaledHeight = editTextHeight * scaleFactor;
            float viewHeight = getHeight();

            if (scaledHeight > viewHeight) {
                translateY = viewHeight - scaledHeight;
                constrainTranslation();
                updateTransform();
            }
        }
    }

    /**
     * 设置光标位置
     */
    private void setCursorPosition(float touchX, float touchY) {
        if (editText == null) {
            return;
        }

        try {
            // 计算在EditText内的相对位置
            // 考虑缩放和平移变换
            float relativeX = (touchX - translateX) / scaleFactor;
            float relativeY = (touchY - translateY) / scaleFactor;

            // 确保坐标在EditText范围内
            relativeX = Math.max(0, Math.min(relativeX, editTextWidth));
            relativeY = Math.max(0, Math.min(relativeY, editTextHeight));

            // 获取光标位置
            int offset = editText.getOffsetForPosition(relativeX, relativeY);

            // 设置光标位置
            editText.setSelection(offset);

            // 强制刷新光标显示
            editText.invalidate();

        } catch (Exception ex) {
            // 如果出现异常，尝试简单的焦点处理
            try {
                editText.clearFocus();
                editText.requestFocus();
            } catch (Exception e) {
                // 忽略异常
            }
        }
    }

    /**
     * 刷新EditText的光标位置
     * 在缩放变化后调用此方法来修正光标位置
     */
    public void refreshCursor() {
        if (editText != null && editText.hasFocus()) {
            editText.post(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 保存当前选择位置
                        int selectionStart = editText.getSelectionStart();
                        int selectionEnd = editText.getSelectionEnd();

                        // 临时失去焦点
                        editText.clearFocus();

                        // 重新获得焦点
                        editText.requestFocus();

                        // 恢复选择位置
                        if (selectionStart >= 0 && selectionEnd >= 0) {
                            editText.setSelection(selectionStart, selectionEnd);
                        }

                        // 强制重绘
                        editText.invalidate();

                    } catch (Exception e) {
                        // 忽略异常
                    }
                }
            });
        }
    }

    /**
     * 设置EditText为全屏宽度
     */
    public void setEditTextFullWidth(EditText editText, int height) {
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        setEditText(editText, screenWidth, height, false);
    }

    /**
     * 设置EditText为指定百分比的屏幕宽度
     * @param editText EditText实例
     * @param widthPercent 宽度百分比 (0.0 - 1.0)
     * @param height 高度
     */
    public void setEditTextWithWidthPercent(EditText editText, float widthPercent, int height) {
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        int width = (int) (screenWidth * Math.max(0.1f, Math.min(1.0f, widthPercent)));
        setEditText(editText, width, height, false);
    }

    /**
     * 获取当前EditText的实际宽度
     */
    public int getEditTextWidth() {
        return editTextWidth;
    }

    /**
     * 获取当前EditText的实际高度
     */
    public int getEditTextHeight() {
        return editTextHeight;
    }

    /**
     * 获取屏幕宽度
     */
    public int getScreenWidth() {
        return getResources().getDisplayMetrics().widthPixels;
    }

    /**
     * 获取屏幕高度
     */
    public int getScreenHeight() {
        return getResources().getDisplayMetrics().heightPixels;
    }

    /**
     * 重置视图到初始状态
     */
    public void resetView() {
        scaleFactor = DEFAULT_SCALE;
        centerEditText();
    }
}