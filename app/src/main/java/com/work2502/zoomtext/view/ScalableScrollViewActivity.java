package com.work2502.zoomtext.view;

import android.app.Activity;
import android.graphics.Color;
import android.os.Bundle;
import android.text.InputType;
import android.view.Gravity;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Button;

public class ScalableScrollViewActivity extends Activity {

    private ScalableScrollView scalableView;
    private EditText editText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 创建主布局
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
//        mainLayout.setBackgroundColor(Color.LIGHT_GRAY);

        // 创建工具栏
        createToolbar(mainLayout);

        // 创建ScalableScrollView
        scalableView = new ScalableScrollView(this);
        scalableView.setBackgroundColor(Color.WHITE);

        // 创建一个很高的EditText
        createLargeEditText();

        // 添加到主布局
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0, 1.0f
        );
        mainLayout.addView(scalableView, params);

        setContentView(mainLayout);
    }

    private void createToolbar(LinearLayout parent) {
        LinearLayout toolbar = new LinearLayout(this);
        toolbar.setOrientation(LinearLayout.HORIZONTAL);
        toolbar.setGravity(Gravity.CENTER);
        toolbar.setPadding(16, 16, 16, 16);
        toolbar.setBackgroundColor(Color.DKGRAY);

        // 缩小按钮
        Button zoomOutBtn = new Button(this);
        zoomOutBtn.setText("缩小");
        zoomOutBtn.setOnClickListener(v -> {
            float currentScale = scalableView.getScaleFactor();
            scalableView.setScaleFactor(currentScale * 0.8f);
        });

        // 重置按钮
        Button resetBtn = new Button(this);
        resetBtn.setText("重置");
        resetBtn.setOnClickListener(v -> scalableView.resetView());

        // 放大按钮
        Button zoomInBtn = new Button(this);
        zoomInBtn.setText("放大");
        zoomInBtn.setOnClickListener(v -> {
            float currentScale = scalableView.getScaleFactor();
            scalableView.setScaleFactor(currentScale * 1.25f);
        });

        // 到顶部按钮
        Button topBtn = new Button(this);
        topBtn.setText("顶部");
        topBtn.setOnClickListener(v -> scalableView.scrollToTop());

        // 到底部按钮
        Button bottomBtn = new Button(this);
        bottomBtn.setText("底部");
        bottomBtn.setOnClickListener(v -> scalableView.scrollToBottom());

        toolbar.addView(zoomOutBtn);
        toolbar.addView(resetBtn);
        toolbar.addView(zoomInBtn);
        toolbar.addView(topBtn);
        toolbar.addView(bottomBtn);

        parent.addView(toolbar);
    }

    private void createLargeEditText() {
        editText = new EditText(this);

        // 设置EditText属性
        editText.setHint("这是一个很高的文本编辑框，支持缩放和滚动...\n\n" +
                "你可以：\n" +
                "• 双指缩放\n" +
                "• 单指拖拽滚动\n" +
                "• 双击快速缩放\n" +
                "• 单击获得焦点并编辑\n\n" +
                "继续输入更多内容来测试滚动功能...");

        editText.setGravity(Gravity.TOP | Gravity.START);
        editText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_FLAG_MULTI_LINE);
        editText.setVerticalScrollBarEnabled(true); // 禁用内部滚动
        editText.setHorizontalScrollBarEnabled(false);
        editText.setPadding(20, 20, 20, 20);
        editText.setTextSize(16);
        editText.setLineSpacing(4, 1.2f);
        editText.setBackgroundColor(Color.WHITE);

        // 设置EditText尺寸：宽度适中，高度很大
        int width = 600;   // dp转px需要根据设备密度计算，这里简化处理
        int height = 2000; // 很高的高度，超过屏幕

        // 添加到ScalableScrollView
        scalableView.setEditText(editText, width, height);
    }
}