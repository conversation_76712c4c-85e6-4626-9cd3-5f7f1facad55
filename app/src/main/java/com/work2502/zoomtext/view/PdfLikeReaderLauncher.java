package com.work2502.zoomtext.view;

import android.content.Context;
import android.content.Intent;
import android.widget.Button;
import androidx.appcompat.app.AppCompatActivity;

/**
 * 用于启动PdfLikeReaderActivity的辅助类
 */
public class PdfLikeReaderLauncher {

    /**
     * 创建一个启动PdfLikeReaderActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    public static Button createLauncherButton(Context context) {
        Button button = new Button(context);
        button.setText("启动PDF阅读器示例");
        button.setOnClickListener(v -> {
            Intent intent = new Intent(context, PdfLikeReaderActivity.class);
            context.startActivity(intent);
        });
        return button;
    }

    /**
     * 直接启动PdfLikeReaderActivity
     * @param activity 当前Activity
     */
    public static void launch(AppCompatActivity activity) {
        Intent intent = new Intent(activity, PdfLikeReaderActivity.class);
        activity.startActivity(intent);
    }
}
