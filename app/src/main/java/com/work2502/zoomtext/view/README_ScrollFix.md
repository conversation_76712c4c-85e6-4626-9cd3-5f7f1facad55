# PdfLikeReaderView 滑动抖动修复说明

## 🔍 问题描述
用户在上下滑动时出现左右抖动问题，影响用户体验。

## 🛠️ 修复方案

### 1. 滑动方向检测
添加了智能的滑动方向检测机制：
- `SCROLL_DIRECTION_HORIZONTAL` - 水平滑动
- `SCROLL_DIRECTION_VERTICAL` - 垂直滑动
- `SCROLL_DIRECTION_NONE` - 无滑动

### 2. 方向判断逻辑
```java
if (Math.abs(totalDx) > Math.abs(totalDy) * 1.5f) {
    // 主要是水平滑动
    scrollDirection = SCROLL_DIRECTION_HORIZONTAL;
} else if (Math.abs(totalDy) > Math.abs(totalDx) * 1.5f) {
    // 主要是垂直滑动
    scrollDirection = SCROLL_DIRECTION_VERTICAL;
} else {
    // 对角线滑动，允许两个方向
    scrollDirection = SCROLL_DIRECTION_HORIZONTAL | SCROLL_DIRECTION_VERTICAL;
}
```

### 3. 滑动限制
根据检测到的主要滑动方向，限制不必要的移动：
- 垂直滑动时，减少水平方向的移动
- 水平滑动时，减少垂直方向的移动

### 4. 惯性滚动优化
```java
if (scrollDirection == SCROLL_DIRECTION_HORIZONTAL) {
    // 主要是水平滑动，减少垂直惯性
    finalVelocityY *= 0.1f;
} else if (scrollDirection == SCROLL_DIRECTION_VERTICAL) {
    // 主要是垂直滑动，减少水平惯性
    finalVelocityX *= 0.1f;
}
```

### 5. 边界约束改进
添加容差值，避免浮点数精度导致的边界抖动：
```java
float tolerance = 0.5f;
if (translateX < minX - tolerance) {
    translateX = minX;
} else if (translateX > maxX + tolerance) {
    translateX = maxX;
}
```

## ✅ 修复效果

### 解决的问题：
1. **上下滑动时的左右抖动** - 通过方向检测和移动限制解决
2. **惯性滚动的方向偏移** - 通过速度调整解决
3. **边界处的微小抖动** - 通过容差值解决

### 保留的功能：
1. **对角线滑动** - 仍然支持同时在两个方向滑动
2. **缩放功能** - 不影响双指缩放
3. **双击缩放** - 保持原有功能
4. **边界约束** - 改进但保持约束功能

## 🎯 使用建议

### 测试方法：
1. **垂直滑动测试**：
   - 尽量垂直地上下滑动
   - 观察是否还有左右抖动

2. **水平滑动测试**：
   - 尽量水平地左右滑动
   - 观察是否还有上下抖动

3. **对角线滑动测试**：
   - 故意进行对角线滑动
   - 确认两个方向都能正常移动

4. **惯性滚动测试**：
   - 快速滑动后观察惯性效果
   - 确认惯性方向与滑动方向一致

### 参数调整：
如果需要调整敏感度，可以修改以下参数：

1. **方向判断比例**：
   ```java
   // 当前是1.5倍，可以调整为1.2-2.0之间
   Math.abs(totalDx) > Math.abs(totalDy) * 1.5f
   ```

2. **惯性衰减比例**：
   ```java
   // 当前是0.1倍，可以调整为0.05-0.3之间
   finalVelocityY *= 0.1f;
   ```

3. **边界容差**：
   ```java
   // 当前是0.5像素，可以调整为0.1-2.0之间
   float tolerance = 0.5f;
   ```

## 🔧 技术细节

### 核心改进：
1. **智能方向检测** - 基于移动距离比例判断主要方向
2. **选择性移动** - 根据方向只在允许的轴上移动
3. **惯性方向控制** - 限制非主要方向的惯性速度
4. **精确边界处理** - 使用容差避免浮点数精度问题

### 兼容性：
- 保持所有原有API不变
- 不影响现有的缩放功能
- 向后兼容所有使用场景

这个修复方案应该能够显著减少滑动时的抖动问题，提供更流畅的用户体验。
