package com.work2502.zoomtext.view;

import android.app.Activity;
import android.graphics.Color;
import android.os.Bundle;
import android.text.InputType;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

/**
 * 演示PdfLikeReaderView功能的Activity
 * 展示类似PDF阅读器的缩放和滚动功能
 */
public class PdfLikeReaderActivity extends Activity {

    private PdfLikeReaderView pdfReaderView;
    private LinearLayout contentLayout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 创建主布局
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setBackgroundColor(Color.LTGRAY);

        // 创建工具栏
        createToolbar(mainLayout);

        // 创建PdfLikeReaderView
        pdfReaderView = new PdfLikeReaderView(this);
        pdfReaderView.setBackgroundColor(Color.WHITE);

        // 创建示例内容
        createSampleContent();

        // 添加到主布局
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
        );
        mainLayout.addView(pdfReaderView, params);

        setContentView(mainLayout);

        // 显示使用提示
        Toast.makeText(this, "支持双指缩放、拖拽滚动、双击快速缩放", Toast.LENGTH_LONG).show();
    }

    /**
     * 创建工具栏
     */
    private void createToolbar(LinearLayout parent) {
        LinearLayout toolbar = new LinearLayout(this);
        toolbar.setOrientation(LinearLayout.HORIZONTAL);
        toolbar.setBackgroundColor(Color.DKGRAY);
        toolbar.setPadding(16, 16, 16, 16);

        // 缩小按钮
        Button zoomOutBtn = new Button(this);
        zoomOutBtn.setText("缩小");
        zoomOutBtn.setOnClickListener(v -> {
            float currentScale = pdfReaderView.getScaleFactor();
            pdfReaderView.setScaleFactor(currentScale * 0.8f);
        });

        // 重置按钮
        Button resetBtn = new Button(this);
        resetBtn.setText("重置");
        resetBtn.setOnClickListener(v -> pdfReaderView.reset());

        // 放大按钮
        Button zoomInBtn = new Button(this);
        zoomInBtn.setText("放大");
        zoomInBtn.setOnClickListener(v -> {
            float currentScale = pdfReaderView.getScaleFactor();
            pdfReaderView.setScaleFactor(currentScale * 1.25f);
        });

        // 到顶部按钮
        Button topBtn = new Button(this);
        topBtn.setText("顶部");
        topBtn.setOnClickListener(v -> pdfReaderView.scrollToTop());

        // 到底部按钮
        Button bottomBtn = new Button(this);
        bottomBtn.setText("底部");
        bottomBtn.setOnClickListener(v -> pdfReaderView.scrollToBottom());

        // 切换内容按钮
        Button switchContentBtn = new Button(this);
        switchContentBtn.setText("切换内容");
        switchContentBtn.setOnClickListener(v -> switchContent());

        toolbar.addView(zoomOutBtn);
        toolbar.addView(resetBtn);
        toolbar.addView(zoomInBtn);
        toolbar.addView(topBtn);
        toolbar.addView(bottomBtn);
        toolbar.addView(switchContentBtn);

        parent.addView(toolbar);
    }

    /**
     * 创建示例内容
     */
    private void createSampleContent() {
        // 创建一个包含多种内容的布局
        contentLayout = new LinearLayout(this);
        contentLayout.setOrientation(LinearLayout.VERTICAL);
        contentLayout.setPadding(40, 40, 40, 40);
        contentLayout.setBackgroundColor(Color.WHITE);

        // 添加标题
        TextView titleView = new TextView(this);
        titleView.setText("PDF阅读器示例文档");
        titleView.setTextSize(24);
        titleView.setTextColor(Color.BLACK);
        titleView.setGravity(Gravity.CENTER);
        titleView.setPadding(0, 0, 0, 40);
//        contentLayout.addView(titleView);

        // 添加段落文本
        addParagraph("这是一个演示PDF阅读器功能的示例文档。您可以使用以下手势来操作：");
        addParagraph("• 双指捏合：缩放内容");
        addParagraph("• 单指拖拽：滚动浏览");
        addParagraph("• 双击：快速缩放");
        addParagraph("• 工具栏按钮：精确控制");

        // 添加分隔线
        addDivider();

        // 添加更多内容
        addParagraph("功能特点：");
        addParagraph("1. 平滑的缩放动画");
        addParagraph("2. 惯性滚动支持");
        addParagraph("3. 边界约束");
        addParagraph("4. 多点触控手势");
        addParagraph("5. 双击快速缩放");

        addDivider();

        // 添加长文本段落
        addParagraph("这是一个很长的段落，用来测试滚动功能。" +
                "当内容超出屏幕范围时，您可以通过拖拽手势来滚动查看。" +
                "滚动支持惯性效果，让操作更加自然流畅。" +
                "同时，边界约束确保内容不会滚动到不合理的位置。".repeat(10));

        // 创建自定义EditText
        EditText editText = new PdfLikeReaderView.NonScrollableEditText(this);
        editText.setHint("这是一个可编辑的文本框\n您可以在这里输入文本...");
        editText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_FLAG_MULTI_LINE);
        editText.setMinLines(100); // 设置最小行数
        editText.setPadding(20, 20, 20, 20);
        editText.setBackgroundColor(Color.parseColor("#F5F5F5"));
        // 防止EditText自动获取焦点
        editText.setFocusableInTouchMode(true);
        editText.clearFocus();
        // 完全禁用EditText的内部滚动
        editText.setVerticalScrollBarEnabled(false);
        editText.setHorizontalScrollBarEnabled(false);
        editText.setOverScrollMode(View.OVER_SCROLL_NEVER);
        // 设置IME选项
        editText.setImeOptions(android.view.inputmethod.EditorInfo.IME_FLAG_NO_EXTRACT_UI);
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        editParams.setMargins(0, 20, 0, 20);
        contentLayout.addView(editText, editParams);

//        addDivider();

        // 添加更多测试内容
        for (int i = 1; i <= 10; i++) {
            addParagraph("第 " + i + " 段测试内容：这里是一些用于测试滚动功能的文本内容。" +
                    "您可以缩放查看细节，也可以滚动浏览整个文档。");
        }

        // 设置内容到PdfReaderView
        pdfReaderView.setContentView(contentLayout);
    }

    /**
     * 添加段落文本
     */
    private void addParagraph(String text) {
        TextView textView = new TextView(this);
        textView.setText(text);
        textView.setTextSize(16);
        textView.setTextColor(Color.BLACK);
        textView.setLineSpacing(8, 1.2f);
        textView.setPadding(0, 0, 0, 16);
//        contentLayout.addView(textView);
    }

    /**
     * 添加分隔线
     */
    private void addDivider() {
        TextView divider = new TextView(this);
        divider.setBackgroundColor(Color.LTGRAY);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, 2
        );
        params.setMargins(0, 20, 0, 20);
//        contentLayout.addView(divider, params);
    }

    /**
     * 切换内容类型
     */
    private void switchContent() {
        // 创建图片内容示例
        ImageView imageView = new ImageView(this);
        imageView.setBackgroundColor(Color.parseColor("#E3F2FD"));
        imageView.setScaleType(ImageView.ScaleType.CENTER);
        
        // 创建一个简单的文本作为图片替代
        TextView imageText = new TextView(this);
        imageText.setText("这是一个模拟图片\n\n支持缩放和滚动\n\n尺寸: 800x1200");
        imageText.setTextSize(18);
        imageText.setTextColor(Color.BLUE);
        imageText.setGravity(Gravity.CENTER);
        imageText.setBackgroundColor(Color.parseColor("#E3F2FD"));
        imageText.setPadding(40, 40, 40, 40);
        
        // 设置固定尺寸
        pdfReaderView.setContentView(imageText);
        pdfReaderView.setContentSize(800, 1200);
        
        Toast.makeText(this, "已切换到图片模式", Toast.LENGTH_SHORT).show();
    }
}
