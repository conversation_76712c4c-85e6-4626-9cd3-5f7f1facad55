# ScalableScrollView 光标位置修正改进

## 问题描述

在 `ScalableScrollView` 中放置 `EditText` 时，当进行缩放操作后，EditText 的光标位置会出现错误。主要问题包括：

1. **默认缩放为0.8倍**：初始状态下内容被缩小显示
2. **以中心点缩放**：缩放操作以手势中心点为基准进行
3. **光标位置错误**：缩放后点击文本时，光标位置与点击位置不匹配
4. **坐标计算问题**：没有正确考虑缩放和平移变换对坐标的影响

## 解决方案

### 1. 设置正确的默认缩放

```java
private static final float DEFAULT_SCALE = 0.8f;
private float scaleFactor = DEFAULT_SCALE;
```

- 将默认缩放设置为0.8倍
- 在重置方法中使用正确的默认值

### 2. 改进光标位置计算

```java
private void setCursorPosition(float touchX, float touchY) {
    // 计算在EditText内的相对位置，考虑缩放和平移变换
    float relativeX = (touchX - translateX) / scaleFactor;
    float relativeY = (touchY - translateY) / scaleFactor;

    // 确保坐标在EditText范围内
    relativeX = Math.max(0, Math.min(relativeX, editTextWidth));
    relativeY = Math.max(0, Math.min(relativeY, editTextHeight));

    // 获取并设置光标位置
    int offset = editText.getOffsetForPosition(relativeX, relativeY);
    editText.setSelection(offset);
}
```

### 3. 自动刷新光标位置

```java
public void refreshCursor() {
    if (editText != null && editText.hasFocus()) {
        editText.post(new Runnable() {
            @Override
            public void run() {
                // 保存当前选择位置
                int selectionStart = editText.getSelectionStart();
                int selectionEnd = editText.getSelectionEnd();

                // 临时失去焦点并重新获得焦点
                editText.clearFocus();
                editText.requestFocus();

                // 恢复选择位置
                if (selectionStart >= 0 && selectionEnd >= 0) {
                    editText.setSelection(selectionStart, selectionEnd);
                }

                editText.invalidate();
            }
        });
    }
}
```

### 4. 在关键时机调用刷新

- **缩放结束后**：`onScaleEnd()` 方法中调用 `refreshCursor()`
- **双击缩放后**：`animateScale()` 方法中调用 `refreshCursor()`
- **设置缩放后**：`setScaleFactor()` 方法中调用 `refreshCursor()`

### 5. 延迟处理焦点

```java
@Override
public boolean onSingleTapUp(MotionEvent e) {
    if (isTouchInEditText(e.getX(), e.getY()) && editText != null) {
        editText.requestFocus();

        // 延迟设置光标位置，确保焦点已经设置完成
        editText.post(new Runnable() {
            @Override
            public void run() {
                setCursorPosition(e.getX(), e.getY());
            }
        });

        return true;
    }
    return false;
}
```

## 使用方法

### 1. 基本使用（自动限制宽度）

```java
ScalableScrollView scalableScrollView = new ScalableScrollView(context);
EditText editText = new EditText(context);

// 设置EditText属性
editText.setText("您的文本内容");
editText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_FLAG_MULTI_LINE);

// 添加到ScalableScrollView（宽度会自动限制为不超过屏幕宽度）
int width = 1200;  // 即使设置很大的宽度，也会被限制为屏幕宽度
int height = 2000; // 像素
scalableScrollView.setEditText(editText, width, height);
```

### 2. 不限制宽度

```java
// 如果需要设置超过屏幕宽度的EditText（不推荐）
scalableScrollView.setEditText(editText, width, height, false);
```

### 3. 使用全屏宽度

```java
// 设置EditText为全屏宽度
scalableScrollView.setEditTextFullWidth(editText, height);
```

### 4. 使用百分比宽度

```java
// 设置EditText为屏幕宽度的90%
scalableScrollView.setEditTextWithWidthPercent(editText, 0.9f, height);

// 设置EditText为屏幕宽度的70%
scalableScrollView.setEditTextWithWidthPercent(editText, 0.7f, height);
```

### 5. 获取尺寸信息

```java
// 获取屏幕尺寸
int screenWidth = scalableScrollView.getScreenWidth();
int screenHeight = scalableScrollView.getScreenHeight();

// 获取EditText实际尺寸
int editTextWidth = scalableScrollView.getEditTextWidth();
int editTextHeight = scalableScrollView.getEditTextHeight();
```

### 6. 手动刷新光标

```java
// 如果发现光标位置不正确，可以手动调用
scalableScrollView.refreshCursor();
```

### 7. 设置缩放比例

```java
// 设置缩放比例（会自动刷新光标）
scalableScrollView.setScaleFactor(1.2f);
```

## 改进效果

1. **精确的光标定位**：点击文本时光标准确定位到点击位置
2. **缩放后自动修正**：缩放操作结束后自动修正光标位置
3. **稳定的焦点处理**：通过延迟处理避免焦点冲突
4. **异常处理**：添加异常处理确保应用稳定性
5. **用户体验优化**：提供手动刷新选项作为备用方案
6. **智能宽度限制**：自动限制EditText宽度不超过屏幕宽度
7. **灵活的尺寸设置**：提供多种宽度设置方式（全屏、百分比等）
8. **尺寸信息查询**：可以查询屏幕和EditText的实际尺寸

## 测试建议

1. 在不同缩放级别下测试光标定位
2. 测试长文档的滚动和缩放组合
3. 测试文本选择功能
4. 测试输入法兼容性
5. 测试异常情况的处理

## 注意事项

1. 确保在UI线程中调用刷新方法
2. 避免频繁调用刷新方法影响性能
3. 在EditText内容变化时可能需要重新计算位置
4. 考虑不同设备的屏幕密度差异

这些改进显著提升了在缩放环境下EditText的使用体验，解决了光标位置不准确的问题。
