package com.work2502.zoomtext.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.PointF;
import android.text.Layout;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.OverScroller;

/**
 * 类似PDF阅读器的自定义ViewGroup
 * 支持缩放、滚动和多点触控手势
 *
 * 主要功能：
 * - 双指缩放（pinch-to-zoom）
 * - 垂直和水平滚动
 * - 双击快速缩放
 * - 惯性滚动
 * - 边界约束
 * - 平滑动画
 */
public class PdfLikeReaderView extends FrameLayout {

    // 缩放范围常量
    private static final float MIN_SCALE = 0.8f;
    private static final float MAX_SCALE = 1.3f;
    private static final float DEFAULT_SCALE = 1.0f;

    // 动画相关常量
    private static final int ANIMATION_DURATION = 300;
    private static final int FLING_DURATION = 2000;

    // 手势检测器
    private ScaleGestureDetector scaleDetector;
    private GestureDetector gestureDetector;
    private OverScroller scroller;


    // 变换参数
    private float scaleFactor = DEFAULT_SCALE;
    private float translateX = 0f;
    private float translateY = 0f;

    // 触摸状态
    private PointF lastTouchPoint = new PointF();
    private boolean isScaling = false;
    private boolean isDragging = false;
    private boolean isAnimating = false;

    // 内容视图
    private View contentView;
    private int contentWidth = 0;
    private int contentHeight = 0;

    // 触摸相关
    private int touchSlop;
    private float totalDx = 0f;
    private float totalDy = 0f;

    // 滑动方向检测
    private static final int SCROLL_DIRECTION_NONE = 0;
    private static final int SCROLL_DIRECTION_HORIZONTAL = 1;
    private static final int SCROLL_DIRECTION_VERTICAL = 2;
    private int scrollDirection = SCROLL_DIRECTION_NONE;

    // 动画器
    private ValueAnimator scaleAnimator;
    private ValueAnimator translateAnimator;

    public PdfLikeReaderView(Context context) {
        super(context);
        init();
    }

    public PdfLikeReaderView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public PdfLikeReaderView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    /**
     * 初始化组件
     */
    private void init() {
        // 初始化手势检测器
        scaleDetector = new ScaleGestureDetector(getContext(), new ScaleListener());
        gestureDetector = new GestureDetector(getContext(), new GestureListener());
        scroller = new OverScroller(getContext(), new DecelerateInterpolator());

        // 获取触摸配置
        ViewConfiguration vc = ViewConfiguration.get(getContext());
        touchSlop = vc.getScaledTouchSlop();

        // 设置可获取焦点
        setFocusable(true);
        setFocusableInTouchMode(true);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (contentView != null && w > 0 && h > 0) {
            measureContentSize();
            centerContent();
        }
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        // 获取第一个子视图作为内容视图
        if (getChildCount() > 0) {
            setContentView(getChildAt(0));
        }
    }

    /**
     * 设置内容视图
     */
    public void setContentView(View view) {
        if (contentView != null) {
            removeView(contentView);
            // 移除之前的焦点监听
            removeEditTextFocusListeners(contentView);
        }

        contentView = view;
        if (contentView != null) {
            addView(contentView);

            // 添加EditText焦点监听
            addEditTextFocusListeners(contentView);

            // 强制布局以获取正确的尺寸
            post(() -> {
                measureContentSize();
                centerContent();
            });
        }
    }

    /**
     * 测量内容尺寸
     */
    private void measureContentSize() {
        if (contentView == null) return;

        // 使用父容器的尺寸进行测量
        int parentWidth = getWidth();
        int parentHeight = getHeight();

        if (parentWidth > 0 && parentHeight > 0) {
            // 测量内容尺寸
            contentView.measure(
                MeasureSpec.makeMeasureSpec(parentWidth, MeasureSpec.AT_MOST),
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
            );

            contentWidth = contentView.getMeasuredWidth();
            contentHeight = contentView.getMeasuredHeight();

            // 如果内容太小，使用最小尺寸
            if (contentWidth < parentWidth / 2) {
                contentWidth = parentWidth;
            }
            if (contentHeight < parentHeight / 2) {
                contentHeight = Math.max(contentHeight, parentHeight);
            }
        }
    }

    /**
     * 设置内容视图的尺寸
     */
    public void setContentSize(int width, int height) {
        contentWidth = width;
        contentHeight = height;
        if (contentView != null) {
            LayoutParams params = new LayoutParams(width, height);
            contentView.setLayoutParams(params);
            centerContent();
        }
    }

    /**
     * 改进触摸事件拦截逻辑
     */
    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (contentView == null) {
            return super.onInterceptTouchEvent(ev);
        }

        // 获取当前触摸的视图
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            currentTouchedView = findViewAtPoint(ev.getRawX(), ev.getRawY());
        }

        // 如果当前正在编辑文本，且点击在EditText内部，不拦截触摸事件
        if (isEditTextFocused && currentTouchedView instanceof EditText) {
            // 检查是否是点击或选择文本的操作
            if (ev.getAction() == MotionEvent.ACTION_DOWN ||
                ev.getAction() == MotionEvent.ACTION_UP ||
                ev.getPointerCount() > 1) {
                return false;
            }

            // 对于移动事件，我们需要判断是否是滚动操作
            if (ev.getAction() == MotionEvent.ACTION_MOVE) {
                float dy = ev.getY() - lastTouchPoint.y;
                // 如果是小幅度移动，可能是选择文本，不拦截
                if (Math.abs(dy) < touchSlop * 2) {
                    return false;
                }
            }
        }

        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 停止当前的滚动和动画
                scroller.abortAnimation();
                stopAllAnimations();

                lastTouchPoint.set(ev.getX(), ev.getY());
                totalDx = 0f;
                totalDy = 0f;
                isDragging = false;
                scrollDirection = SCROLL_DIRECTION_NONE;
                break;

            case MotionEvent.ACTION_MOVE:
                if (ev.getPointerCount() > 1) {
                    // 多点触摸，拦截用于缩放
                    scrollDirection = SCROLL_DIRECTION_NONE;
                    return true;
                }

                float dx = ev.getX() - lastTouchPoint.x;
                float dy = ev.getY() - lastTouchPoint.y;
                totalDx += Math.abs(dx);
                totalDy += Math.abs(dy);

                // 确定主要滑动方向
                if (scrollDirection == SCROLL_DIRECTION_NONE && (totalDx > touchSlop || totalDy > touchSlop)) {
                    if (Math.abs(totalDx) > Math.abs(totalDy) * 1.5f) {
                        // 主要是水平滑动
                        scrollDirection = SCROLL_DIRECTION_HORIZONTAL;
                    } else if (Math.abs(totalDy) > Math.abs(totalDx) * 1.5f) {
                        // 主要是垂直滑动
                        scrollDirection = SCROLL_DIRECTION_VERTICAL;
                    } else {
                        // 对角线滑动，允许两个方向
                        scrollDirection = SCROLL_DIRECTION_HORIZONTAL | SCROLL_DIRECTION_VERTICAL;
                    }
                }

                // 如果移动距离超过阈值且可以滚动，拦截事件
                if (scrollDirection != SCROLL_DIRECTION_NONE && canScroll()) {
                    // 检查是否需要滚动
                    float[] bounds = getScrollBounds();
                    boolean canScrollHorizontally = bounds[0] != bounds[1]; // minX != maxX
                    boolean canScrollVertically = bounds[2] != bounds[3];   // minY != maxY

                    // 根据滑动方向和可滚动方向决定是否拦截
                    boolean shouldIntercept = false;
                    if ((scrollDirection & SCROLL_DIRECTION_HORIZONTAL) != 0 && canScrollHorizontally) {
                        shouldIntercept = true;
                    }
                    if ((scrollDirection & SCROLL_DIRECTION_VERTICAL) != 0 && canScrollVertically) {
                        shouldIntercept = true;
                    }

                    if (shouldIntercept) {
                        getParent().requestDisallowInterceptTouchEvent(true);
                        return true;
                    }
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                getParent().requestDisallowInterceptTouchEvent(false);
                scrollDirection = SCROLL_DIRECTION_NONE;
                currentTouchedView = null;
                break;
        }

        return super.onInterceptTouchEvent(ev);
    }

    /**
     * 改进触摸事件处理
     */
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (contentView == null) {
            return super.onTouchEvent(event);
        }

        boolean handled = false;

        // 如果当前正在编辑文本，不处理缩放和滚动
        if (isEditTextFocused && currentTouchedView instanceof EditText) {
            return false;
        }

        // 处理缩放手势
        handled |= scaleDetector.onTouchEvent(event);

        // 如果不在缩放状态，处理其他手势
        if (!isScaling) {
            handled |= gestureDetector.onTouchEvent(event);
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                isScaling = false;
                isDragging = false;
                scrollDirection = SCROLL_DIRECTION_NONE;
                currentTouchedView = null;
                break;
        }

        return handled || super.onTouchEvent(event);
    }

    /**
     * 缩放手势监听器
     */
    private class ScaleListener extends ScaleGestureDetector.SimpleOnScaleGestureListener {
        @Override
        public boolean onScaleBegin(ScaleGestureDetector detector) {
            isScaling = true;
            stopAllAnimations();
            return true;
        }

        @Override
        public boolean onScale(ScaleGestureDetector detector) {
            float prevScale = scaleFactor;
            scaleFactor *= detector.getScaleFactor();
            scaleFactor = Math.max(MIN_SCALE, Math.min(scaleFactor, MAX_SCALE));

            if (scaleFactor != prevScale) {
                // 以手势中心点为基准进行缩放
                float focusX = detector.getFocusX();
                float focusY = detector.getFocusY();

                // 计算缩放后的平移调整
                float scaleChange = scaleFactor / prevScale;

                // 考虑当前的pivot点，调整平移
                float pivotX = contentWidth / 2f;
                float pivotY = contentHeight / 2f;

                // 计算相对于内容中心的焦点位置
                float contentCenterX = translateX + pivotX * prevScale;
                float contentCenterY = translateY + pivotY * prevScale;

                // 计算新的平移位置
                translateX = focusX - (focusX - contentCenterX) * scaleChange - pivotX * scaleFactor;
                translateY = focusY - (focusY - contentCenterY) * scaleChange - pivotY * scaleFactor;

                constrainTranslation();
                updateTransform();
            }
            return true;
        }

        @Override
        public void onScaleEnd(ScaleGestureDetector detector) {
            isScaling = false;
        }
    }

    /**
     * 普通手势监听器
     */
    private class GestureListener extends GestureDetector.SimpleOnGestureListener {
        @Override
        public boolean onDown(MotionEvent e) {
            scroller.abortAnimation();
            return true;
        }

        @Override
        public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
            if (!isScaling && !isAnimating) {
                isDragging = true;

                // 根据滑动方向限制移动
                float newTranslateX = translateX;
                float newTranslateY = translateY;

                // 检查可滚动方向
                float[] bounds = getScrollBounds();
                boolean canScrollHorizontally = bounds[0] != bounds[1]; // minX != maxX
                boolean canScrollVertically = bounds[2] != bounds[3];   // minY != maxY

                // 根据主要滑动方向和可滚动方向决定是否移动
                if (scrollDirection == SCROLL_DIRECTION_HORIZONTAL && canScrollHorizontally) {
                    // 只允许水平移动
                    newTranslateX -= distanceX;
                } else if (scrollDirection == SCROLL_DIRECTION_VERTICAL && canScrollVertically) {
                    // 只允许垂直移动
                    newTranslateY -= distanceY;
                } else if ((scrollDirection & SCROLL_DIRECTION_HORIZONTAL) != 0 &&
                          (scrollDirection & SCROLL_DIRECTION_VERTICAL) != 0) {
                    // 对角线滑动，允许两个方向
                    if (canScrollHorizontally) {
                        newTranslateX -= distanceX;
                    }
                    if (canScrollVertically) {
                        newTranslateY -= distanceY;
                    }
                } else {
                    // 自动检测主要方向
                    if (Math.abs(distanceX) > Math.abs(distanceY) && canScrollHorizontally) {
                        newTranslateX -= distanceX;
                    } else if (Math.abs(distanceY) > Math.abs(distanceX) && canScrollVertically) {
                        newTranslateY -= distanceY;
                    } else {
                        // 允许两个方向
                        if (canScrollHorizontally) newTranslateX -= distanceX;
                        if (canScrollVertically) newTranslateY -= distanceY;
                    }
                }

                translateX = newTranslateX;
                translateY = newTranslateY;
                constrainTranslation();
                updateTransform();
                return true;
            }
            return false;
        }

        @Override
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            if (!isScaling && !isAnimating) {
                // 根据滑动方向限制惯性滚动
                float finalVelocityX = velocityX;
                float finalVelocityY = velocityY;

                // 检查可滚动方向
                float[] bounds = getScrollBounds();
                boolean canScrollHorizontally = bounds[0] != bounds[1];
                boolean canScrollVertically = bounds[2] != bounds[3];

                // 根据主要滑动方向限制惯性滚动
                if (scrollDirection == SCROLL_DIRECTION_HORIZONTAL) {
                    // 主要是水平滑动，减少垂直惯性
                    finalVelocityY *= 0.1f;
                } else if (scrollDirection == SCROLL_DIRECTION_VERTICAL) {
                    // 主要是垂直滑动，减少水平惯性
                    finalVelocityX *= 0.1f;
                }

                // 如果不能在某个方向滚动，取消该方向的惯性
                if (!canScrollHorizontally) {
                    finalVelocityX = 0;
                }
                if (!canScrollVertically) {
                    finalVelocityY = 0;
                }

                startFling(finalVelocityX, finalVelocityY);
                return true;
            }
            return false;
        }

        @Override
        public boolean onDoubleTap(MotionEvent e) {
            // 双击缩放
            float targetScale = (scaleFactor > DEFAULT_SCALE) ? DEFAULT_SCALE : MAX_SCALE * 0.6f;
            animateScale(targetScale, e.getX(), e.getY());
            return true;
        }
    }

    /**
     * 启动惯性滚动
     */
    private void startFling(float velocityX, float velocityY) {
        if (contentView == null) return;

        // 计算滚动边界
        float[] bounds = getScrollBounds();
        int minX = (int) bounds[0];
        int maxX = (int) bounds[1];
        int minY = (int) bounds[2];
        int maxY = (int) bounds[3];

        scroller.fling(
            (int) translateX, (int) translateY,
            (int) velocityX, (int) velocityY,
            minX, maxX, minY, maxY,
            50, 50  // 边界回弹效果
        );

        post(flingRunnable);
    }

    /**
     * 惯性滚动Runnable
     */
    private final Runnable flingRunnable = new Runnable() {
        @Override
        public void run() {
            if (scroller.computeScrollOffset()) {
                translateX = scroller.getCurrX();
                translateY = scroller.getCurrY();
                updateTransform();
                post(this);
            }
        }
    };

    /**
     * 获取滚动边界
     */
    private float[] getScrollBounds() {
        if (contentView == null) {
            return new float[]{0, 0, 0, 0};
        }

        float scaledWidth = contentWidth * scaleFactor;
        float scaledHeight = contentHeight * scaleFactor;

        float viewWidth = getWidth();
        float viewHeight = getHeight();

        float minX, maxX, minY, maxY;

        if (scaledWidth <= viewWidth) {
            // 内容宽度小于视图宽度，居中显示
            minX = maxX = (viewWidth - scaledWidth) / 2;
        } else {
            // 内容宽度大于视图宽度，允许滚动
            minX = viewWidth - scaledWidth;
            maxX = 0;
        }

        if (scaledHeight <= viewHeight) {
            // 内容高度小于视图高度，居中显示
            minY = maxY = (viewHeight - scaledHeight) / 2;
        } else {
            // 内容高度大于视图高度，允许滚动
            minY = viewHeight - scaledHeight;
            maxY = 0;
        }

        return new float[]{minX, maxX, minY, maxY};
    }

    /**
     * 约束平移范围
     */
    private void constrainTranslation() {
        float[] bounds = getScrollBounds();

        // 更精确的边界约束，避免浮点数精度问题
        float minX = bounds[0];
        float maxX = bounds[1];
        float minY = bounds[2];
        float maxY = bounds[3];

        // 添加小的容差值，避免边界抖动
        float tolerance = 0.5f;

        if (translateX < minX - tolerance) {
            translateX = minX;
        } else if (translateX > maxX + tolerance) {
            translateX = maxX;
        }

        if (translateY < minY - tolerance) {
            translateY = minY;
        } else if (translateY > maxY + tolerance) {
            translateY = maxY;
        }
    }

    /**
     * 检查是否可以滚动
     */
    private boolean canScroll() {
        if (contentView == null) return false;

        float scaledWidth = contentWidth * scaleFactor;
        float scaledHeight = contentHeight * scaleFactor;

        return scaledWidth > getWidth() || scaledHeight > getHeight();
    }

    /**
     * 居中显示内容
     */
    private void centerContent() {
        if (contentView == null || getWidth() == 0 || getHeight() == 0 || contentWidth == 0 || contentHeight == 0) return;

        float scaledWidth = contentWidth * scaleFactor;
        float scaledHeight = contentHeight * scaleFactor;

        // 计算居中位置，考虑缩放后的尺寸
        translateX = (getWidth() - scaledWidth) / 2;
        translateY = (getHeight() - scaledHeight) / 2;

        // 确保内容在合理范围内
        constrainTranslation();
        updateTransform();
    }

    /**
     * 更新变换
     */
    private void updateTransform() {
        if (contentView == null) return;

        // 设置缩放中心点为内容视图的中心
        contentView.setPivotX(contentWidth / 2f);
        contentView.setPivotY(contentHeight / 2f);

        // 应用变换
        contentView.setTranslationX(translateX);
        contentView.setTranslationY(translateY);
        contentView.setScaleX(scaleFactor);
        contentView.setScaleY(scaleFactor);

        invalidate();
    }

    /**
     * 动画缩放到指定比例
     */
    private void animateScale(float targetScale, float focusX, float focusY) {
        if (isAnimating) return;

        targetScale = Math.max(MIN_SCALE, Math.min(targetScale, MAX_SCALE));

        float startScale = scaleFactor;
        float startX = translateX;
        float startY = translateY;

        // 计算目标平移位置
        float scaleChange = targetScale / startScale;
        float targetX = focusX + (startX - focusX) * scaleChange;
        float targetY = focusY + (startY - focusY) * scaleChange;

        stopAllAnimations();

        scaleAnimator = ValueAnimator.ofFloat(0f, 1f);
        scaleAnimator.setDuration(ANIMATION_DURATION);
        scaleAnimator.setInterpolator(new DecelerateInterpolator());
        float finalTargetScale = targetScale;
        scaleAnimator.addUpdateListener(animation -> {
            float progress = (Float) animation.getAnimatedValue();

            scaleFactor = startScale + (finalTargetScale - startScale) * progress;
            translateX = startX + (targetX - startX) * progress;
            translateY = startY + (targetY - startY) * progress;

            constrainTranslation();
            updateTransform();
        });
        scaleAnimator.addListener(new android.animation.AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(android.animation.Animator animation) {
                isAnimating = true;
            }

            @Override
            public void onAnimationEnd(android.animation.Animator animation) {
                isAnimating = false;
            }
        });

        isAnimating = true;
        scaleAnimator.start();
    }

    /**
     * 停止所有动画
     */
    private void stopAllAnimations() {
        if (scaleAnimator != null && scaleAnimator.isRunning()) {
            scaleAnimator.cancel();
        }
        if (translateAnimator != null && translateAnimator.isRunning()) {
            translateAnimator.cancel();
        }
        removeCallbacks(flingRunnable);
        scroller.abortAnimation();
        isAnimating = false;
    }

    // ========== 公共API方法 ==========

    /**
     * 获取当前缩放比例
     */
    public float getScaleFactor() {
        return scaleFactor;
    }

    /**
     * 设置缩放比例
     */
    public void setScaleFactor(float scale) {
        setScaleFactor(scale, true);
    }

    /**
     * 设置缩放比例
     * @param scale 缩放比例
     * @param animate 是否使用动画
     */
    public void setScaleFactor(float scale, boolean animate) {
        scale = Math.max(MIN_SCALE, Math.min(scale, MAX_SCALE));

        if (animate) {
            animateScale(scale, getWidth() / 2f, getHeight() / 2f);
        } else {
            scaleFactor = scale;
            centerContent();
        }
    }

    /**
     * 重置缩放和位置
     */
    public void reset() {
        reset(true);
    }

    /**
     * 重置缩放和位置
     * @param animate 是否使用动画
     */
    public void reset(boolean animate) {
        if (animate) {
            animateScale(DEFAULT_SCALE, getWidth() / 2f, getHeight() / 2f);
        } else {
            scaleFactor = DEFAULT_SCALE;
            centerContent();
        }
    }

    /**
     * 滚动到顶部
     */
    public void scrollToTop() {
        scrollToTop(true);
    }

    /**
     * 滚动到顶部
     * @param animate 是否使用动画
     */
    public void scrollToTop(boolean animate) {
        float targetY = getScrollBounds()[3]; // maxY
        scrollTo(translateX, targetY, animate);
    }

    /**
     * 滚动到底部
     */
    public void scrollToBottom() {
        scrollToBottom(true);
    }

    /**
     * 滚动到底部
     * @param animate 是否使用动画
     */
    public void scrollToBottom(boolean animate) {
        float targetY = getScrollBounds()[2]; // minY
        scrollTo(translateX, targetY, animate);
    }

    /**
     * 滚动到指定位置
     */
    public void scrollTo(float x, float y, boolean animate) {
        if (animate) {
            animateTranslate(x, y);
        } else {
            translateX = x;
            translateY = y;
            constrainTranslation();
            updateTransform();
        }
    }

    /**
     * 动画平移到指定位置
     */
    private void animateTranslate(float targetX, float targetY) {
        if (isAnimating) return;

        float startX = translateX;
        float startY = translateY;

        // 约束目标位置
        float[] bounds = getScrollBounds();
        targetX = Math.max(bounds[0], Math.min(targetX, bounds[1]));
        targetY = Math.max(bounds[2], Math.min(targetY, bounds[3]));

        stopAllAnimations();

        translateAnimator = ValueAnimator.ofFloat(0f, 1f);
        translateAnimator.setDuration(ANIMATION_DURATION);
        translateAnimator.setInterpolator(new DecelerateInterpolator());
        float finalTargetX = targetX;
        float finalTargetY = targetY;
        translateAnimator.addUpdateListener(animation -> {
            float progress = (Float) animation.getAnimatedValue();

            translateX = startX + (finalTargetX - startX) * progress;
            translateY = startY + (finalTargetY - startY) * progress;

            updateTransform();
        });
        translateAnimator.addListener(new android.animation.AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(android.animation.Animator animation) {
                isAnimating = true;
            }

            @Override
            public void onAnimationEnd(android.animation.Animator animation) {
                isAnimating = false;
            }
        });

        isAnimating = true;
        translateAnimator.start();
    }

    /**
     * 设置缩放范围
     */
    public void setScaleRange(float minScale, float maxScale) {
        // 这里我们不能修改常量，但可以添加实例变量来支持动态范围
        // 为了保持简单，暂时保持常量不变
    }

    /**
     * 获取内容视图
     */
    public View getContentView() {
        return contentView;
    }

    /**
     * 检查是否正在缩放
     */
    public boolean isScaling() {
        return isScaling;
    }

    /**
     * 检查是否正在拖拽
     */
    public boolean isDragging() {
        return isDragging;
    }

    /**
     * 检查是否正在动画
     */
    public boolean isAnimating() {
        return isAnimating;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopAllAnimations();
    }

    /**
     * 查找指定坐标处的任何视图
     */
    private View findViewAtPoint(float rawX, float rawY) {
        if (contentView == null) return null;

        // 转换为视图内坐标
        int[] location = new int[2];
        getLocationOnScreen(location);
        float localX = rawX - location[0];
        float localY = rawY - location[1];

        // 考虑当前的缩放和平移
        float contentX = (localX - translateX) / scaleFactor;
        float contentY = (localY - translateY) / scaleFactor;

        // 递归查找视图
        return findViewInViewGroup(contentView, contentX, contentY);
    }

    /**
     * 递归查找ViewGroup中的视图
     */
    private View findViewInViewGroup(View view, float x, float y) {
        // 检查坐标是否在视图范围内
        if (x < view.getLeft() || x > view.getRight() ||
            y < view.getTop() || y > view.getBottom()) {
            return null;
        }

        // 如果是ViewGroup，递归查找子视图
        if (view instanceof ViewGroup) {
            ViewGroup group = (ViewGroup) view;
            for (int i = group.getChildCount() - 1; i >= 0; i--) {
                View child = group.getChildAt(i);
                // 转换为子视图的坐标
                float childX = x - child.getLeft();
                float childY = y - child.getTop();
                View result = findViewInViewGroup(child, childX, childY);
                if (result != null) {
                    return result;
                }
            }
        }

        // 返回当前视图
        return view;
    }

    /**
     * 递归添加EditText焦点监听
     */
    private void addEditTextFocusListeners(View view) {
        if (view instanceof EditText) {
            EditText editText = (EditText) view;
            editText.setOnFocusChangeListener((v, hasFocus) -> {
                if (hasFocus) {
                    // 当EditText获得焦点时，禁用缩放和滚动
                    isEditTextFocused = true;
                    // 确保EditText在视图中可见
                    ensureEditTextVisible((EditText) v);
                } else {
                    // 当EditText失去焦点时，恢复缩放和滚动
                    isEditTextFocused = false;
                }
            });

            // 添加文本变化监听，确保在输入时EditText保持可见
            editText.addTextChangedListener(new android.text.TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(android.text.Editable s) {
                    if (isEditTextFocused) {
                        ensureEditTextVisible(editText);
                    }
                }
            });
        } else if (view instanceof ViewGroup) {
            ViewGroup group = (ViewGroup) view;
            for (int i = 0; i < group.getChildCount(); i++) {
                addEditTextFocusListeners(group.getChildAt(i));
            }
        }
    }

    /**
     * 递归移除EditText焦点监听
     */
    private void removeEditTextFocusListeners(View view) {
        if (view instanceof EditText) {
            EditText editText = (EditText) view;
            editText.setOnFocusChangeListener(null);
            editText.removeTextChangedListener(null); // 移除所有文本变化监听器
        } else if (view instanceof ViewGroup) {
            ViewGroup group = (ViewGroup) view;
            for (int i = 0; i < group.getChildCount(); i++) {
                removeEditTextFocusListeners(group.getChildAt(i));
            }
        }
    }

    // 添加一个标志来跟踪EditText的焦点状态
    private boolean isEditTextFocused = false;

    // 添加一个变量来跟踪当前触摸的视图
    private View currentTouchedView = null;

    /**
     * 确保EditText在视图中可见，并处理光标位置
     */
    private void ensureEditTextVisible(EditText editText) {
        if (editText == null) return;

        // 获取EditText在内容视图中的位置
        int[] editTextLocation = new int[2];
        editText.getLocationInWindow(editTextLocation);

        // 获取PdfLikeReaderView在屏幕中的位置
        int[] viewLocation = new int[2];
        getLocationInWindow(viewLocation);

        // 计算EditText相对于PdfLikeReaderView的位置
        float relativeX = editTextLocation[0] - viewLocation[0];
        float relativeY = editTextLocation[1] - viewLocation[1];

        // 计算EditText的尺寸（考虑缩放）
        float editTextWidth = editText.getWidth() * scaleFactor;
        float editTextHeight = editText.getHeight() * scaleFactor;

        // 获取光标位置
        int selectionStart = editText.getSelectionStart();
        int selectionEnd = editText.getSelectionEnd();

        // 如果有选择区域，确保它可见
        if (selectionStart >= 0 && selectionEnd >= 0) {
            try {
                // 获取光标位置的布局坐标
                Layout layout = editText.getLayout();
                if (layout != null) {
                    int line = layout.getLineForOffset(selectionStart);
                    float cursorX = layout.getPrimaryHorizontal(selectionStart);
                    float cursorY = layout.getLineTop(line);

                    // 转换为屏幕坐标
                    float screenCursorX = relativeX + cursorX * scaleFactor;
                    float screenCursorY = relativeY + cursorY * scaleFactor;

                    // 检查光标是否在视图范围内
                    boolean isCursorVisible = screenCursorX >= 0 && screenCursorX <= getWidth() &&
                                             screenCursorY >= 0 && screenCursorY <= getHeight();

                    // 如果光标不可见，调整位置
                    if (!isCursorVisible) {
                        float adjustX = translateX;
                        float adjustY = translateY;

                        if (screenCursorX < 0) {
                            adjustX -= screenCursorX - 20; // 额外空间
                        } else if (screenCursorX > getWidth()) {
                            adjustX -= (screenCursorX - getWidth() + 20);
                        }

                        if (screenCursorY < 0) {
                            adjustY -= screenCursorY - 20;
                        } else if (screenCursorY > getHeight()) {
                            adjustY -= (screenCursorY - getHeight() + 20);
                        }

                        // 应用调整
                        animateTranslate(adjustX, adjustY);
                        return;
                    }
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }

        // 如果无法获取光标位置或发生异常，使用常规方法确保EditText可见
        boolean isVisible = relativeX >= 0 && relativeX + editTextWidth <= getWidth() &&
                            relativeY >= 0 && relativeY + editTextHeight <= getHeight();

        if (!isVisible) {
            float adjustX = translateX;
            float adjustY = translateY;

            if (relativeX < 0) {
                adjustX -= relativeX - 20;
            } else if (relativeX + editTextWidth > getWidth()) {
                adjustX -= (relativeX + editTextWidth - getWidth() + 20);
            }

            if (relativeY < 0) {
                adjustY -= relativeY - 20;
            } else if (relativeY + editTextHeight > getHeight()) {
                adjustY -= (relativeY + editTextHeight - getHeight() + 20);
            }

            // 应用调整
            animateTranslate(adjustX, adjustY);
        }
    }
    /**
     * 自定义EditText，禁用内部滚动
     */
    public static class NonScrollableEditText extends androidx.appcompat.widget.AppCompatEditText {
        public NonScrollableEditText(Context context) {
            super(context);
        }

        public NonScrollableEditText(Context context, AttributeSet attrs) {
            super(context, attrs);
        }

        public NonScrollableEditText(Context context, AttributeSet attrs, int defStyleAttr) {
            super(context, attrs, defStyleAttr);
        }

        @Override
        protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
            // 使EditText高度适应内容
            int heightMode = MeasureSpec.getMode(heightMeasureSpec);
            if (heightMode != MeasureSpec.EXACTLY) {
                heightMeasureSpec = MeasureSpec.makeMeasureSpec(
                        Integer.MAX_VALUE >> 2, MeasureSpec.AT_MOST);
            }
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        }

        @Override
        public boolean dispatchTouchEvent(MotionEvent event) {
            // 允许点击和选择文本，但禁止滚动
            getParent().requestDisallowInterceptTouchEvent(false);
            return super.dispatchTouchEvent(event);
        }

        @Override
        protected boolean overScrollBy(int deltaX, int deltaY, int scrollX, int scrollY,
                                       int scrollRangeX, int scrollRangeY, int maxOverScrollX,
                                       int maxOverScrollY, boolean isTouchEvent) {
            // 禁用过度滚动
            return false;
        }

        @Override
        public void scrollTo(int x, int y) {
            // 禁用滚动
        }

        @Override
        public void scrollBy(int x, int y) {
            // 禁用滚动
        }
    }
}
