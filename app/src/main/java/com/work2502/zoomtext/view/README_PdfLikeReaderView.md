# PdfLikeReaderView 使用说明

## 概述

`PdfLikeReaderView` 是一个类似PDF阅读器功能的自定义Android ViewGroup组件，支持缩放、滚动和多点触控手势操作。

## 主要功能

### 1. 缩放功能
- **双指缩放（Pinch-to-Zoom）**：支持双指捏合手势进行缩放
- **缩放范围**：0.5x - 5.0x
- **缩放中心**：以手势中心点为基准进行缩放
- **平滑动画**：支持动画缩放效果

### 2. 滚动功能
- **垂直滚动**：支持上下滑动浏览内容
- **水平滚动**：支持左右滑动浏览内容
- **惯性滚动**：支持惯性滚动效果
- **边界约束**：自动约束滚动边界，防止内容滚动到不合理位置

### 3. 触摸交互
- **多点触控**：正确处理多点触控手势
- **双击缩放**：双击快速缩放功能
- **手势区分**：智能区分缩放和滚动操作
- **触摸事件分发**：正确处理触摸事件的拦截和分发

### 4. 动画效果
- **平滑缩放动画**：缩放操作带有平滑的动画效果
- **平滑滚动动画**：滚动到指定位置时的动画效果
- **动画时长**：300ms的动画时长，提供良好的用户体验

## 使用方法

### 1. 基本使用

```java
// 在Activity中创建
PdfLikeReaderView pdfReaderView = new PdfLikeReaderView(this);

// 设置内容视图
TextView contentView = new TextView(this);
contentView.setText("这是要显示的内容");
pdfReaderView.setContentView(contentView);

// 添加到布局中
parentLayout.addView(pdfReaderView);
```

### 2. XML布局使用

```xml
<com.work2502.zoomtext.view.PdfLikeReaderView
    android:id="@+id/pdfReaderView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    
    <!-- 内容视图 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="PDF内容示例" />
            
    </LinearLayout>
    
</com.work2502.zoomtext.view.PdfLikeReaderView>
```

### 3. 程序化控制

```java
// 获取当前缩放比例
float currentScale = pdfReaderView.getScaleFactor();

// 设置缩放比例（带动画）
pdfReaderView.setScaleFactor(2.0f);

// 设置缩放比例（无动画）
pdfReaderView.setScaleFactor(2.0f, false);

// 重置缩放和位置
pdfReaderView.reset();

// 滚动到顶部
pdfReaderView.scrollToTop();

// 滚动到底部
pdfReaderView.scrollToBottom();

// 滚动到指定位置
pdfReaderView.scrollTo(100, 200, true);

// 设置内容尺寸
pdfReaderView.setContentSize(800, 1200);
```

### 4. 状态查询

```java
// 检查是否正在缩放
boolean isScaling = pdfReaderView.isScaling();

// 检查是否正在拖拽
boolean isDragging = pdfReaderView.isDragging();

// 检查是否正在动画
boolean isAnimating = pdfReaderView.isAnimating();

// 获取内容视图
View contentView = pdfReaderView.getContentView();
```

## 手势操作

### 1. 缩放手势
- **双指捏合**：放大或缩小内容
- **双击**：在默认大小和放大状态之间切换

### 2. 滚动手势
- **单指拖拽**：滚动浏览内容
- **惯性滚动**：快速滑动后的惯性效果

### 3. 边界处理
- 内容小于视图时自动居中显示
- 内容大于视图时允许滚动
- 自动约束滚动边界

## 技术实现

### 1. 核心组件
- `ScaleGestureDetector`：处理缩放手势
- `GestureDetector`：处理滚动和双击手势
- `OverScroller`：处理惯性滚动
- `ValueAnimator`：处理动画效果

### 2. 变换实现
- 使用View的`setTranslationX/Y`和`setScaleX/Y`方法
- 实时计算和约束变换参数
- 以手势中心点为基准进行缩放

### 3. 性能优化
- 智能的触摸事件拦截
- 动画期间停止其他操作
- 及时清理动画资源

## 适用场景

1. **PDF阅读器**：显示PDF文档内容
2. **图片查看器**：查看大尺寸图片
3. **长文档阅读**：阅读长篇文章或文档
4. **图表展示**：显示需要缩放查看的图表
5. **地图应用**：简单的地图缩放和滚动

## 注意事项

1. **内容视图**：确保设置了合适的内容视图
2. **尺寸设置**：对于固定尺寸的内容，使用`setContentSize`方法
3. **触摸冲突**：注意与其他触摸组件的冲突处理
4. **内存管理**：大内容时注意内存使用
5. **动画性能**：复杂内容时可能影响动画流畅度

## 示例代码

完整的使用示例请参考：
- `PdfLikeReaderActivity.java`：演示Activity
- `PdfLikeReaderLauncher.java`：启动器类

这些文件展示了如何创建不同类型的内容（文本、图片等）并在PdfLikeReaderView中显示。
