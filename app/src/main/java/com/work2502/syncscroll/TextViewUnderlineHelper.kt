package com.work2502.syncscroll

import android.graphics.Paint
import android.widget.TextView

/**
 * TextView下划线辅助类
 */
object TextViewUnderlineHelper {
    
    /**
     * 为TextView添加下划线
     * @param textView 需要添加下划线的TextView
     */
    fun addUnderline(textView: TextView) {
        // 设置下划线
        textView.paintFlags = textView.paintFlags or Paint.UNDERLINE_TEXT_FLAG
    }
    
    /**
     * 移除TextView的下划线
     * @param textView 需要移除下划线的TextView
     */
    fun removeUnderline(textView: TextView) {
        // 移除下划线
        textView.paintFlags = textView.paintFlags and Paint.UNDERLINE_TEXT_FLAG.inv()
    }
    
    /**
     * 切换TextView的下划线状态
     * @param textView 需要切换下划线状态的TextView
     * @return 切换后是否有下划线
     */
    fun toggleUnderline(textView: TextView): Boolean {
        val hasUnderline = textView.paintFlags and Paint.UNDERLINE_TEXT_FLAG != 0
        if (hasUnderline) {
            removeUnderline(textView)
        } else {
            addUnderline(textView)
        }
        return !hasUnderline
    }
}
