package com.work2502.syncscroll

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.widget.OverScroller
import android.widget.ScrollView
import java.lang.reflect.Field

/**
 * 上下滑动列表
 * <AUTHOR>
 * @modified 修改为仅支持垂直滚动
 */
class BaseTableScrollView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : ScrollView(context, attrs) {
    private var currentScrollT: Int = 0
    private  var scrollChangedListener: OnMyScrollChangedListener? = null
    var isIntercept: Boolean = true
    lateinit var scrollerField: Field
    var maxScrollY = 0
    var changeListeners= arrayListOf<OnMyScrollChangedListener>()

    init {
        tag = VIEW_TAG
        overScrollMode = OVER_SCROLL_NEVER
        try {
            scrollerField = javaClass.superclass.getDeclaredField("mScroller")
            scrollerField.isAccessible = true
        } catch (e: NoSuchFieldException) {
            e.printStackTrace()
        }
    }
    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        return super.onInterceptTouchEvent(ev)
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        return super.dispatchTouchEvent(ev)
    }
    fun abortScroll() {
        try {
            val scroller = scrollerField[this] as OverScroller
            if (scroller != null && !scroller.isFinished) {
                scroller.abortAnimation()
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
    /**
     * 获取当前ScrollView最大能滑动的垂直距离
     */
    private fun getChildHeight(): Int {
        try {
            if (maxScrollY==0) {
                maxScrollY = getChildAt(0).measuredHeight - measuredHeight
            }
        } catch (e: Exception) {
            Log.e("SV", "测量最大垂直滑动距离发生异常:$e")
        }
        return maxScrollY
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        if (ev.actionMasked == MotionEvent.ACTION_DOWN) {
            scrollChangedListener?.abort()
        }
        when (ev.action) {
            MotionEvent.ACTION_MOVE -> {
                val scrollY = scrollY
                val childHeight = getChildHeight()
//                Log.i("SV", "scrollY= $scrollY;  getChildHeight==  $childHeight  ")
                // 处理垂直滚动的边界情况
                if(isIntercept) {
                    if (scrollY >= childHeight) {
                        // 已经滚动到底部
                        parent.requestDisallowInterceptTouchEvent(false)
                    } else if (scrollY == 0) {
                        // 已经滚动到顶部
                        parent.requestDisallowInterceptTouchEvent(false)
                    } else {
                        // 正在滚动中，阻止父容器拦截触摸事件
                        parent.requestDisallowInterceptTouchEvent(true)
                    }
                }
            }
        }
        return super.onTouchEvent(ev)
    }

    fun addOnScrollChange(onScrollChangedListener: OnMyScrollChangedListener){
        scrollChangedListener=onScrollChangedListener
    }

    override fun fling(velocityY: Int) {
        scrollChangedListener?.onFling(velocityY)
        super.fling(velocityY)
    }


    override fun onScrollChanged(l: Int, t: Int, oldl: Int, oldt: Int) {
        // 当前的ScrollView被触摸时，滑动其它
//        Log.i("SV", "l=$l t=$t  oldl= $oldl;  oldt==  $oldt")
        super.onScrollChanged(l, t, oldl, oldt)
        currentScrollT = t
        scrollChangedListener?.onScrollChanged(l, t, oldl, oldt)
        if (isIntercept) {
            if (t >= getChildHeight()) {
                // 已经滚动到底部
                parent.requestDisallowInterceptTouchEvent(false)
            } else if (t == 0) {
                // 已经滚动到顶部
                parent.requestDisallowInterceptTouchEvent(false)
            } else {
                // 正在滚动中，阻止父容器拦截触摸事件
                parent.requestDisallowInterceptTouchEvent(true)
            }
        }
    }

    companion object {
        val VIEW_TAG = "TableVerticalScrollView"
    }

}