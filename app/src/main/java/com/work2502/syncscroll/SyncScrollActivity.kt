package com.work2502.syncscroll

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.OverScroller
import androidx.appcompat.app.AppCompatActivity
import com.work2502.compose.databinding.ActivitySyncScrollBinding

/**
 * 同步滚动示例Activity
 * 实现两个并排的NestedScrollView进行同步的上下滚动联动效果
 */
class SyncScrollActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySyncScrollBinding

    // 用于延迟隐藏滚动指示器
    private val handler = Handler(Looper.getMainLooper())


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySyncScrollBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化示例数据
        setupSampleData()

        // 设置滚动视图联动
        setupSyncScrollViews()

        // 设置滚动指示器
        setupScrollIndicator()

        // 为左右两侧文本添加下划线
        TextViewUnderlineHelper.addUnderline(binding.leftTextView)
        TextViewUnderlineHelper.addUnderline(binding.rightTextView)
    }

    /**
     * 初始化示例数据
     */
    private fun setupSampleData() {
        // 左侧原文内容
        val originalText = "这是一行文字，用于演示同步滚动效果。".repeat(100)

        // 设置文本内容
        binding.leftTextView.setText(originalText)
        binding.rightTextView.text = originalText
    }

    /**
     * 设置滚动视图联动
     */
    private fun setupSyncScrollViews() {
        // 设置左右滚动视图互相联动
        binding.leftScrollView.addOnScrollChange(onScrollChangedListener = object : OnMyScrollChangedListener {
            override fun onFling(velocityX: Int) {
                super.onFling(velocityX)
                onFlingChild(binding.rightScrollView,velocityX)
            }

            override fun onScrollChanged(
                l: Int,
                t: Int,
                oldl: Int,
                oldt: Int
            ) {
                binding.rightScrollView.scrollTo(l, t)
            }

            override fun abort() {
                super.abort()
                binding.rightScrollView.abortScroll()
            }
        })
        binding.rightScrollView.addOnScrollChange(onScrollChangedListener = object : OnMyScrollChangedListener {

            override fun onScrollChanged(
                l: Int,
                t: Int,
                oldl: Int,
                oldt: Int
            ) {
                binding.leftScrollView.scrollTo(l, t)
            }
            override fun onFling(velocityX: Int) {
                super.onFling(velocityX)
                onFlingChild(binding.leftScrollView,velocityX)
            }
            override fun abort() {
                super.abort()
                binding.leftScrollView.abortScroll()
            }
        })
    }
    fun onFlingChild(
        scrollView: BaseTableScrollView?,
        velocityY: Int,
    ) {
        if (scrollView != null) {
            val height: Int =
                scrollView.height - scrollView.paddingTop - scrollView.paddingBottom
            val bottom: Int = scrollView.getChildAt(0).height
            var scroller: OverScroller? = null
            try {
                scroller = scrollView.scrollerField.get(scrollView) as OverScroller?
            } catch (e: IllegalAccessException) {
                e.printStackTrace()
            }
            if (scroller != null) {
                scroller.fling(
                    scrollView.scrollX, scrollView.scrollY, 0,
                    velocityY, 0, 0, 0, Math.max(0, bottom - height),
                    0, height / 2
                )
                scrollView.postInvalidateOnAnimation()
            }
        }
    }
    /**
     * 设置滚动指示器
     */
    private fun setupScrollIndicator() {
    }


    override fun onDestroy() {
        super.onDestroy()
        // 移除所有回调，避免内存泄漏
    }
}
