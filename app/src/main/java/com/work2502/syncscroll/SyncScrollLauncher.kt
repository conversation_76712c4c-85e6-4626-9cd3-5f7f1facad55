package com.work2502.syncscroll

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动SyncScrollActivity的辅助类
 */
object SyncScrollLauncher {

    /**
     * 创建一个启动SyncScrollActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): But<PERSON> {
        val button = Button(context)
        button.text = "启动同步滚动示例"
        button.setOnClickListener {
            val intent = Intent(context, SyncScrollActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动SyncScrollActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
        val intent = Intent(activity, SyncScrollActivity::class.java)
        activity.startActivity(intent)
    }
}
