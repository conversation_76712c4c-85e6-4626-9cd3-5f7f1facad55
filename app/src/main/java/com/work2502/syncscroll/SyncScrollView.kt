//package com.example.htmltodolist.syncscroll
//
//import android.content.Context
//import android.util.AttributeSet
//import android.view.View
//import androidx.core.widget.NestedScrollView
//
///**
// * 自定义的同步滚动视图
// * 实现两个滚动视图之间的滚动联动
// */
//class SyncScrollView @JvmOverloads constructor(
//    context: Context,
//    attrs: AttributeSet? = null,
//    defStyleAttr: Int = 0
//) : NestedScrollView(context, attrs, defStyleAttr) {
//
//    // 同步滚动的目标视图
//    var syncTarget: SyncScrollView? = null
//
//    // 滚动监听器
//    var onScrollChangeListener: OnScrollChangeListener? = null
//
//    // 是否正在同步滚动
//    private var isSyncing = false
//
//    // 滚动比例监听器
//    var onScrollRatioChangedListener: ((Float) -> Unit)? = null
//
//    /**
//     * 设置滚动监听
//     */
//    init {
//        // 设置滚动监听器
//        setOnScrollChangeListener { _, scrollX, scrollY, _, _ ->
//            // 如果正在同步滚动，不触发新的滚动事件，避免无限循环
//            if (isSyncing) return@setOnScrollChangeListener
//
//            // 计算滚动比例
//            val scrollRatio = calculateScrollRatio()
//
//            // 通知滚动比例变化
//            onScrollRatioChangedListener?.invoke(scrollRatio)
//
//            // 同步目标视图的滚动位置
//            syncTarget?.let { target ->
//                // 标记目标视图正在同步滚动
//                target.isSyncing = true
//
//                // 计算目标视图应该滚动到的位置
//                val targetScrollY = calculateTargetScrollY(target, scrollRatio)
//
//                // 滚动目标视图
//                target.scrollTo(scrollX, targetScrollY)
//
//                // 重置同步标记
//                target.isSyncing = false
//            }
//
//            // 调用外部设置的滚动监听器
//            onScrollChangeListener?.onScrollChange(this, scrollX, scrollY, scrollX, scrollY)
//        }
//    }
//
//    /**
//     * 计算当前滚动比例
//     * @return 滚动比例 (0.0f - 1.0f)
//     */
//    private fun calculateScrollRatio(): Float {
//        // 获取可滚动的总高度
//        val scrollRange = getScrollRange()
//        if (scrollRange <= 0) return 0f
//
//        // 计算当前滚动比例
//        return scrollY.toFloat() / scrollRange
//    }
//
//    /**
//     * 计算目标视图应该滚动到的位置
//     * @param target 目标视图
//     * @param ratio 滚动比例
//     * @return 目标滚动位置
//     */
//    private fun calculateTargetScrollY(target: SyncScrollView, ratio: Float): Int {
//        // 获取目标视图可滚动的总高度
//        val targetScrollRange = target.getScrollRange()
//
//        // 根据比例计算目标滚动位置
//        return (targetScrollRange * ratio).toInt()
//    }
//
//    /**
//     * 获取可滚动的总高度
//     * @return 可滚动的总高度
//     */
//    private fun getScrollRange(): Int {
//        var scrollRange = 0
//
//        // 如果有子视图，计算可滚动的总高度
//        if (childCount > 0) {
//            val child = getChildAt(0)
//            // 内容高度减去视图高度，即为可滚动的总高度
//            scrollRange = maxOf(0, child.height - (height - paddingBottom - paddingTop))
//        }
//
//        return scrollRange
//    }
//
//}
