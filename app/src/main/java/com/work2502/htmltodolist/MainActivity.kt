package com.work2502.htmltodolist

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Base64
import android.view.KeyEvent
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.work2502.compose.databinding.ActivityMainBinding
import com.work2502.syncscroll.SyncScrollLauncher
import com.work2502.zoomtext.view.ZoomTextLauncher
import com.work2502.zoomtext.view.PdfLikeReaderLauncher
import com.work2502.zoomtext.compose.PdfLikeReaderComposeLauncher
import com.work2502.multitouch.MultiTouchLauncher
import com.work2502.multitouch.MultiTouchGroupLauncher
import com.work2502.multitouch.MultiTouchComposeLauncher
import com.work2502.multitouch.EnhancedMultiTouchComposeLauncher
import com.work2502.multitouch.InfiniteCanvasLauncher
import com.work2502.multitouch.NestedScrollLauncher
import java.nio.charset.StandardCharsets

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var todoManager: TodoManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化TodoManager
        todoManager = TodoManager(this)

        // 添加启动EditText滚动控制示例的按钮
        addScrollableEditTextLauncherButton()

        // 设置点击监听器
        setupClickListeners()

        // 设置EditText的触摸监听器，用于处理待办事项的点击
        setupTodoListTouchListener()

        // 加载保存的待办事项列表
        loadTodoList()
    }

    /**
     * 设置各个按钮的点击监听器
     */
    private fun setupClickListeners() {
        // 添加按钮点击监听器
        binding.addButton.setOnClickListener {
            addNewTodoItem()
        }

        // 保存按钮点击监听器
        binding.saveButton.setOnClickListener {
            saveTodoList()
        }

        // 加载按钮点击监听器
        binding.loadButton.setOnClickListener {
            loadTodoList()
        }

        // 设置输入框的动作监听器，当用户按下回车键时添加待办事项
        binding.newTodoEditText.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)
            ) {
                addNewTodoItem()
                return@setOnEditorActionListener true
            }
            return@setOnEditorActionListener false
        }
    }

    /**
     * 设置WebView和JavaScript接口
     */
    @SuppressLint("SetJavaScriptEnabled", "AddJavascriptInterface")
    private fun setupTodoListTouchListener() {
        // 配置WebView
        binding.todoListWebView.apply {
            // 启用JavaScript
            settings.javaScriptEnabled = true
            // 启用DOM存储
            settings.domStorageEnabled = true
            // 设置缓存模式
            settings.cacheMode = android.webkit.WebSettings.LOAD_NO_CACHE
            // 启用混合内容
            settings.allowContentAccess = true
            // 启用文件访问
            settings.allowFileAccess = true
            // 设置默认编码
            settings.defaultTextEncodingName = "UTF-8"
            // 启用宽视图
            settings.useWideViewPort = true
            // 启用概览模式
            settings.loadWithOverviewMode = true

            // 设置WebViewClient
            webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    // 页面加载完成后的操作
                    view?.clearCache(true)
                }
            }

            // 添加JavaScript接口
            addJavascriptInterface(object {
                @JavascriptInterface
                fun toggleTodoItem(id: Long) {
                    // 在UI线程上切换待办事项状态
                    runOnUiThread {
                        if (todoManager.toggleTodoItemStatus(id)) {
                            // 更新UI
                            updateTodoListDisplay()
                        }
                    }
                }
            }, "Android")
        }
    }

    /**
     * 添加新的待办事项
     */
    private fun addNewTodoItem() {
        val content = binding.newTodoEditText.text.toString().trim()
        if (content.isNotEmpty()) {
            // 添加新的待办事项
            todoManager.addTodoItem(content)

            // 清空输入框
            binding.newTodoEditText.text.clear()

            // 更新UI
            updateTodoListDisplay()

            Toast.makeText(this, "已添加新的待办事项", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "请输入待办事项内容", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 更新待办事项列表显示
     */
    private fun updateTodoListDisplay() {
        try {
            // 生成完整的HTML内容
            val htmlContent = generateCompleteHtml()

            // 使用Base64编码加载 HTML内容
            val encodedHtml = Base64.encodeToString(
                htmlContent.toByteArray(StandardCharsets.UTF_8),
                Base64.NO_PADDING
            )

            // 将HTML内容加载到WebView
            binding.todoListWebView.loadData(encodedHtml, "text/html", "base64")
        } catch (e: Exception) {
            // 如果出错，尝试直接加载
            val simpleHtml = "<html><body><h1>待办事项列表</h1><p>加载失败: ${e.message}</p></body></html>"
            binding.todoListWebView.loadData(simpleHtml, "text/html", "UTF-8")
            e.printStackTrace()
        }
    }

    /**
     * 生成完整的HTML内容
     */
    private fun generateCompleteHtml(): String {
        val sb = StringBuilder()

        // HTML头部
        sb.append("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        padding: 10px;
                        background-color: #f9f9f9;
                    }
                    ul {
                        list-style-type: none;
                        padding-left: 0;
                        margin: 0;
                    }
                    li {
                        margin-bottom: 10px;
                        padding: 10px;
                        border-radius: 5px;
                        background-color: white;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                        display: flex;
                        align-items: center;
                    }
                    .completed {
                        text-decoration: line-through;
                        color: #888888;
                    }
                    .checkbox {
                        display: inline-flex;
                        justify-content: center;
                        align-items: center;
                        width: 24px;
                        height: 24px;
                        border: 2px solid #666;
                        border-radius: 3px;
                        margin-right: 10px;
                        cursor: pointer;
                        background-color: #fff;
                        flex-shrink: 0;
                    }
                    .checkbox.checked {
                        background-color: #4CAF50;
                        border-color: #4CAF50;
                        color: white;
                    }
                    .todo-content {
                        flex-grow: 1;
                        font-size: 16px;
                    }
                    .status-label {
                        margin-left: 8px;
                        font-size: 12px;
                        color: green;
                    }
                    .empty-list {
                        text-align: center;
                        color: #888;
                        font-style: italic;
                        padding: 20px;
                    }
                </style>
                <script type="text/javascript">
                    function toggleTodo(id) {
                        // 调用Android接口切换待办事项状态
                        if (window.Android) {
                            Android.toggleTodoItem(id);
                        }
                    }
                </script>
            </head>
            <body>
        """)

        // 添加列表开始标签
        sb.append("<ul>")

        // 添加每个待办事项
        val todoItems = todoManager.getAllTodoItems()
        if (todoItems.isNotEmpty()) {
            todoItems.forEach { todoItem ->
                val checkboxHtml = if (todoItem.isCompleted) {
                    "<div class='checkbox checked' onclick='toggleTodo(${todoItem.id})'>&#10004;</div>"
                } else {
                    "<div class='checkbox' onclick='toggleTodo(${todoItem.id})'></div>"
                }

                val contentClass = if (todoItem.isCompleted) "completed" else ""

                sb.append("""
                    <li>
                        $checkboxHtml
                        <span class='todo-content $contentClass'>${todoItem.text}</span>
                        ${if (todoItem.isCompleted) "<span class='status-label'>(已完成)</span>" else ""}
                    </li>
                """)
            }
        } else {
            // 如果没有待办事项，显示提示信息
            sb.append("<div class='empty-list'>暂无待办事项，请添加新的待办事项</div>")
        }

        // 添加列表结束标签
        sb.append("</ul>")

        // HTML尾部
        sb.append("""
            </body>
            </html>
        """)

        return sb.toString()
    }

    /**
     * 保存待办事项列表
     */
    private fun saveTodoList() {
        if (todoManager.saveTodoList()) {
            Toast.makeText(this, "待办事项列表已保存", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "保存失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 加载待办事项列表
     */
    private fun loadTodoList() {
        if (todoManager.loadTodoList()) {
            updateTodoListDisplay()
            Toast.makeText(this, "待办事项列表已加载", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "没有找到保存的待办事项列表", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 添加启动EditText滚动控制示例的按钮
     */
    private fun addScrollableEditTextLauncherButton() {
        // 创建一个新的按钮
        val launcherButton = Button(this).apply {
            text = "启动EditText滚动控制示例"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动ScrollableEditTextActivity
                ScrollableEditTextLauncher.launch(this@MainActivity)
            }
        }

        // 创建待办事项列表编辑器启动按钮
        val todoListButton = Button(this).apply {
            text = "启动待办事项列表编辑器"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动TodoListActivity
                TodoListLauncher.launch(this@MainActivity)
            }
        }

        // 创庯Compose待办事项列表编辑器启动按钮
        val todoListComposeButton = Button(this).apply {
            text = "启动Compose待办事项列表编辑器"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动TodoListComposeActivity
                TodoListComposeLauncher.launch(this@MainActivity)
            }
        }

        // 创建同步滚动示例启动按钮
        val syncScrollButton = Button(this).apply {
            text = "启动同步滚动示例"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动SyncScrollActivity
                SyncScrollLauncher.launch(this@MainActivity)
            }
        }
        val textZoomButton = Button(this).apply {
            text = "文字放大效果"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动SyncScrollActivity
                ZoomTextLauncher.launch(this@MainActivity)
            }
        }

        // 创建PDF阅读器示例启动按钮
        val pdfReaderButton = Button(this).apply {
            text = "PDF阅读器示例"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动PdfLikeReaderActivity
                PdfLikeReaderLauncher.launch(this@MainActivity)
            }
        }

        // 创建PDF阅读器Compose版启动按钮
        val pdfReaderComposeButton = Button(this).apply {
            text = "PDF阅读器Compose版"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动PdfLikeReaderComposeActivity
                PdfLikeReaderComposeLauncher.launch(this@MainActivity)
            }
        }

        // 将按钮添加到底部按钮容器中
        binding.buttonContainer.addView(launcherButton)
        binding.buttonContainer.addView(todoListButton)
        binding.buttonContainer.addView(todoListComposeButton)
        binding.buttonContainer.addView(syncScrollButton)
        binding.buttonContainer.addView(textZoomButton)
        binding.buttonContainer.addView(pdfReaderButton)
        binding.buttonContainer.addView(pdfReaderComposeButton)
        binding.buttonContainer.addView(MultiTouchLauncher.createLauncherButton(this))
        binding.buttonContainer.addView(MultiTouchGroupLauncher.createLauncherButton(this))

        // 创建多触摸Compose示例启动按钮
        val multiTouchComposeButton = Button(this).apply {
            text = "多触摸Compose示例"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动MultiTouchComposeActivity
                MultiTouchComposeLauncher.launch(this@MainActivity)
            }
        }
        binding.buttonContainer.addView(multiTouchComposeButton)

        // 创建增强版多触摸Compose示例启动按钮
        val enhancedMultiTouchComposeButton = Button(this).apply {
            text = "增强版多触摸Compose示例"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动EnhancedMultiTouchComposeActivity
                EnhancedMultiTouchComposeLauncher.launch(this@MainActivity)
            }
        }
        binding.buttonContainer.addView(enhancedMultiTouchComposeButton)

        // 创建无限画布示例启动按钮
        val infiniteCanvasButton = Button(this).apply {
            text = "无限画布示例"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动InfiniteCanvasActivity
                InfiniteCanvasLauncher.launch(this@MainActivity)
            }
        }
        binding.buttonContainer.addView(infiniteCanvasButton)

        // 创建嵌套滚动示例启动按钮
        val nestedScrollButton = Button(this).apply {
            text = "嵌套滚动示例"
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 16, 0, 0)
            }
            setOnClickListener {
                // 启动NestedScrollActivity
                NestedScrollLauncher.launch(this@MainActivity)
            }
        }
        binding.buttonContainer.addView(nestedScrollButton)
    }
}
