package com.work2502.htmltodolist

import android.os.Bundle
import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.work2502.compose.databinding.ActivityScrollableEdittextBinding

/**
 * 演示如何使用代码控制长文本的EditText滚动
 */
class ScrollableEditTextActivity : AppCompatActivity() {

    private lateinit var binding: ActivityScrollableEdittextBinding

    // 示例文本，包含多行
    private val sampleText = buildString {
        append("这是一个演示EditText滚动控制的示例。\n\n")
        for (i in 1..100) {
            append("这是第 $i 行文本，用于演示滚动控制功能。\n")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityScrollableEdittextBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化UI
        setupUI()

        // 设置按钮点击监听器
        setupClickListeners()
    }

    /**
     * 初始化UI
     */
    private fun setupUI() {
        // 设置EditText的初始文本
        binding.longTextEditText.setText(sampleText)

        // 确保EditText可以滚动
        binding.longTextEditText.isVerticalScrollBarEnabled = true

        // 设置滚动条样式
        binding.longTextEditText.scrollBarStyle = android.view.View.SCROLLBARS_INSIDE_OVERLAY

        // 设置滚动模式
        binding.longTextEditText.overScrollMode = android.view.View.OVER_SCROLL_ALWAYS

        // 设置硬件加速
        binding.longTextEditText.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)

        // 设置文本缓冲类型
        binding.longTextEditText.setTextIsSelectable(true)

        // 禁用编辑，只允许滚动和选择
        binding.longTextEditText.isEnabled = true
        binding.longTextEditText.isFocusable = true
        binding.longTextEditText.isFocusableInTouchMode = true
        binding.longTextEditText.isLongClickable = true
        binding.longTextEditText.isCursorVisible = true
        binding.longTextEditText.keyListener = null  // 禁止编辑，但允许选择
    }

    /**
     * 设置按钮点击监听器
     */
    private fun setupClickListeners() {
        // 滚动到顶部按钮
        binding.scrollTopButton.setOnClickListener {
            scrollToTop()
        }

        // 滚动到底部按钮
        binding.scrollBottomButton.setOnClickListener {
            scrollToBottom()
        }

        // 滚动到指定行按钮
        binding.scrollToLineButton.setOnClickListener {
            val lineNumberStr = binding.lineNumberEditText.text.toString()
            if (lineNumberStr.isNotEmpty()) {
                try {
                    val lineNumber = lineNumberStr.toInt()
                    scrollToLine(lineNumber)
                } catch (e: NumberFormatException) {
                    Toast.makeText(this, "请输入有效的行号", Toast.LENGTH_SHORT).show()
                }
            } else {
                Toast.makeText(this, "请输入行号", Toast.LENGTH_SHORT).show()
            }
        }

        // 向上滚动按钮
        binding.scrollUpButton.setOnClickListener {
            scrollUp()
        }

        // 向下滚动按钮
        binding.scrollDownButton.setOnClickListener {
            scrollDown()
        }

        // 加载示例文本按钮
        binding.loadSampleTextButton.setOnClickListener {
            binding.longTextEditText.setText(sampleText)
            Toast.makeText(this, "已加载示例文本", Toast.LENGTH_SHORT).show()
        }

        // 搜索按钮
        binding.searchButton.setOnClickListener {
            performSearch()
        }

        // 搜索输入框回车键监听
        binding.searchEditText.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)
            ) {
                performSearch()
                return@setOnEditorActionListener true
            }
            return@setOnEditorActionListener false
        }
    }

    /**
     * 滚动到顶部
     */
    private fun scrollToTop() {
        // 使用平滑滚动
        binding.longTextEditText.post {
            smoothScrollTo(0)
            Toast.makeText(this, "已滚动到顶部", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 滚动到底部
     */
    private fun scrollToBottom() {
        binding.longTextEditText.post {
            val layout = binding.longTextEditText.layout
            if (layout != null) {
                val scrollAmount = layout.getLineTop(layout.lineCount) - binding.longTextEditText.height
                if (scrollAmount > 0) {
                    // 使用平滑滚动
                    smoothScrollTo(scrollAmount)
                } else {
                    smoothScrollTo(0)
                }
                Toast.makeText(this, "已滚动到底部", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 滚动到指定行
     * @param lineNumber 行号（从1开始）
     */
    private fun scrollToLine(lineNumber: Int) {
        binding.longTextEditText.post {
            val layout = binding.longTextEditText.layout
            if (layout != null) {
                // 确保行号在有效范围内
                val validLineNumber = lineNumber.coerceIn(1, layout.lineCount)

                // 计算要滚动到的Y坐标
                val scrollY = layout.getLineTop(validLineNumber - 1)

                // 执行平滑滚动
                smoothScrollTo(scrollY)

                // 高亮显示当前行
                highlightLine(validLineNumber - 1)

                Toast.makeText(this, "已滚动到第 $validLineNumber 行", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 高亮显示指定行
     * @param lineIndex 行索引（从0开始）
     */
    private fun highlightLine(lineIndex: Int) {
        try {
            val layout = binding.longTextEditText.layout ?: return
            if (lineIndex < 0 || lineIndex >= layout.lineCount) return

            val text = binding.longTextEditText.text
            val lineStart = layout.getLineStart(lineIndex)
            val lineEnd = layout.getLineEnd(lineIndex)

            // 设置选中区域
            binding.longTextEditText.requestFocus()
            binding.longTextEditText.setSelection(lineStart, lineEnd)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 向上滚动一页
     */
    private fun scrollUp() {
        binding.longTextEditText.post {
            val layout = binding.longTextEditText.layout
            if (layout != null) {
                // 获取当前滚动位置
                val scrollY = binding.longTextEditText.scrollY

                // 计算一页的高度（EditText的高度）
                val pageHeight = binding.longTextEditText.height

                // 计算新的滚动位置（向上滚动一页）
                val newScrollY = (scrollY - pageHeight).coerceAtLeast(0)

                // 执行平滑滚动
                smoothScrollTo(newScrollY)

                // 显示当前可见的行号
                val firstVisibleLine = getFirstVisibleLineNumber()
                Toast.makeText(this, "向上滚动一页，当前第 $firstVisibleLine 行", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 向下滚动一页
     */
    private fun scrollDown() {
        binding.longTextEditText.post {
            val layout = binding.longTextEditText.layout
            if (layout != null) {
                // 获取当前滚动位置
                val scrollY = binding.longTextEditText.scrollY

                // 计算一页的高度（EditText的高度）
                val pageHeight = binding.longTextEditText.height

                // 计算文本的总高度
                val totalHeight = layout.getLineTop(layout.lineCount)

                // 计算新的滚动位置（向下滚动一页）
                val newScrollY = (scrollY + pageHeight).coerceAtMost(totalHeight - pageHeight)

                // 执行平滑滚动
                smoothScrollTo(newScrollY)

                // 显示当前可见的行号
                binding.longTextEditText.postDelayed({
                    val firstVisibleLine = getFirstVisibleLineNumber()
                    Toast.makeText(this, "向下滚动一页，当前第 $firstVisibleLine 行", Toast.LENGTH_SHORT).show()
                }, 300) // 等待滚动完成
            }
        }
    }

    /**
     * 高级滚动控制：滚动到包含特定文本的位置
     * @param searchText 要搜索的文本
     */
    private fun scrollToText(searchText: String) {
        binding.longTextEditText.post {
            val text = binding.longTextEditText.text.toString()
            val index = text.indexOf(searchText)

            if (index != -1) {
                val layout = binding.longTextEditText.layout
                if (layout != null) {
                    // 找到文本所在的行
                    val line = layout.getLineForOffset(index)

                    // 获取该行的顶部位置
                    val scrollY = layout.getLineTop(line)

                    // 执行平滑滚动
                    smoothScrollTo(scrollY)

                    // 高亮显示匹配文本
                    binding.longTextEditText.postDelayed({
                        binding.longTextEditText.setSelection(index, index + searchText.length)
                        Toast.makeText(this, "已滚动到匹配文本位置", Toast.LENGTH_SHORT).show()
                    }, 300) // 等待滚动完成
                }
            } else {
                Toast.makeText(this, "未找到匹配文本", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 高级滚动控制：平滑滚动到指定位置
     * @param y 要滚动到的Y坐标
     */
    private fun smoothScrollTo(y: Int) {
        // 获取当前滚动位置
        val startY = binding.longTextEditText.scrollY
        val distance = y - startY

        // 创建动画器
        val animator = android.animation.ValueAnimator.ofInt(startY, y)
        animator.duration = 300 // 滚动时间，可以根据距离调整
        animator.interpolator = android.view.animation.DecelerateInterpolator()

        animator.addUpdateListener { valueAnimator ->
            val value = valueAnimator.animatedValue as Int
            binding.longTextEditText.scrollTo(0, value)
        }

        animator.start()
    }

    /**
     * 获取当前可见的第一行行号
     * @return 当前可见的第一行行号（从1开始）
     */
    private fun getFirstVisibleLineNumber(): Int {
        val layout = binding.longTextEditText.layout
        if (layout != null) {
            val scrollY = binding.longTextEditText.scrollY
            return layout.getLineForVertical(scrollY) + 1
        }
        return 1
    }

    /**
     * 获取当前可见的最后一行行号
     * @return 当前可见的最后一行行号（从1开始）
     */
    private fun getLastVisibleLineNumber(): Int {
        val layout = binding.longTextEditText.layout
        if (layout != null) {
            val scrollY = binding.longTextEditText.scrollY
            val visibleHeight = binding.longTextEditText.height
            return layout.getLineForVertical(scrollY + visibleHeight) + 1
        }
        return 1
    }

    /**
     * 执行搜索
     */
    private fun performSearch() {
        val searchText = binding.searchEditText.text.toString().trim()
        if (searchText.isEmpty()) {
            Toast.makeText(this, "请输入搜索内容", Toast.LENGTH_SHORT).show()
            return
        }

        // 隐藏键盘
        val imm = getSystemService(INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
        imm.hideSoftInputFromWindow(binding.searchEditText.windowToken, 0)

        // 执行搜索并滚动
        scrollToText(searchText)
    }
}
