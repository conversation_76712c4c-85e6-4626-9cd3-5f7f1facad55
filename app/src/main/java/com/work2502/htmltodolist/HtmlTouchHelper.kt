package com.work2502.htmltodolist

import android.text.Spannable
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.MotionEvent
import android.widget.TextView

/**
 * HTML触摸辅助类，用于处理HTML内容中的点击事件
 */
class HtmlTouchHelper : LinkMovementMethod() {
    
    companion object {
        private var instance: HtmlTouchHelper? = null
        
        /**
         * 获取HtmlTouchHelper实例
         * @return HtmlTouchHelper实例
         */
        fun getInstance(): HtmlTouchHelper {
            if (instance == null) {
                instance = HtmlTouchHelper()
            }
            return instance!!
        }
    }
    
    /**
     * 处理触摸事件
     */
    override fun onTouchEvent(widget: TextView, buffer: Spannable, event: MotionEvent): Boolean {
        val action = event.action
        
        if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_DOWN) {
            val x = event.x.toInt() - widget.totalPaddingLeft + widget.scrollX
            val y = event.y.toInt() - widget.totalPaddingTop + widget.scrollY
            
            val layout = widget.layout
            val line = layout.getLineForVertical(y)
            val off = layout.getOffsetForHorizontal(line, x.toFloat())
            
            // 获取点击位置的ClickableSpan
            val links = buffer.getSpans(off, off, ClickableSpan::class.java)
            
            if (links.isNotEmpty()) {
                if (action == MotionEvent.ACTION_UP) {
                    // 点击事件
                    links[0].onClick(widget)
                }
                return true
            }
        }
        
        return super.onTouchEvent(widget, buffer, event)
    }
}
