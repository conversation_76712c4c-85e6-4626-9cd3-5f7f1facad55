package com.work2502.htmltodolist

import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.work2502.compose.databinding.ActivityTodoListBinding

/**
 * 待办事项列表示例Activity
 */
class TodoListActivity : AppCompatActivity() {

    private lateinit var binding: ActivityTodoListBinding
    
    // 示例待办事项
    private val sampleTodoItems = listOf(
        "完成Android项目",
        "学习Kotlin协程",
        "阅读设计模式书籍",
        "锻炼身体",
        "准备明天的会议"
    )
    
    // 当前示例项目索引
    private var currentSampleIndex = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTodoListBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 设置按钮点击监听器
        setupClickListeners()
        
        // 添加初始待办事项
        addInitialTodoItems()
    }

    /**
     * 设置按钮点击监听器
     */
    private fun setupClickListeners() {
        // 添加示例项目按钮
        binding.addItemButton.setOnClickListener {
            addSampleTodoItem()
        }
        
        // 清空列表按钮
        binding.clearButton.setOnClickListener {
            clearTodoList()
        }
    }

    /**
     * 添加初始待办事项
     */
    private fun addInitialTodoItems() {
        // 添加一个初始待办事项
        binding.todoListEditText.addTodoItem("点击复选框可以标记完成状态")
        
        // 添加一个已完成的待办事项
        binding.todoListEditText.addTodoItem("这是一个已完成的待办事项", true)
        
        // 添加提示
        binding.todoListEditText.addTodoItem("按回车键可以添加新的待办事项")
    }

    /**
     * 添加示例待办事项
     */
    private fun addSampleTodoItem() {
        if (currentSampleIndex < sampleTodoItems.size) {
            // 添加示例待办事项
            binding.todoListEditText.addTodoItem(sampleTodoItems[currentSampleIndex])
            
            // 更新索引
            currentSampleIndex = (currentSampleIndex + 1) % sampleTodoItems.size
            
            // 显示提示
            Toast.makeText(this, "已添加示例待办事项", Toast.LENGTH_SHORT).show()
        } else {
            // 重置索引
            currentSampleIndex = 0
            addSampleTodoItem()
        }
    }

    /**
     * 清空待办事项列表
     */
    private fun clearTodoList() {
        binding.todoListEditText.clearTodoItems()
        Toast.makeText(this, "已清空待办事项列表", Toast.LENGTH_SHORT).show()
    }
}
