package com.work2502.htmltodolist

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.text.Editable
import android.text.Spannable
import android.text.TextWatcher
import android.text.style.ImageSpan
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputConnection
import android.view.inputmethod.InputConnectionWrapper
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.content.ContextCompat
import com.work2502.compose.R

/**
 * 自定义的待办事项列表编辑器
 * 支持复选框和待办事项列表功能
 */
class TodoListEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.editTextStyle
) : AppCompatEditText(context, attrs, defStyleAttr) {

    // 复选框图标
    private val uncheckedDrawable: Drawable?
    private val checkedDrawable: Drawable?

    // 复选框大小
    private val checkboxSize: Int

    // 复选框和文本之间的间距
    private val checkboxPadding: Int

    // 行高
    private val lineHeight: Int

    // 存储每行的复选框状态
    private val checkboxStates = mutableMapOf<Int, Boolean>()

    // 文本变化监听器
    private val textWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            // 不需要实现
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            // 不需要实现
        }

        override fun afterTextChanged(s: Editable?) {
            ensureCheckboxesAtLineStart()
        }
    }

    init {
        // 初始化复选框图标
        uncheckedDrawable = ContextCompat.getDrawable(context, R.drawable.checkbox_unchecked)
        checkedDrawable = ContextCompat.getDrawable(context, R.drawable.checkbox_checked)

        // 设置复选框大小
        checkboxSize = resources.getDimensionPixelSize(R.dimen.checkbox_size)

        // 设置复选框和文本之间的间距
        checkboxPadding = resources.getDimensionPixelSize(R.dimen.checkbox_padding)

        // 设置行高
        lineHeight = resources.getDimensionPixelSize(R.dimen.todo_line_height)

        // 设置内边距，为复选框留出空间
        setPadding(
            checkboxSize + checkboxPadding * 2,
            paddingTop,
            paddingRight,
            paddingBottom
        )

        // 添加文本变化监听器
        addTextChangedListener(textWatcher)
    }

    /**
     * 确保每行开头都有复选框
     */
    private fun ensureCheckboxesAtLineStart() {
        val text = text ?: return
        val layout = layout ?: return

        // 移除文本变化监听器，避免递归调用
        removeTextChangedListener(textWatcher)

        val editable = text as Editable

        // 遍历每一行
        for (i in 0 until layout.lineCount) {
            val lineStart = layout.getLineStart(i)
            val lineEnd = layout.getLineEnd(i)

            // 如果这一行是空行，跳过
            if (lineStart == lineEnd) continue

            // 检查这一行是否已经有复选框
            val hasCheckbox = editable.getSpans(lineStart, lineStart + 1, ImageSpan::class.java).isNotEmpty()

            if (!hasCheckbox) {
                // 获取复选框状态，默认为未选中
                val isChecked = checkboxStates[i] ?: false

                // 创建复选框图标
                val drawable = if (isChecked) checkedDrawable else uncheckedDrawable
                drawable?.setBounds(0, 0, checkboxSize, checkboxSize)

                // 创建ImageSpan
                val imageSpan = drawable?.let { CheckboxImageSpan(it, i) }

                // 在行首插入空格，并添加ImageSpan
                if (imageSpan != null) {
                    editable.insert(lineStart, " ")
                    editable.setSpan(imageSpan, lineStart, lineStart + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
            }
        }

        // 重新添加文本变化监听器
        addTextChangedListener(textWatcher)
    }

    /**
     * 处理触摸事件，检测复选框点击
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_UP) {
            val layout = layout ?: return super.onTouchEvent(event)

            // 获取点击位置
            val x = event.x.toInt()
            val y = event.y.toInt()

            // 检查是否点击了复选框区域
            if (x < checkboxSize + checkboxPadding * 2) {
                // 获取点击的行
                val line = layout.getLineForVertical(y)
                val lineStart = layout.getLineStart(line)

                // 获取该行的复选框
                val checkboxSpans = (text as Spannable).getSpans(lineStart, lineStart + 1, CheckboxImageSpan::class.java)

                if (checkboxSpans.isNotEmpty()) {
                    // 切换复选框状态
                    val checkboxSpan = checkboxSpans[0]
                    val isChecked = checkboxStates[checkboxSpan.lineIndex] ?: false
                    checkboxStates[checkboxSpan.lineIndex] = !isChecked

                    // 更新复选框图标
                    val drawable = if (!isChecked) checkedDrawable else uncheckedDrawable
                    drawable?.setBounds(0, 0, checkboxSize, checkboxSize)
                    if (drawable != null) {
                        checkboxSpan.checkboxDrawable = drawable
                    }

                    // 刷新视图
                    invalidate()

                    return true
                }
            }
        }

        return super.onTouchEvent(event)
    }

    /**
     * 处理按键事件，特别是回车键
     */
    override fun onCreateInputConnection(outAttrs: EditorInfo): InputConnection {
        val connection = super.onCreateInputConnection(outAttrs)

        return object : InputConnectionWrapper(connection, true) {
            override fun sendKeyEvent(event: KeyEvent): Boolean {
                if (event.action == KeyEvent.ACTION_DOWN && event.keyCode == KeyEvent.KEYCODE_ENTER) {
                    // 在当前位置插入换行符
                    val editable = text ?: return super.sendKeyEvent(event)
                    val selectionStart = selectionStart

                    // 插入换行符
                    editable.insert(selectionStart, "\n")

                    // 确保新行有复选框
                    post {
                        ensureCheckboxesAtLineStart()
                    }

                    return true
                }

                return super.sendKeyEvent(event)
            }
        }
    }

    /**
     * 自定义的ImageSpan，用于绘制复选框
     */
    inner class CheckboxImageSpan(
        var checkboxDrawable: Drawable?,
        val lineIndex: Int
    ) : ImageSpan(checkboxDrawable!!) {

        override fun draw(
            canvas: Canvas,
            text: CharSequence,
            start: Int,
            end: Int,
            x: Float,
            top: Int,
            y: Int,
            bottom: Int,
            paint: Paint
        ) {
            val drawableObj = checkboxDrawable ?: return

            // 计算复选框的位置
            val transY = (bottom - top - drawableObj.bounds.height()) / 2 + top

            // 保存画布状态
            canvas.save()

            // 绘制复选框
            canvas.translate(x, transY.toFloat())
            drawableObj.draw(canvas)

            // 恢复画布状态
            canvas.restore()
        }
    }

    /**
     * 添加一个新的待办事项
     * @param text 待办事项文本
     * @param isChecked 是否已完成
     */
    fun addTodoItem(text: String, isChecked: Boolean = false) {
        val currentText = this.text?.toString() ?: ""
        val newText = if (currentText.isEmpty()) {
            text
        } else {
            "$currentText\n$text"
        }

        // 设置新文本
        setText(newText)

        // 更新最后一行的复选框状态
        val layout = layout ?: return
        val lastLine = layout.lineCount - 1
        checkboxStates[lastLine] = isChecked

        // 确保所有行都有复选框
        ensureCheckboxesAtLineStart()
    }

    /**
     * 获取所有待办事项
     * @return 待办事项列表，每个元素是一个Pair，包含文本和是否已完成
     */
    fun getTodoItems(): List<Pair<String, Boolean>> {
        val result = mutableListOf<Pair<String, Boolean>>()
        val text = text?.toString() ?: return result
        val layout = layout ?: return result

        // 遍历每一行
        for (i in 0 until layout.lineCount) {
            val lineStart = layout.getLineStart(i)
            val lineEnd = layout.getLineEnd(i)

            // 跳过空行
            if (lineStart == lineEnd) continue

            // 获取行文本，去除复选框
            var lineText = text.substring(lineStart, lineEnd).trim()
            if (lineText.startsWith(" ")) {
                lineText = lineText.substring(1)
            }

            // 获取复选框状态
            val isChecked = checkboxStates[i] ?: false

            result.add(Pair(lineText, isChecked))
        }

        return result
    }

    /**
     * 清空所有待办事项
     */
    fun clearTodoItems() {
        setText("")
        checkboxStates.clear()
    }
}
