package com.work2502.htmltodolist

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动TodoListComposeActivity的辅助类
 */
object TodoListComposeLauncher {

    /**
     * 创建一个启动TodoListComposeActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): Button {
        val button = Button(context)
        button.text = "启动Compose待办事项列表编辑器"
        button.setOnClickListener {
            val intent = Intent(context, TodoListComposeActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动TodoListComposeActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
        val intent = Intent(activity, TodoListComposeActivity::class.java)
        activity.startActivity(intent)
    }
}
