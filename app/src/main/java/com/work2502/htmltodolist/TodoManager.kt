package com.work2502.htmltodolist

import android.content.Context
import android.text.Html
import android.text.Spanned
import android.util.Log
import java.io.*

/**
 * 待办事项管理器，负责管理待办事项列表和HTML格式化
 */
class TodoManager(private val context: Context) {

    companion object {
        private const val TAG = "TodoManager"
        private const val FILENAME = "todo_list.dat"
    }

    // 待办事项列表
    private val todoItems = mutableListOf<TodoItem>()

    /**
     * 添加新的待办事项
     * @param content 待办事项内容
     * @return 新添加的待办事项
     */
    fun addTodoItem(content: String): TodoItem {
        val id = System.currentTimeMillis()
        val todoItem = TodoItem(id, content)
        todoItems.add(todoItem)
        return todoItem
    }

    /**
     * 切换待办事项的完成状态
     * @param id 待办事项ID
     * @return 是否成功切换状态
     */
    fun toggleTodoItemStatus(id: Long): Boolean {
        val todoItem = todoItems.find { it.id == id }
        return if (todoItem != null) {
            todoItem.isCompleted = !todoItem.isCompleted
            true
        } else {
            false
        }
    }

    /**
     * 获取所有待办事项
     * @return 待办事项列表
     */
    fun getAllTodoItems(): List<TodoItem> {
        return todoItems.toList()
    }

    /**
     * 将待办事项列表转换为HTML格式，使用li元素和checkbox
     * @return HTML格式的待办事项列表
     */
    fun getHtmlFormattedTodoList(): Spanned {
        val stringBuilder = StringBuilder()

        // 添加HTML头部、CSS样式和JavaScript代码
        stringBuilder.append("""
            <html>
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        padding: 10px;
                        background-color: #f9f9f9;
                    }
                    ul {
                        list-style-type: none;
                        padding-left: 0;
                        margin: 0;
                    }
                    li {
                        margin-bottom: 10px;
                        padding: 10px;
                        border-radius: 5px;
                        background-color: white;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                        display: flex;
                        align-items: center;
                    }
                    .completed {
                        text-decoration: line-through;
                        color: #888888;
                    }
                    .checkbox {
                        display: inline-flex;
                        justify-content: center;
                        align-items: center;
                        width: 24px;
                        height: 24px;
                        border: 2px solid #666;
                        border-radius: 3px;
                        margin-right: 10px;
                        cursor: pointer;
                        background-color: #fff;
                        flex-shrink: 0;
                    }
                    .checkbox.checked {
                        background-color: #4CAF50;
                        border-color: #4CAF50;
                        color: white;
                    }
                    .todo-content {
                        flex-grow: 1;
                        font-size: 16px;
                    }
                    .status-label {
                        margin-left: 8px;
                        font-size: 12px;
                        color: green;
                    }
                    .empty-list {
                        text-align: center;
                        color: #888;
                        font-style: italic;
                        padding: 20px;
                    }
                </style>
                <script type="text/javascript">
                    function toggleTodo(id) {
                        // 调用Android接口切换待办事项状态
                        if (window.Android) {
                            Android.toggleTodoItem(id);
                        }
                    }
                </script>
            </head>
            <body>
        """)

        // 添加列表开始标签
        stringBuilder.append("<ul>")

        // 添加每个待办事项
        todoItems.forEachIndexed { index, todoItem ->
            val checkboxHtml = if (todoItem.isCompleted) {
                "<div class='checkbox checked' data-id='${todoItem.id}' onclick='toggleTodo(${todoItem.id})'>&#10004;</div>"
            } else {
                "<div class='checkbox' data-id='${todoItem.id}' onclick='toggleTodo(${todoItem.id})'></div>"
            }

            val contentClass = if (todoItem.isCompleted) "completed" else ""

            val itemHtml = """
                <li>
                    $checkboxHtml
                    <span class='todo-content $contentClass'>${todoItem.text}</span>
                    ${if (todoItem.isCompleted) "<span class='status-label'>(已完成)</span>" else ""}
                </li>
            """

            stringBuilder.append(itemHtml)
        }

        // 添加列表结束标签
        stringBuilder.append("</ul>")

        // 如果没有待办事项，显示提示信息
        if (todoItems.isEmpty()) {
            stringBuilder.append("<div class='empty-list'>暂无待办事项，请添加新的待办事项</div>")
        }

        // 添加HTML尾部
        stringBuilder.append("</body></html>")

        // 将HTML字符串转换为Spanned对象
        return Html.fromHtml(stringBuilder.toString(), Html.FROM_HTML_MODE_COMPACT)
    }

    /**
     * 保存待办事项列表到文件
     * @return 是否保存成功
     */
    fun saveTodoList(): Boolean {
        return try {
            val fileOutputStream = context.openFileOutput(FILENAME, Context.MODE_PRIVATE)
            val objectOutputStream = ObjectOutputStream(fileOutputStream)
            objectOutputStream.writeObject(ArrayList(todoItems))
            objectOutputStream.close()
            fileOutputStream.close()
            Log.d(TAG, "待办事项列表保存成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存待办事项列表失败", e)
            false
        }
    }

    /**
     * 从文件加载待办事项列表
     * @return 是否加载成功
     */
    @Suppress("UNCHECKED_CAST")
    fun loadTodoList(): Boolean {
        return try {
            val file = context.getFileStreamPath(FILENAME)
            if (file.exists()) {
                val fileInputStream = context.openFileInput(FILENAME)
                val objectInputStream = ObjectInputStream(fileInputStream)
                val loadedItems = objectInputStream.readObject() as ArrayList<TodoItem>
                objectInputStream.close()
                fileInputStream.close()

                todoItems.clear()
                todoItems.addAll(loadedItems)
                Log.d(TAG, "待办事项列表加载成功，共${todoItems.size}项")
                true
            } else {
                Log.d(TAG, "待办事项文件不存在")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载待办事项列表失败", e)
            false
        }
    }

    /**
     * 根据点击位置查找待办事项ID
     * @param html HTML文本
     * @param position 点击位置
     * @return 待办事项ID，如果未找到则返回null
     */
    fun findTodoItemIdByPosition(html: String, position: Int): Long? {
        // 查找包含data-id属性的div标签（复选框）
        val checkboxRegex = "<div[^>]*class='checkbox[^']*'[^>]*data-id='(\\d+)'[^>]*>".toRegex()
        val checkboxMatches = checkboxRegex.findAll(html)

        for (match in checkboxMatches) {
            val divStart = match.range.first
            val divEnd = html.indexOf("</div>", divStart) + 6

            if (position in divStart..divEnd) {
                // 提取data-id属性值
                val idMatch = "data-id='(\\d+)'".toRegex().find(match.value)
                return idMatch?.groupValues?.get(1)?.toLongOrNull()
            }
        }

        // 如果没有找到复选框，则查找整个li元素
        val liRegex = "<li>[\\s\\S]*?data-id='(\\d+)'[\\s\\S]*?</li>".toRegex()
        val liMatches = liRegex.findAll(html)

        for (match in liMatches) {
            val liStart = match.range.first
            val liEnd = match.range.last + 1

            if (position in liStart..liEnd) {
                // 提取data-id属性值
                val idMatch = "data-id='(\\d+)'".toRegex().find(match.value)
                return idMatch?.groupValues?.get(1)?.toLongOrNull()
            }
        }

        return null
    }
}
