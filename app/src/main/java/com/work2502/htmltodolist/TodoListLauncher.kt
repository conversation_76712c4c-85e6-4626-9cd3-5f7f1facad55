package com.work2502.htmltodolist

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动TodoListActivity的辅助类
 */
object TodoListLauncher {

    /**
     * 创建一个启动TodoListActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): Button {
        val button = Button(context)
        button.text = "启动待办事项列表编辑器"
        button.setOnClickListener {
            val intent = Intent(context, TodoListActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动TodoListActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
        val intent = Intent(activity, TodoListActivity::class.java)
        activity.startActivity(intent)
    }
}
