package com.work2502.htmltodolist

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.outlined.CheckCircle
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEvent
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 待办事项数据类
 */
data class TodoItem(
    val id: Long = System.currentTimeMillis(),
    var text: String = "",
    var isCompleted: Boolean = false
)

@Preview
@Composable
fun TodoListEditText(modifier: Modifier = Modifier) {
    // 使用可变状态列表存储待办事项
    val todoItems = remember { mutableStateListOf<TodoItem>() }

    // 如果列表为空，添加一个空的待办事项
    if (todoItems.isEmpty()) {
        todoItems.add(TodoItem())
    }

    // 创建焦点请求器，用于在添加新项目后设置焦点
    val focusRequesters = remember {
        List(100) { FocusRequester() } // 预创建足够多的焦点请求器
    }

    Surface(
        modifier = modifier
            .fillMaxHeight(0.8f)
            .padding(16.dp),
        shadowElevation = 4.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .padding(16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            // 显示所有待办事项
            todoItems.forEachIndexed { index, todoItem ->
                TodoItemRow(
                    todoItem = todoItem,
                    focusRequester = focusRequesters[index],
                    isLastItem = index == todoItems.size - 1,
                    onToggleComplete = {
                        todoItems[index] = todoItem.copy(isCompleted = !todoItem.isCompleted)
                    },
                    onTextChange = { newText ->
                        todoItems[index] = todoItem.copy(text = newText)
                    },
                    onEnterPressed = {
                        // 在当前项目后添加新的待办事项
                        todoItems.add(index + 1, TodoItem())
                        // 请求焦点到新添加的项目
                        focusRequesters[index + 1].requestFocus()
                    }
                )

                if (index < todoItems.size - 1) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

@Composable
fun TodoItemRow(
    todoItem: TodoItem,
    focusRequester: FocusRequester,
    isLastItem: Boolean,
    onToggleComplete: () -> Unit,
    onTextChange: (String) -> Unit,
    onEnterPressed: () -> Unit
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        // 复选框
        IconButton(onClick = onToggleComplete) {
            Icon(
                imageVector = if (todoItem.isCompleted) {
                    Icons.Filled.CheckCircle
                } else {
                    Icons.Outlined.CheckCircle
                },
                contentDescription = if (todoItem.isCompleted) "标记为未完成" else "标记为已完成",
                tint = if (todoItem.isCompleted) Color(0xFF4CAF50) else Color.Gray
            )
        }

        // 文本输入框
        var textFieldValue by remember { mutableStateOf(TextFieldValue(todoItem.text)) }

        BasicTextField(
            value = textFieldValue,
            onValueChange = {
                textFieldValue = it
                onTextChange(it.text)
            },
            modifier = Modifier
                .weight(1f)
                .focusRequester(focusRequester)
                .onKeyEvent { keyEvent: KeyEvent ->
                    // 检测回车键
                    if (keyEvent.key == Key.Enter) {
                        onEnterPressed()
                        true
                    } else {
                        false
                    }
                },
            textStyle = TextStyle(
                fontSize = 16.sp,
                color = if (todoItem.isCompleted) Color.Gray else Color.Black,
                textDecoration = if (todoItem.isCompleted) TextDecoration.LineThrough else null
            ),
            cursorBrush = SolidColor(Color.Black),
            decorationBox = { innerTextField ->
                Box {
                    // 如果文本为空且是最后一项，显示提示文字
                    if (todoItem.text.isEmpty() && isLastItem) {
                        Text(
                            text = "按回车键添加新的待办事项",
                            color = Color.Gray
                        )
                    }
                    innerTextField()
                }
            }
        )
    }
}