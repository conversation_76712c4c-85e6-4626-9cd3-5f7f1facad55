package com.work2502.htmltodolist

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动ScrollableEditTextActivity的辅助类
 */
object ScrollableEditTextLauncher {

    /**
     * 创建一个启动ScrollableEditTextActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): Button {
        val button = Button(context)
        button.text = "启动EditText滚动控制示例"
        button.setOnClickListener {
            val intent = Intent(context, ScrollableEditTextActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动ScrollableEditTextActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
        val intent = Intent(activity, ScrollableEditTextActivity::class.java)
        activity.startActivity(intent)
    }
}
