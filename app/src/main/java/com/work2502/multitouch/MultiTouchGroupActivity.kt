package com.work2502.multitouch

import android.os.Bundle
import android.view.ViewGroup
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.work2502.compose.R

/**
 * 展示多触摸交互ViewGroup的Activity
 */
class MultiTouchGroupActivity : AppCompatActivity() {

    private lateinit var multiTouchViewGroup: MultiTouchViewGroup
    private lateinit var resetButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_multi_touch_group)

        // 初始化视图
        multiTouchViewGroup = findViewById(R.id.multiTouchViewGroup)
        resetButton = findViewById(R.id.resetButton)

        // 设置缩放范围
        multiTouchViewGroup.setScaleRange(0.5f, 5.0f)

        // 添加子视图
        addChildViews()

        // 设置重置按钮点击事件
        resetButton.setOnClickListener {
            multiTouchViewGroup.reset()
            Toast.makeText(this, "视图已重置", Toast.LENGTH_SHORT).show()
        }

        // 显示使用提示
        Toast.makeText(
            this,
            "使用单指拖动可滑动内容\n双指捏合可缩放内容\n快速滑动可触发惯性滚动\n双击可重置视图",
            Toast.LENGTH_LONG
        ).show()
    }

    /**
     * 添加子视图
     */
    private fun addChildViews() {
        // 添加一个ImageView
        val imageView = ImageView(this).apply {
            layoutParams = FrameLayout.LayoutParams(
                RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                width = 400
                height = 400
                leftMargin = 100
                topMargin = 100
            }
            setImageDrawable(ContextCompat.getDrawable(this@MultiTouchGroupActivity, R.drawable.ic_launcher_background))
            scaleType = ImageView.ScaleType.FIT_XY
        }
//        multiTouchViewGroup.addView(imageView)

        // 添加一个TextView
        val textView = TextView(this).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            ).apply {
                width = 600
                height = 300
                leftMargin = 600
                topMargin = 200
            }
            text = "这是一个可以缩放和滚动的TextView。\n\n" +
                    "您可以使用单指拖动来滚动内容，\n" +
                    "使用双指捏合来缩放内容，\n" +
                    "快速滑动可以触发惯性滚动，\n" +
                    "双击可以重置视图。"
            textSize = 18f
            setBackgroundColor(ContextCompat.getColor(this@MultiTouchGroupActivity, android.R.color.holo_blue_light))
            setPadding(20, 20, 20, 20)
        }
        multiTouchViewGroup.addView(textView)

//        // 添加一个Button
//        val button = Button(this).apply {
//            layoutParams = FrameLayout.LayoutParams(
//                FrameLayout.LayoutParams.WRAP_CONTENT,
//                FrameLayout.LayoutParams.WRAP_CONTENT
//            ).apply {
//                leftMargin = 300
//                topMargin = 600
//            }
//            text = "点击我"
//            setOnClickListener {
//                Toast.makeText(this@MultiTouchGroupActivity, "按钮被点击了", Toast.LENGTH_SHORT).show()
//            }
//        }
//        multiTouchViewGroup.addView(button)
//
//        // 添加另一个ImageView
//        val imageView2 = ImageView(this).apply {
//            layoutParams = FrameLayout.LayoutParams(
//                FrameLayout.LayoutParams.WRAP_CONTENT,
//                FrameLayout.LayoutParams.WRAP_CONTENT
//            ).apply {
//                width = 300
//                height = 300
//                leftMargin = 800
//                topMargin = 600
//            }
//            setImageDrawable(ContextCompat.getDrawable(this@MultiTouchGroupActivity, R.drawable.ic_launcher_foreground))
//            scaleType = ImageView.ScaleType.FIT_CENTER
//        }
//        multiTouchViewGroup.addView(imageView2)
    }
}
