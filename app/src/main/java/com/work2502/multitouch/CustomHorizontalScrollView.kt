package com.work2502.multitouch

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.HorizontalScrollView
import kotlin.math.abs

/**
 * 自定义HorizontalScrollView，改善与NestedScrollView嵌套时的滑动体验
 */
class CustomHorizontalScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : HorizontalScrollView(context, attrs, defStyleAttr) {

    // 上一次触摸事件的x和y坐标
    private var lastX = 0f
    private var lastY = 0f
    
    // 是否正在水平滚动
    private var isHorizontalScrolling = false
    
    // 触摸滑动阈值
    private val touchSlop = android.view.ViewConfiguration.get(context).scaledTouchSlop
    
    /**
     * 拦截触摸事件
     * 根据滑动方向决定是否拦截事件
     */
    override fun onInterceptTouchEvent(ev: MotionEvent): <PERSON><PERSON><PERSON> {
        when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 记录按下位置
                lastX = ev.x
                lastY = ev.y
                // 重置状态
                isHorizontalScrolling = false
                // 请求父视图不要拦截
                parent.requestDisallowInterceptTouchEvent(true)
            }
            
            MotionEvent.ACTION_MOVE -> {
                // 计算移动距离
                val deltaX = abs(ev.x - lastX)
                val deltaY = abs(ev.y - lastY)
                
                // 如果水平移动距离明显大于垂直移动距离，则拦截事件
                if (deltaX > touchSlop && deltaX > deltaY * 1.5f) {
                    isHorizontalScrolling = true
                    parent.requestDisallowInterceptTouchEvent(true)
                    return true
                } 
                // 如果垂直移动距离明显大于水平移动距离，则不拦截事件
                else if (deltaY > touchSlop && deltaY > deltaX * 1.5f) {
                    isHorizontalScrolling = false
                    parent.requestDisallowInterceptTouchEvent(false)
                    return false
                }
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                // 重置状态
                isHorizontalScrolling = false
                parent.requestDisallowInterceptTouchEvent(false)
            }
        }
        
        return super.onInterceptTouchEvent(ev)
    }
    
    /**
     * 处理触摸事件
     */
    override fun onTouchEvent(ev: MotionEvent): Boolean {
        when (ev.actionMasked) {
            MotionEvent.ACTION_MOVE -> {
                // 计算移动距离
                val deltaX = abs(ev.x - lastX)
                val deltaY = abs(ev.y - lastY)
                
                // 更新最后位置
                lastX = ev.x
                lastY = ev.y
                
                // 如果垂直移动距离明显大于水平移动距离，则让父视图处理
                if (deltaY > touchSlop && deltaY > deltaX * 1.5f && !isHorizontalScrolling) {
                    parent.requestDisallowInterceptTouchEvent(false)
                }
            }
        }
        
        return super.onTouchEvent(ev)
    }
}
