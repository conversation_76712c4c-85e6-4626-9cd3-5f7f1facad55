package com.work2502.multitouch

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动NestedScrollActivity的辅助类
 */
object NestedScrollLauncher {

    /**
     * 创建一个启动NestedScrollActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): But<PERSON> {
        val button = Button(context)
        button.text = "嵌套滚动示例"
        button.setOnClickListener {
            val intent = Intent(context, NestedScrollActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动NestedScrollActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
//        val intent = Intent(activity, NestedScrollActivity::class.java)
        val intent = Intent(activity, NestedScrollEditTextActivity::class.java)
        activity.startActivity(intent)
    }
}
