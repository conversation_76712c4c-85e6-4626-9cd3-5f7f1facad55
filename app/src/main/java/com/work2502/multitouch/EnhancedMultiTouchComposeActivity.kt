package com.work2502.multitouch

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.work2502.compose.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

/**
 * 展示增强版多触摸Compose组件的Activity
 */
class EnhancedMultiTouchComposeActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            MaterialTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    EnhancedMultiTouchComposeScreen()
                }
            }
        }
    }
}

/**
 * 增强版多触摸Compose示例屏幕
 */
@Composable
fun EnhancedMultiTouchComposeScreen() {
    // 创建可缩放状态
    val zoomableState = rememberEnhancedZoomableState(
        minScale = 0.5f,
        maxScale = 5f,
        enableRotation = false
    )
    
    // 获取协程作用域
    val coroutineScope = rememberCoroutineScope()
    
    // 是否显示控制面板
    var showControls by remember { mutableStateOf(true) }
    
    // 是否启用惯性滚动
    var enableInertia by remember { mutableStateOf(true) }
    
    // 是否启用双击缩放
    var enableDoubleTapZoom by remember { mutableStateOf(true) }
    
    // 是否启用旋转
    var enableRotation by remember { mutableStateOf(false) }
    
    // 自动隐藏控制面板
    LaunchedEffect(Unit) {
        delay(5000)
        showControls = false
    }
    
    Scaffold(
        topBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.primaryContainer)
                    .padding(16.dp)
            ) {
                Text(
                    text = "增强版多触摸Compose示例",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 显示当前缩放比例和偏移量
                Text(
                    text = "缩放比例: ${(zoomableState.scale * 100).roundToInt() / 100f}x",
                    fontSize = 14.sp
                )
                
                Text(
                    text = "偏移量: (${(zoomableState.offset.x * 100).roundToInt() / 100f}, ${(zoomableState.offset.y * 100).roundToInt() / 100f})",
                    fontSize = 14.sp
                )
                
                if (enableRotation) {
                    Text(
                        text = "旋转角度: ${(zoomableState.rotation * 100).roundToInt() / 100f}°",
                        fontSize = 14.sp
                    )
                }
            }
        },
        bottomBar = {
            AnimatedVisibility(
                visible = showControls,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.9f))
                        .padding(16.dp)
                ) {
                    // 控制选项
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "启用惯性滚动",
                            modifier = Modifier.weight(1f)
                        )
                        Switch(
                            checked = enableInertia,
                            onCheckedChange = { enableInertia = it }
                        )
                    }
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "启用双击缩放",
                            modifier = Modifier.weight(1f)
                        )
                        Switch(
                            checked = enableDoubleTapZoom,
                            onCheckedChange = { enableDoubleTapZoom = it }
                        )
                    }
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "启用旋转",
                            modifier = Modifier.weight(1f)
                        )
                        Switch(
                            checked = enableRotation,
                            onCheckedChange = {
                                enableRotation = it
                                // 更新状态
                                zoomableState.apply {
                                    coroutineScope.launch {
                                        if (!it) {
                                            // 如果禁用旋转，重置旋转角度
                                            updateRotation(0f)
                                        }
                                    }
                                }
                            }
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 缩放滑块
                    Text(text = "缩放: ${(zoomableState.scale * 100).roundToInt() / 100f}x")
                    Slider(
                        value = zoomableState.scale,
                        onValueChange = {
                            coroutineScope.launch {
                                zoomableState.updateScale(it)
                            }
                        },
                        valueRange = zoomableState.minScale..zoomableState.maxScale,
                        steps = 100
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Button(
                            onClick = {
                                coroutineScope.launch {
                                    zoomableState.reset()
                                }
                            }
                        ) {
                            Text(text = "重置")
                        }
                        
                        Button(
                            onClick = {
                                showControls = !showControls
                            }
                        ) {
                            Text(text = if (showControls) "隐藏控制" else "显示控制")
                        }
                    }
                }
            }
        }
    ) { paddingValues ->
        // 主内容区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color.LightGray.copy(alpha = 0.3f))
                .clickable(onClick = { showControls = !showControls })
        ) {
            // 使用增强版ZoomableBox组件
            EnhancedZoomableBox(
                modifier = Modifier.fillMaxSize(),
                state = zoomableState,
                enableInertia = enableInertia,
                enableDoubleTapToZoom = enableDoubleTapZoom,
                onTap = {
                    // 点击时切换控制面板显示状态
                    showControls = !showControls
                }
            ) {
                // ZoomableBox的内容
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 图片卡片
                    Card(
                        modifier = Modifier
                            .size(200.dp)
                            .border(2.dp, MaterialTheme.colorScheme.primary, RoundedCornerShape(8.dp)),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_launcher_background),
                            contentDescription = "示例图片",
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop
                        )
                    }
                    
                    // 文本卡片
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(8.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        Text(
                            text = "这是一个可以缩放和滚动的文本。\n\n" +
                                    "您可以使用双指捏合来缩放内容，\n" +
                                    "使用单指拖动来滚动内容。\n\n" +
                                    "尝试放大后滑动查看内容的边缘部分。\n\n" +
                                    "点击屏幕可以显示或隐藏控制面板。",
                            modifier = Modifier.padding(16.dp),
                            fontSize = 16.sp
                        )
                    }
                    
                    // 按钮
                    Button(
                        onClick = {
                            // 显示点击消息
                            // 在实际应用中，您可以使用Toast或其他方式显示消息
                        }
                    ) {
                        Text(text = "点击我")
                    }
                    
                    // 另一个图片
                    Image(
                        painter = painterResource(id = R.drawable.ic_launcher_foreground),
                        contentDescription = "另一个示例图片",
                        modifier = Modifier
                            .size(150.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .border(1.dp, Color.Gray, RoundedCornerShape(8.dp))
                    )
                }
            }
            
            // 使用提示
            AnimatedVisibility(
                visible = !showControls,
                enter = fadeIn(),
                exit = fadeOut(),
                modifier = Modifier.align(Alignment.BottomCenter)
            ) {
                Text(
                    text = "点击屏幕显示控制面板",
                    modifier = Modifier
                        .padding(bottom = 16.dp)
                        .background(Color.Black.copy(alpha = 0.5f), RoundedCornerShape(4.dp))
                        .padding(8.dp),
                    color = Color.White,
                    fontSize = 14.sp
                )
            }
        }
    }
}

/**
 * 可点击修饰符
 */
fun Modifier.clickable(onClick: () -> Unit): Modifier = this.pointerInput(Unit) {
    detectTapGestures(onTap = { onClick() })
}

/**
 * 预览
 */
@Preview(showBackground = true)
@Composable
fun EnhancedMultiTouchComposeScreenPreview() {
    MaterialTheme {
        EnhancedMultiTouchComposeScreen()
    }
}

/**
 * 用于启动EnhancedMultiTouchComposeActivity的辅助类
 */
object EnhancedMultiTouchComposeLauncher {
    /**
     * 启动EnhancedMultiTouchComposeActivity
     */
    fun launch(activity: androidx.appcompat.app.AppCompatActivity) {
        val intent = android.content.Intent(activity, EnhancedMultiTouchComposeActivity::class.java)
        activity.startActivity(intent)
    }
}
