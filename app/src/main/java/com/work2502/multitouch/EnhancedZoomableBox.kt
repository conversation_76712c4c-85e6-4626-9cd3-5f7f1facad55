package com.work2502.multitouch

import android.widget.EditText
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.SpringSpec
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.tween
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.calculateCentroid
import androidx.compose.foundation.gestures.calculatePan
import androidx.compose.foundation.gestures.calculateRotation
import androidx.compose.foundation.gestures.calculateZoom
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.RenderEffect
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.positionChange
import androidx.compose.ui.input.pointer.positionChanged
import androidx.compose.ui.input.pointer.util.VelocityTracker
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.Velocity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.max

/**
 * 增强版可缩放Box的状态
 */
@Stable
class EnhancedZoomableState(
    initialScale: Float = 1f,
    initialOffset: Offset = Offset.Zero,
    initialRotation: Float = 0f,
    val minScale: Float = 0.8f,
    val maxScale: Float = 1.25f,
    val enableRotation: Boolean = false
) {
    // 缩放动画
    private val scaleAnimatable = Animatable(initialScale)

    // 偏移动画
    private val offsetAnimatable = Animatable(
        initialOffset,
        Offset.VectorConverter
    )

    // 旋转动画
    private val rotationAnimatable = Animatable(initialRotation)

    // 当前缩放值
    var scale by mutableStateOf(initialScale)
        private set

    // 当前偏移值
    var offset by mutableStateOf(initialOffset)
        private set

    // 当前旋转值
    var rotation by mutableStateOf(initialRotation)
        private set

    // 容器大小
    var containerSize by mutableStateOf(IntSize.Zero)
        internal set

    // 内容大小
    var contentSize by mutableStateOf(IntSize.Zero)
        internal set

    // 是否正在变换
    var isTransforming by mutableStateOf(false)
        private set

    // 是否正在动画中
    var isAnimating by mutableStateOf(false)
        private set

    // 速度追踪器
    private val velocityTracker = VelocityTracker()

    // 惯性滚动作业
    private var flingJob: Job? = null

    /**
     * 更新缩放值
     */
    suspend fun updateScale(
        newScale: Float,
        animationSpec: AnimationSpec<Float> = SpringSpec(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessLow
        )
    ) {
        isAnimating = true
        scaleAnimatable.animateTo(
            targetValue = newScale.coerceIn(minScale, maxScale),
            animationSpec = animationSpec
        ) {
            scale = value
        }
        isAnimating = false
    }

    /**
     * 更新偏移值
     */
    suspend fun updateOffset(
        newOffset: Offset,
        animationSpec: AnimationSpec<Offset> = SpringSpec(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessLow
        )
    ) {
        isAnimating = true
        offsetAnimatable.animateTo(
            targetValue = constrainOffset(newOffset),
            animationSpec = animationSpec
        ) {
            offset = value
        }
        isAnimating = false
    }

    /**
     * 更新旋转值
     */
    suspend fun updateRotation(
        newRotation: Float,
        animationSpec: AnimationSpec<Float> = SpringSpec(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessLow
        )
    ) {
        if (!enableRotation) return

        isAnimating = true
        rotationAnimatable.animateTo(
            targetValue = newRotation,
            animationSpec = animationSpec
        ) {
            rotation = value
        }
        isAnimating = false
    }

    /**
     * 重置所有变换
     */
    suspend fun reset(
        animationSpec: AnimationSpec<Float> = tween(300)
    ) {
        isAnimating = true

        // 同时启动所有动画
        kotlinx.coroutines.coroutineScope {
            launch {
                scaleAnimatable.animateTo(1f, animationSpec) {
                    scale = value
                }
            }

            launch {
                offsetAnimatable.animateTo(Offset.Zero, tween(300)) {
                    offset = value
                }
            }

            if (enableRotation) {
                launch {
                    rotationAnimatable.animateTo(0f, animationSpec) {
                        rotation = value
                    }
                }
            }
        }

        isAnimating = false
    }

    /**
     * 应用变换
     */
    internal fun applyTransformation(
        centroid: Offset,
        pan: Offset,
        zoom: Float,
        rotation: Float
    ) {
        isTransforming = true

        // 应用缩放
        val newScale = (scale * zoom).coerceIn(minScale, maxScale)

        // 应用平移
        val newOffset = offset + pan

        // 应用旋转
        val newRotation = if (enableRotation) {
            this.rotation + rotation
        } else {
            this.rotation
        }

        // 更新状态
        this.scale = newScale
        this.offset = constrainOffset(newOffset)
        this.rotation = newRotation
    }

    /**
     * 结束变换
     */
    internal fun endTransformation() {
        isTransforming = false
    }

    /**
     * 约束偏移量
     */
    private fun constrainOffset(offset: Offset): Offset {
        if (containerSize == IntSize.Zero || contentSize == IntSize.Zero) {
            return offset
        }

        // 计算内容的有效区域
        val contentWidth = contentSize.width * scale
        val contentHeight = contentSize.height * scale

        // 如果内容小于容器，则居中显示
        if (contentWidth <= containerSize.width && contentHeight <= containerSize.height) {
            return Offset.Zero
        }

        // 计算最大可滑动范围
        val maxX = max(0f, (contentWidth - containerSize.width) / 2)
        val maxY = max(0f, (contentHeight - containerSize.height) / 2)

        // 约束X轴偏移
        val constrainedX = offset.x.coerceIn(-maxX, maxX)
        // 约束Y轴偏移
        val constrainedY = offset.y.coerceIn(-maxY, maxY)

        return Offset(constrainedX, constrainedY)
    }

    /**
     * 处理惯性滚动
     */
    internal fun processFling(
        velocity: Velocity,
        scope: CoroutineScope,
        decayAnimationSpec: AnimationSpec<Offset> = SpringSpec(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessLow
        )
    ) {
        // 取消之前的惯性滚动
        flingJob?.cancel()

        // 如果速度太小，不处理惯性滚动
        if (abs(velocity.x) < 50f && abs(velocity.y) < 50f) {
            return
        }

        // 启动新的惯性滚动
        flingJob = scope.launch {
            isAnimating = true

            // 计算目标偏移
            val targetOffsetX = offset.x + velocity.x / 20f
            val targetOffsetY = offset.y + velocity.y / 20f

            // 动画到目标偏移
            updateOffset(Offset(x = targetOffsetX, y = targetOffsetY), decayAnimationSpec)

            isAnimating = false
        }
    }

    /**
     * 添加速度样本
     */
    internal fun addPosition(timeMillis: Long, position: Offset) {
        velocityTracker.addPosition(timeMillis, position)
    }

    /**
     * 计算当前速度
     */
    internal fun calculateVelocity(): Velocity {
        return velocityTracker.calculateVelocity()
    }

    /**
     * 重置速度追踪器
     */
    internal fun resetVelocityTracker() {
        velocityTracker.resetTracking()
    }
}

/**
 * 记住可缩放状态
 */
@Composable
fun rememberEnhancedZoomableState(
    initialScale: Float = 1f,
    initialOffset: Offset = Offset.Zero,
    initialRotation: Float = 0f,
    minScale: Float = 0.8f,
    maxScale: Float = 1.3f,
    enableRotation: Boolean = false
): EnhancedZoomableState {
    return remember {
        EnhancedZoomableState(
            initialScale = initialScale,
            initialOffset = initialOffset,
            initialRotation = initialRotation,
            minScale = minScale,
            maxScale = maxScale,
            enableRotation = enableRotation
        )
    }
}

/**
 * 增强版可缩放Box组件，优化EditText光标处理
 */
@Composable
fun EnhancedZoomableBox(
    modifier: Modifier = Modifier,
    state: EnhancedZoomableState = rememberEnhancedZoomableState(),
    enableInertia: Boolean = true,
    enableDoubleTapToZoom: Boolean = true,
    onTap: (() -> Unit)? = null,
    onDoubleTap: (() -> Unit)? = null,
    onLongPress: (() -> Unit)? = null,
    onScaleChanged: ((Float) -> Unit)? = null,
    content: @Composable BoxScope.() -> Unit
) {
    // 获取协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 获取当前密度
    val density = LocalDensity.current

    // 跟踪当前焦点的EditText
    var focusedEditText by remember { mutableStateOf<EditText?>(null) }

    // 监听缩放变化
    LaunchedEffect(state.scale, state.offset) {
        onScaleChanged?.invoke(state.scale)

        // 当缩放或偏移变化时，重新设置光标位置
        focusedEditText?.let { editText ->
            // 延迟一下，确保变换已应用
            delay(100)

            if (editText.hasFocus()) {
                // 保存当前选择位置
                val selStart = editText.selectionStart
                val selEnd = editText.selectionEnd

                if (selStart >= 0 && selEnd >= 0) {
                    // 强制刷新EditText的布局和光标
                    editText.post {
                        // 临时失去焦点再重新获得焦点，强制重绘光标
                        editText.clearFocus()
                        editText.requestFocus()
                        editText.setSelection(selStart, selEnd)

                        // 确保光标可见
                        editText.invalidate()
                    }
                }
            }
        }
    }

    // 创建Box组件
    Box(
        modifier = modifier
            .clipToBounds() // 裁剪超出边界的内容
            .onSizeChanged { state.containerSize = it } // 记录容器大小
            .pointerInput(Unit) {
                // 检测点击手势
                detectTapGestures(
                    onTap = {
                        // 检查是否点击了EditText
                        focusedEditText = null
                        onTap?.invoke()
                    },
                    onDoubleTap = {
                        if (enableDoubleTapToZoom) {
                            coroutineScope.launch {
                                if (state.scale > 1f) {
                                    // 如果已经放大，则重置
                                    state.reset()
                                } else {
                                    // 否则放大到2倍
                                    state.updateScale(2f)
                                }
                            }
                        }
                        onDoubleTap?.invoke()
                    },
                    onLongPress = { onLongPress?.invoke() }
                )
            }
            .pointerInput(Unit) {
                // 自定义手势检测
                awaitEachGesture {
                    // 等待第一个按下事件
                    val down = awaitFirstDown(requireUnconsumed = false)
                    state.resetVelocityTracker()
                    state.addPosition(down.uptimeMillis, down.position)

                    // 是否处于变换状态
                    var transforming = false
                    var pastTouchSlop = false

                    // 上一次的指针数量
                    var previousPointerCount = 1

                    // 处理后续事件
                    do {
                        val event = awaitPointerEvent()
                        val pointerCount = event.changes.size

                        // 如果指针数量变化，重置状态
                        if (pointerCount != previousPointerCount) {
                            transforming = false
                            pastTouchSlop = false
                        }
                        previousPointerCount = pointerCount

                        // 处理多指触摸
                        if (pointerCount > 1) {
                            // 计算手势参数
                            val centroid = event.calculateCentroid()
                            val pan = event.calculatePan()
                            val zoom = event.calculateZoom().coerceIn(0.8f, 1.25f)
                            val rotation = event.calculateRotation()

                            // 如果超过触摸阈值或已经在变换中
                            if (!pastTouchSlop) {
                                pastTouchSlop = abs(zoom - 1f) > 0.01f || pan.getDistance() > 5f
                            }

                            if (pastTouchSlop) {
                                transforming = true

                                // 应用变换
                                state.applyTransformation(
                                    centroid = centroid,
                                    pan = pan,
                                    zoom = zoom,
                                    rotation = rotation
                                )

                                // 消费事件
                                event.changes.forEach { it.consume() }
                            }
                        } else if (pointerCount == 1) {
                            // 处理单指触摸
                            val change = event.changes[0]

                            // 如果位置改变且已经超过触摸阈值或在变换中
                            if (change.positionChanged()) {
                                if (!pastTouchSlop) {
                                    pastTouchSlop = change.positionChange().getDistance() > 5f
                                }

                                if (pastTouchSlop) {
                                    transforming = true

                                    // 应用平移
                                    state.applyTransformation(
                                        centroid = change.position,
                                        pan = change.positionChange(),
                                        zoom = 1f,
                                        rotation = 0f
                                    )

                                    // 记录位置用于计算速度
                                    state.addPosition(change.uptimeMillis, change.position)

                                    // 消费事件
                                    change.consume()
                                }
                            }
                        }
                    } while (event.changes.any { it.pressed })

                    // 结束变换
                    state.endTransformation()

                    // 如果启用惯性滚动且正在变换
                    if (enableInertia && transforming) {
                        // 计算速度
                        val velocity = state.calculateVelocity()

                        // 处理惯性滚动
                        state.processFling(velocity, coroutineScope)
                    }
                }
            },
        content = {
            // 创建内容容器
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .onSizeChanged { state.contentSize = it } // 记录内容大小
                    .graphicsLayer {
                        // 应用变换
                        scaleX = state.scale
                        scaleY = state.scale
                        translationX = state.offset.x
                        translationY = state.offset.y
                        rotationZ = state.rotation

                        // 设置变换原点为中心
                        transformOrigin = androidx.compose.ui.graphics.TransformOrigin(0.5f, 0.5f)

                        // 设置裁剪模式
                        clip = true
                    }
            ) {
                // 渲染内容
                CompositionLocalProvider(
                    LocalEditTextHandler provides { editText ->
                        // 当EditText获得焦点时记录它
                        editText.setOnFocusChangeListener { view, hasFocus ->
                            if (hasFocus) {
                                focusedEditText = view as EditText
                            } else if (focusedEditText == view) {
                                focusedEditText = null
                            }
                        }

                        // 添加文本变化监听器，确保光标可见
                        editText.addTextChangedListener(object : android.text.TextWatcher {
                            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                            override fun afterTextChanged(s: android.text.Editable?) {
                                if (editText.hasFocus()) {
                                    // 保存当前选择位置
                                    val selStart = editText.selectionStart
                                    val selEnd = editText.selectionEnd
                                    if (selStart >= 0 && selEnd >= 0) {
                                        // 延迟一下，确保布局已更新
                                        editText.post {
                                            // 重新设置选择位置，触发光标重绘
                                            editText.setSelection(selStart, selEnd)
                                        }
                                    }
                                }
                            }
                        })

                        // 设置自定义的EditText属性
                        editText.apply {
                            // 禁用EditText的过度滚动效果
                            overScrollMode = android.view.View.OVER_SCROLL_NEVER
                            // 设置IME选项，防止键盘弹出时自动滚动
                            imeOptions = imeOptions or android.view.inputmethod.EditorInfo.IME_FLAG_NO_EXTRACT_UI

                            // 添加布局变化监听器，处理缩放后的光标位置
                            addOnLayoutChangeListener { view, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
                                if (hasFocus() && (left != oldLeft || top != oldTop || right != oldRight || bottom != oldBottom)) {
                                    // 布局发生变化时，强制刷新光标位置
                                    post {
                                        val selStart = selectionStart
                                        val selEnd = selectionEnd
                                        if (selStart >= 0 && selEnd >= 0) {
                                            // 清除选择然后重新设置，强制光标重绘
                                            clearFocus()
                                            requestFocus()
                                            setSelection(selStart, selEnd)
                                        }
                                    }
                                }
                            }
                        }
                    }
                ) {
                    content()
                }
            }
        }
    )
}

/**
 * 创建一个CompositionLocal来处理EditText
 */
private val LocalEditTextHandler = compositionLocalOf<(EditText) -> Unit> { { _ -> } }

/**
 * 扩展函数，用于在Compose中处理EditText
 */
@Composable
fun Modifier.handleEditText(): Modifier = composed {
    val editTextHandler = LocalEditTextHandler.current
    val rootView = LocalView.current

    this.then(
        Modifier.composed {
            onGloballyPositioned { coordinates ->
                // 查找所有子视图中的EditText
                findEditTextsInView(rootView).forEach { editText ->
                    editTextHandler(editText)
                }
            }
        }
    )
}

/**
 * 递归查找视图中的所有EditText
 */
private fun findEditTextsInView(view: android.view.View): List<EditText> {
    val result = mutableListOf<EditText>()

    if (view is EditText) {
        result.add(view)
    } else if (view is android.view.ViewGroup) {
        for (i in 0 until view.childCount) {
            result.addAll(findEditTextsInView(view.getChildAt(i)))
        }
    }

    return result
}