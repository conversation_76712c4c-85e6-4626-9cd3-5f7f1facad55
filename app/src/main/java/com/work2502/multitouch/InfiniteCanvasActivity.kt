package com.work2502.multitouch

import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.work2502.compose.R

/**
 * 展示无限画布ViewGroup的Activity
 */
class InfiniteCanvasActivity : AppCompatActivity() {

    private lateinit var infiniteCanvasViewGroup: InfiniteCanvasViewGroup
    private lateinit var resetButton: Button
    private lateinit var toggleBoundaryButton: Button
    private lateinit var infoTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_infinite_canvas)

        // 初始化视图
        infiniteCanvasViewGroup = findViewById(R.id.infiniteCanvasViewGroup)
        resetButton = findViewById(R.id.resetButton)
        toggleBoundaryButton = findViewById(R.id.toggleBoundaryButton)
        infoTextView = findViewById(R.id.infoTextView)

        // 设置重置按钮点击事件
        resetButton.setOnClickListener {
            infiniteCanvasViewGroup.reset()
            updateInfoText()
            Toast.makeText(this, "视图已重置", Toast.LENGTH_SHORT).show()
        }

        // 设置切换边界限制按钮点击事件
        toggleBoundaryButton.setOnClickListener {
            val enableBoundary = infiniteCanvasViewGroup.getTag(R.id.tag_enable_boundary) as? Boolean ?: false
            infiniteCanvasViewGroup.setEnableBoundaryLimit(enableBoundary)
            infiniteCanvasViewGroup.setTag(R.id.tag_enable_boundary, enableBoundary)
            toggleBoundaryButton.text = if (enableBoundary) "禁用边界限制" else "启用边界限制"
            Toast.makeText(this, if (enableBoundary) "已启用边界限制" else "已禁用边界限制", Toast.LENGTH_SHORT).show()
        }

        // 添加子视图
        addChildViews()

        // 显示使用提示
        Toast.makeText(
            this,
            "使用单指拖动可滑动内容\n快速滑动可触发惯性滚动\n双击可重置视图",
            Toast.LENGTH_LONG
        ).show()
    }

    /**
     * 添加子视图
     */
    private fun addChildViews() {
        // 添加一个大的网格背景
        val gridSize = 3000
        val gridSpacing = 100
        val gridView = View(this).apply {
            layoutParams = ViewGroup.LayoutParams(gridSize, gridSize)
            setBackgroundResource(R.drawable.grid_background)
            alpha = 0.3f
        }
        infiniteCanvasViewGroup.addView(gridView)

        // 添加中心标记
        val centerMark = TextView(this).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            text = "中心点 (0,0)"
            setTextColor(ContextCompat.getColor(this@InfiniteCanvasActivity, android.R.color.black))
            setBackgroundColor(ContextCompat.getColor(this@InfiniteCanvasActivity, android.R.color.white))
            setPadding(16, 8, 16, 8)
            x = gridSize / 2f - 50
            y = gridSize / 2f - 20
        }
        infiniteCanvasViewGroup.addView(centerMark)

        // 添加四个角落的标记
        val cornerSize = 100f
        val cornerMargin = 50f

        // 左上角
        addCornerMark("左上角", cornerMargin, cornerMargin)

        // 右上角
        addCornerMark("右上角", gridSize - cornerSize - cornerMargin, cornerMargin)

        // 左下角
        addCornerMark("左下角", cornerMargin, gridSize - cornerSize - cornerMargin)

        // 右下角
        addCornerMark("右下角", gridSize - cornerSize - cornerMargin, gridSize - cornerSize - cornerMargin)

        // 添加一些随机位置的按钮
        for (i in 0 until 10) {
            val x = (Math.random() * (gridSize - 200)).toFloat() + 100
            val y = (Math.random() * (gridSize - 200)).toFloat() + 100

            val button = Button(this).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                text = "按钮 $i"
                this.x = x
                this.y = y
                setOnClickListener {
                    Toast.makeText(this@InfiniteCanvasActivity, "点击了按钮 $i", Toast.LENGTH_SHORT).show()
                }
            }
            infiniteCanvasViewGroup.addView(button)
        }

        // 初始化位置，将视图居中
        infiniteCanvasViewGroup.post {
            val centerX = (gridSize / 2f - infiniteCanvasViewGroup.width / 2f) * -1
            val centerY = (gridSize / 2f - infiniteCanvasViewGroup.height / 2f) * -1
            infiniteCanvasViewGroup.setOffset(centerX, centerY)
            updateInfoText()
        }
    }

    /**
     * 添加角落标记
     */
    private fun addCornerMark(text: String, x: Float, y: Float) {
        val cornerMark = TextView(this).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            this.text = text
            setTextColor(ContextCompat.getColor(this@InfiniteCanvasActivity, android.R.color.white))
            setBackgroundColor(ContextCompat.getColor(this@InfiniteCanvasActivity, android.R.color.black))
            setPadding(16, 8, 16, 8)
            this.x = x
            this.y = y
            gravity = Gravity.CENTER
        }
        infiniteCanvasViewGroup.addView(cornerMark)
    }

    /**
     * 更新信息文本
     */
    private fun updateInfoText() {
        infoTextView.text = "偏移量: (${infiniteCanvasViewGroup.getOffsetX().toInt()}, ${infiniteCanvasViewGroup.getOffsetY().toInt()})"
    }

    /**
     * 当触摸事件发生时更新信息文本
     */
    override fun onTouchEvent(event: android.view.MotionEvent?): Boolean {
        updateInfoText()
        return super.onTouchEvent(event)
    }
}
