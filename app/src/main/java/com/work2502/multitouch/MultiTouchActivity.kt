package com.work2502.multitouch

import android.annotation.SuppressLint
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.work2502.compose.R

/**
 * 展示多触摸交互自定义View的Activity
 */
class MultiTouchActivity : AppCompatActivity() {

    private lateinit var multiTouchView: MultiTouchView
    private lateinit var resetButton: Button

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_multi_touch)

        // 初始化视图
        multiTouchView = findViewById(R.id.multiTouchView)
        resetButton = findViewById(R.id.resetButton)

        // 设置缩放范围
        multiTouchView.setScaleRange(0.5f, 5.0f)

        // 设置重置按钮点击事件
        resetButton.setOnClickListener {
            multiTouchView.reset()
            Toast.makeText(this, "视图已重置", Toast.LENGTH_SHORT).show()
        }

        // 显示使用提示
        Toast.makeText(
            this,
            "使用单指拖动可滑动内容\n双指捏合可缩放内容\n快速滑动可触发惯性滚动\n双击可重置视图",
            Toast.LENGTH_LONG
        ).show()
    }
}
