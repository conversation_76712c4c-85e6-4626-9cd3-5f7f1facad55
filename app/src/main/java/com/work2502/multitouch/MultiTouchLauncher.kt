package com.work2502.multitouch

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动MultiTouchActivity的辅助类
 */
object MultiTouchLauncher {

    /**
     * 创建一个启动MultiTouchActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): But<PERSON> {
        val button = Button(context)
        button.text = "启动多触摸交互示例"
        button.setOnClickListener {
            val intent = Intent(context, MultiTouchActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动MultiTouchActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
        val intent = Intent(activity, MultiTouchActivity::class.java)
        activity.startActivity(intent)
    }
}
