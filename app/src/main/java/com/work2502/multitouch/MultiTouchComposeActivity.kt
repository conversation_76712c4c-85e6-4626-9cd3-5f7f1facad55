package com.work2502.multitouch

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.work2502.compose.R
import kotlin.math.roundToInt

/**
 * 展示多触摸Compose组件的Activity
 */
class MultiTouchComposeActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            MaterialTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MultiTouchComposeScreen()
                }
            }
        }
    }
}

/**
 * 多触摸Compose示例屏幕
 */
@Composable
fun MultiTouchComposeScreen() {
    // 记录当前的缩放比例
    var scale by remember { mutableFloatStateOf(1f) }
    
    // 记录当前的偏移量
    var offset by remember { mutableStateOf(Offset.Zero) }
    
    Scaffold(
        topBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.primaryContainer)
                    .padding(16.dp)
            ) {
                Text(
                    text = "多触摸Compose示例",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 显示当前缩放比例和偏移量
                Text(
                    text = "缩放比例: ${(scale * 100).roundToInt() / 100f}x",
                    fontSize = 14.sp
                )
                
                Text(
                    text = "偏移量: (${(offset.x * 100).roundToInt() / 100f}, ${(offset.y * 100).roundToInt() / 100f})",
                    fontSize = 14.sp
                )
            }
        },
        bottomBar = {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                Button(
                    onClick = {
                        // 重置缩放和偏移
                        scale = 1f
                        offset = Offset.Zero
                    }
                ) {
                    Text(text = "重置视图")
                }
            }
        }
    ) { paddingValues ->
        // 主内容区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .background(Color.LightGray.copy(alpha = 0.3f))
        ) {
            // 使用ZoomableBox组件
            ZoomableBox(
                modifier = Modifier.fillMaxSize(),
                minScale = 0.5f,
                maxScale = 5f,
                initialScale = 1f,
                enableRotation = false,
                onScaleChanged = { scale = it },
                onOffsetChanged = { offset = it }
            ) {
                // ZoomableBox的内容
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 图片卡片
                    Card(
                        modifier = Modifier
                            .size(200.dp)
                            .border(2.dp, MaterialTheme.colorScheme.primary, RoundedCornerShape(8.dp))
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_launcher_background),
                            contentDescription = "示例图片",
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                    
                    // 文本卡片
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(8.dp)
                    ) {
                        Text(
                            text = "这是一个可以缩放和滚动的文本。\n\n" +
                                    "您可以使用双指捏合来缩放内容，\n" +
                                    "使用单指拖动来滚动内容。\n\n" +
                                    "尝试放大后滑动查看内容的边缘部分。",
                            modifier = Modifier.padding(16.dp),
                            fontSize = 16.sp
                        )
                    }
                    
                    // 按钮
                    Button(
                        onClick = {
                            // 显示点击消息
                            // 在实际应用中，您可以使用Toast或其他方式显示消息
                        }
                    ) {
                        Text(text = "点击我")
                    }
                    
                    // 另一个图片
                    Image(
                        painter = painterResource(id = R.drawable.ic_launcher_foreground),
                        contentDescription = "另一个示例图片",
                        modifier = Modifier
                            .size(150.dp)
                            .border(1.dp, Color.Gray)
                    )
                }
            }
            
            // 使用提示
            Text(
                text = "使用双指捏合缩放，单指拖动滚动",
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 16.dp)
                    .background(Color.Black.copy(alpha = 0.5f))
                    .padding(8.dp),
                color = Color.White,
                fontSize = 14.sp
            )
        }
    }
}

/**
 * 预览
 */
@Preview(showBackground = true)
@Composable
fun MultiTouchComposeScreenPreview() {
    MaterialTheme {
        MultiTouchComposeScreen()
    }
}

/**
 * 用于启动MultiTouchComposeActivity的辅助类
 */
object MultiTouchComposeLauncher {
    /**
     * 启动MultiTouchComposeActivity
     */
    fun launch(activity: androidx.appcompat.app.AppCompatActivity) {
        val intent = android.content.Intent(activity, MultiTouchComposeActivity::class.java)
        activity.startActivity(intent)
    }
}
