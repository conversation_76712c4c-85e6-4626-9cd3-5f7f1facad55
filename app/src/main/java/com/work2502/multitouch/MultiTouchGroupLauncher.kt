package com.work2502.multitouch

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动MultiTouchGroupActivity的辅助类
 */
object MultiTouchGroupLauncher {

    /**
     * 创建一个启动MultiTouchGroupActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): But<PERSON> {
        val button = Button(context)
        button.text = "启动多触摸ViewGroup示例"
        button.setOnClickListener {
            val intent = Intent(context, MultiTouchGroupActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动MultiTouchGroupActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
        val intent = Intent(activity, MultiTouchGroupActivity::class.java)
        activity.startActivity(intent)
    }
}
