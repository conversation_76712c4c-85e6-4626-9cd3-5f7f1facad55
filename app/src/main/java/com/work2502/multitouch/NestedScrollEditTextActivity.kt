package com.work2502.multitouch

import android.os.Bundle
import android.view.GestureDetector
import android.view.Gravity
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.HorizontalScrollView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.GestureDetectorCompat
import com.work2502.compose.R
import kotlin.math.abs

/**
 * 使用NestedScrollView和HorizontalScrollView实现二维滚动的示例Activity
 */
class NestedScrollEditTextActivity : AppCompatActivity(), GestureDetector.OnGestureListener, ScaleGestureDetector.OnScaleGestureListener {

    private lateinit var nestedScrollView: CustomNestedScrollView
    private lateinit var horizontalScrollView: CustomHorizontalScrollView
    private lateinit var resetButton: Button
    private lateinit var infoTextView: TextView
    private lateinit var contentLayout: LinearLayout
    private lateinit var editText: EditText

    // 手势检测器
    private lateinit var gestureDetector: GestureDetectorCompat
    // 缩放手势检测器
    private lateinit var scaleGestureDetector: ScaleGestureDetector

    // 滚动速度衰减因子
    private val scrollDecayFactor = 0.95f

    // 是否正在惯性滚动
    private var isScrolling = false

    // 当前滚动速度
    private var velocityX = 0f
    private var velocityY = 0f

    // 当前缩放比例
    private var scaleFactor = 1.0f
    // 最小缩放比例
    private val minScale = 0.5f
    // 最大缩放比例
    private val maxScale = 3.0f

    // 是否正在缩放
    private var isScaling = false

    // 记录上次选择位置，用于自动滚动
    private var lastSelectionStart = 0
    private var lastSelectionEnd = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_nested_scroll)

        // 初始化视图
        nestedScrollView = findViewById(R.id.nestedScrollView)
        horizontalScrollView = findViewById(R.id.horizontalScrollView)
        resetButton = findViewById(R.id.resetButton)
        infoTextView = findViewById(R.id.infoTextView)
        contentLayout = findViewById(R.id.contentLayout)

        // 初始化手势检测器
        gestureDetector = GestureDetectorCompat(this, this)
        // 初始化缩放手势检测器
        scaleGestureDetector = ScaleGestureDetector(this, this)

        // 设置重置按钮点击事件
        resetButton.setOnClickListener {
            // 滚动到原点位置
            nestedScrollView.scrollTo(0, 0)
            horizontalScrollView.scrollTo(0, 0)

            // 重置缩放
            scaleFactor = 1.0f
            applyScale()

            updateScrollInfo(0, 0)
            Toast.makeText(this, "视图已重置", Toast.LENGTH_SHORT).show()
        }

        // 添加内容
        addContent()

        // 设置滚动监听
        setupScrollListeners()

        // 显示使用提示
        Toast.makeText(
            this,
            "使用手指上下左右滑动查看内容\n双指捏合可缩放内容\n点击重置按钮回到原点",
            Toast.LENGTH_LONG
        ).show()
    }

    // 创建一个大型网格
    val gridSize = 3000
    /**
     * 添加内容
     */
    private fun addContent() {
        contentLayout = findViewById<LinearLayout>(R.id.contentLayout)
        editText = EditText(this)
        val text = "这是一个很长的文本，用于测试滚动功能。阿斯顿发大沙发阿斯顿发大发发答复士大夫阿方\n".repeat(100)
        editText.setText(text)

        // 监听EditText的选择变化
        editText.setOnSelectionChangedListener { start, end ->
            lastSelectionStart = start
            lastSelectionEnd = end
        }

        contentLayout.addView(editText)

        // 初始滚动到中心位置
        nestedScrollView.post {
            // 计算中心位置
            val centerX = (contentLayout.width / 2) - (nestedScrollView.width / 2)
            val centerY = (contentLayout.height / 2) - (nestedScrollView.height / 2)

            // 滚动到中心位置
            horizontalScrollView.scrollTo(centerX, 0)
            nestedScrollView.scrollTo(0, centerY)

            // 更新滚动信息
            updateScrollInfo(centerX, centerY)
        }
    }


    /**
     * 设置滚动监听
     */
    private fun setupScrollListeners() {
        // 监听垂直滚动
        nestedScrollView.setOnScrollChangeListener { v: View, scrollX: Int, scrollY: Int, oldScrollX: Int, oldScrollY: Int ->
            updateScrollInfo(horizontalScrollView.scrollX, scrollY)
        }

        // 监听水平滚动
        horizontalScrollView.viewTreeObserver.addOnScrollChangedListener {
            updateScrollInfo(horizontalScrollView.scrollX, nestedScrollView.scrollY)
        }

        // 设置触摸监听
        contentLayout.setOnTouchListener { v, event ->
            // 处理缩放手势
            val scaleResult = scaleGestureDetector.onTouchEvent(event)

            // 如果不是缩放，则处理滚动和点击
            if (!isScaling) {
                // 将事件传递给手势检测器
                gestureDetector.onTouchEvent(event)

                // 如果是抬起手指，则开始惯性滚动
                if (event.action == MotionEvent.ACTION_UP && (abs(velocityX) > 50 || abs(velocityY) > 50)) {
                    startInertialScroll()
                }

                // 如果是单击，处理光标位置
                if (event.action == MotionEvent.ACTION_UP &&
                    abs(velocityX) < 50 && abs(velocityY) < 50) {
                    handleTap(event.x, event.y)
                }
            }

            true
        }

        // 为了确保缩放事件能被捕获，也为父视图设置触摸监听
        nestedScrollView.setOnTouchListener { v, event ->
            // 处理缩放手势
            scaleGestureDetector.onTouchEvent(event)

            // 如果正在缩放，消费事件
            if (isScaling) {
                true
            } else {
                // 否则让事件继续传递
                false
            }
        }

        horizontalScrollView.setOnTouchListener { v, event ->
            // 处理缩放手势
            scaleGestureDetector.onTouchEvent(event)

            // 如果正在缩放，消费事件
            if (isScaling) {
                true
            } else {
                // 否则让事件继续传递
                false
            }
        }
    }

    /**
     * 开始惯性滚动
     */
    private fun startInertialScroll() {
        isScrolling = true
        inertialScroll()
    }

    /**
     * 惯性滚动
     */
    private fun inertialScroll() {
        if (!isScrolling || (abs(velocityX) < 0.5 && abs(velocityY) < 0.5)) {
            isScrolling = false
            return
        }

        // 计算新的滚动位置
        val scrollX = horizontalScrollView.scrollX + velocityX.toInt()
        val scrollY = nestedScrollView.scrollY + velocityY.toInt()

        // 滚动到新位置
        horizontalScrollView.scrollTo(scrollX, 0)
        nestedScrollView.scrollTo(0, scrollY)

        // 更新滚动信息
        updateScrollInfo(scrollX, scrollY)

        // 速度衰减
        velocityX *= scrollDecayFactor
        velocityY *= scrollDecayFactor

        // 继续滚动
        nestedScrollView.postDelayed({ inertialScroll() }, 16) // 约每秒 60 帧
    }

    /**
     * 更新滚动信息
     */
    private fun updateScrollInfo(scrollX: Int, scrollY: Int) {
        infoTextView.text = "滚动位置: ($scrollX, $scrollY) 缩放: ${String.format("%.2f", scaleFactor)}x"
    }

    /**
     * 当触摸事件发生时更新信息文本
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 处理缩放手势
        scaleGestureDetector.onTouchEvent(event)

        // 如果不是缩放，则处理滚动
        if (!isScaling) {
            // 将事件传递给手势检测器
            gestureDetector.onTouchEvent(event)
        }

        updateScrollInfo(horizontalScrollView.scrollX, nestedScrollView.scrollY)
        return true
    }

    // GestureDetector.OnGestureListener 接口实现
    override fun onDown(e: MotionEvent): Boolean {
        // 停止惯性滚动
        isScrolling = false
        velocityX = 0f
        velocityY = 0f
        return true
    }

    override fun onShowPress(e: MotionEvent) {}

    override fun onSingleTapUp(e: MotionEvent): Boolean = false

    override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
        // 在滚动时不做特殊处理，因为 NestedScrollView 和 HorizontalScrollView 会处理滚动
        return false
    }

    override fun onLongPress(e: MotionEvent) {}

    override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
        // 记录速度以便于惯性滚动
        this.velocityX = velocityX / 1000 // 缩小速度以获得更好的效果
        this.velocityY = velocityY / 1000
        return true
    }

    // ScaleGestureDetector.OnScaleGestureListener 接口实现
    override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
        isScaling = true
        return true
    }

    override fun onScale(detector: ScaleGestureDetector): Boolean {
        // 计算新的缩放比例
        scaleFactor *= detector.scaleFactor

        // 限制缩放范围
        scaleFactor = scaleFactor.coerceIn(minScale, maxScale)

        // 应用缩放到内容布局
        applyScale()

        // 更新信息显示
        updateScrollInfo(horizontalScrollView.scrollX, nestedScrollView.scrollY)

        return true
    }

    override fun onScaleEnd(detector: ScaleGestureDetector) {
        isScaling = false
    }

    /**
     * 应用缩放到内容布局
     */
    private fun applyScale() {
        // 获取缩放中心点
        val centerX = contentLayout.width / 2f
        val centerY = contentLayout.height / 2f

        // 设置缩放中心点
        contentLayout.pivotX = centerX
        contentLayout.pivotY = centerY

        // 应用缩放
        contentLayout.scaleX = scaleFactor
        contentLayout.scaleY = scaleFactor

        // 确保重绘
        contentLayout.invalidate()
    }

    // 确保分发所有触摸事件到缩放检测器
    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        scaleGestureDetector.onTouchEvent(ev)
        return super.dispatchTouchEvent(ev)
    }
}
