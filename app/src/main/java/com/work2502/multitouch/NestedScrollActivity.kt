package com.work2502.multitouch

import android.os.Bundle
import android.view.GestureDetector
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.GestureDetectorCompat
import com.work2502.compose.R
import kotlin.math.abs

/**
 * 使用NestedScrollView和HorizontalScrollView实现二维滚动的示例Activity
 */
class NestedScrollActivity : AppCompatActivity(), GestureDetector.OnGestureListener {

    private lateinit var nestedScrollView: CustomNestedScrollView
    private lateinit var horizontalScrollView: CustomHorizontalScrollView
    private lateinit var resetButton: Button
    private lateinit var infoTextView: TextView

    // 手势检测器
    private lateinit var gestureDetector: GestureDetectorCompat

    // 滚动速度衰减因子
    private val scrollDecayFactor = 0.95f

    // 是否正在惯性滚动
    private var isScrolling = false

    // 当前滚动速度
    private var velocityX = 0f
    private var velocityY = 0f

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_nested_scroll)

        // 初始化视图
        nestedScrollView = findViewById(R.id.nestedScrollView)
        horizontalScrollView = findViewById(R.id.horizontalScrollView)
        resetButton = findViewById(R.id.resetButton)
        infoTextView = findViewById(R.id.infoTextView)

        // 初始化手势检测器
        gestureDetector = GestureDetectorCompat(this, this)

        // 设置重置按钮点击事件
        resetButton.setOnClickListener {
            // 滚动到原点位置
            nestedScrollView.scrollTo(0, 0)
            val horizontalScrollView = findViewById<android.widget.HorizontalScrollView>(R.id.horizontalScrollView)
            horizontalScrollView.scrollTo(0, 0)
            updateScrollInfo(0, 0)
            Toast.makeText(this, "视图已重置", Toast.LENGTH_SHORT).show()
        }

        // 添加内容
        addContent()

        // 设置滚动监听
        setupScrollListeners()

        // 显示使用提示
        Toast.makeText(
            this,
            "使用手指上下左右滑动查看内容\n点击重置按钮回到原点",
            Toast.LENGTH_LONG
        ).show()
    }

    // 创建一个大型网格
    val gridSize = 3000
    /**
     * 添加内容
     */
    private fun addContent() {
        val contentLayout = findViewById<LinearLayout>(R.id.contentLayout)

        val cellSize = 200

        // 创建行
        for (row in 0 until gridSize / cellSize) {
            val rowLayout = LinearLayout(this).apply {
                orientation = LinearLayout.HORIZONTAL
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                )
            }

            // 创建单元格
            for (col in 0 until gridSize / cellSize) {
                val cell = createGridCell(row, col, cellSize)
                rowLayout.addView(cell)
            }

            contentLayout.addView(rowLayout)
        }

        // 添加中心标记
        val centerMark = TextView(this).apply {
            text = "中心点"
            setTextColor(ContextCompat.getColor(this@NestedScrollActivity, android.R.color.white))
            setBackgroundColor(ContextCompat.getColor(this@NestedScrollActivity, android.R.color.black))
            setPadding(16, 8, 16, 8)
            gravity = Gravity.CENTER
        }

        // 将中心标记添加到中心位置
        val centerRow = gridSize / (2 * cellSize)
        val centerCol = gridSize / (2 * cellSize)
        val centerRowLayout = contentLayout.getChildAt(centerRow) as LinearLayout
        val centerCell = centerRowLayout.getChildAt(centerCol) as LinearLayout
        centerCell.addView(centerMark)

        // 添加一些按钮
        for (i in 0 until 10) {
            val row = (Math.random() * (gridSize / cellSize)).toInt()
            val col = (Math.random() * (gridSize / cellSize)).toInt()

            if (row < contentLayout.childCount) {
                val rowLayout = contentLayout.getChildAt(row) as LinearLayout
                if (col < rowLayout.childCount) {
                    val cell = rowLayout.getChildAt(col) as LinearLayout

                    val button = Button(this).apply {
                        text = "按钮 $i"
                        setOnClickListener {
                            Toast.makeText(this@NestedScrollActivity, "点击了按钮 $i", Toast.LENGTH_SHORT).show()
                        }
                    }

                    cell.addView(button)
                }
            }
        }

        // 初始滚动到中心位置
        nestedScrollView.post {
            val horizontalScrollView = findViewById<android.widget.HorizontalScrollView>(R.id.horizontalScrollView)

            // 计算中心位置
            val centerX = (gridSize / 2) - (nestedScrollView.width / 2)
            val centerY = (gridSize / 2) - (nestedScrollView.height / 2)

            // 滚动到中心位置
            horizontalScrollView.scrollTo(centerX, 0)
            nestedScrollView.scrollTo(0, centerY)

            // 更新滚动信息
            updateScrollInfo(centerX, centerY)
        }
    }

    /**
     * 创建网格单元格
     */
    private fun createGridCell(row: Int, col: Int, size: Int): View {
        return LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(size, size)

            // 设置边框
            setBackgroundResource(R.drawable.grid_cell_background)

            // 添加坐标文本
            val coordText = TextView(this@NestedScrollActivity).apply {
                text = "($col,$row)"
                textSize = 10f
                gravity = Gravity.CENTER
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                )
            }

            addView(coordText)

            // 设置不同的背景颜色
            val colorRes = when {
                row == 0 && col == 0 -> android.R.color.holo_red_light // 左上角
                row == 0 && col == gridSize / size - 1 -> android.R.color.holo_blue_light // 右上角
                row == gridSize / size - 1 && col == 0 -> android.R.color.holo_green_light // 左下角
                row == gridSize / size - 1 && col == gridSize / size - 1 -> android.R.color.holo_orange_light // 右下角
                row == gridSize / (2 * size) && col == gridSize / (2 * size) -> android.R.color.holo_purple // 中心
                else -> android.R.color.transparent
            }

            if (colorRes != android.R.color.transparent) {
                setBackgroundColor(ContextCompat.getColor(this@NestedScrollActivity, colorRes))
            }
        }
    }

    /**
     * 设置滚动监听
     */
    private fun setupScrollListeners() {
        // 监听垂直滚动
        nestedScrollView.setOnScrollChangeListener { v: View, scrollX: Int, scrollY: Int, oldScrollX: Int, oldScrollY: Int ->
            updateScrollInfo(horizontalScrollView.scrollX, scrollY)
        }

        // 监听水平滚动
        horizontalScrollView.viewTreeObserver.addOnScrollChangedListener {
            updateScrollInfo(horizontalScrollView.scrollX, nestedScrollView.scrollY)
        }

        // 设置触摸监听
        val contentLayout = findViewById<LinearLayout>(R.id.contentLayout)
        contentLayout.setOnTouchListener { v, event ->
            // 将事件传递给手势检测器
            gestureDetector.onTouchEvent(event)

            // 如果是抬起手指，则开始惯性滚动
            if (event.action == MotionEvent.ACTION_UP && (abs(velocityX) > 50 || abs(velocityY) > 50)) {
                startInertialScroll()
            }

            false
        }
    }

    /**
     * 开始惯性滚动
     */
    private fun startInertialScroll() {
        isScrolling = true
        inertialScroll()
    }

    /**
     * 惯性滚动
     */
    private fun inertialScroll() {
        if (!isScrolling || (abs(velocityX) < 0.5 && abs(velocityY) < 0.5)) {
            isScrolling = false
            return
        }

        // 计算新的滚动位置
        val scrollX = horizontalScrollView.scrollX + velocityX.toInt()
        val scrollY = nestedScrollView.scrollY + velocityY.toInt()

        // 滚动到新位置
        horizontalScrollView.scrollTo(scrollX, 0)
        nestedScrollView.scrollTo(0, scrollY)

        // 更新滚动信息
        updateScrollInfo(scrollX, scrollY)

        // 速度衰减
        velocityX *= scrollDecayFactor
        velocityY *= scrollDecayFactor

        // 继续滚动
        nestedScrollView.postDelayed({ inertialScroll() }, 16) // 约每秒 60 帧
    }

    /**
     * 更新滚动信息
     */
    private fun updateScrollInfo(scrollX: Int, scrollY: Int) {
        infoTextView.text = "滚动位置: ($scrollX, $scrollY)"
    }

    /**
     * 当触摸事件发生时更新信息文本
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 将事件传递给手势检测器
        gestureDetector.onTouchEvent(event)
        updateScrollInfo(horizontalScrollView.scrollX, nestedScrollView.scrollY)
        return super.onTouchEvent(event)
    }

    // GestureDetector.OnGestureListener 接口实现
    override fun onDown(e: MotionEvent): Boolean {
        // 停止惯性滚动
        isScrolling = false
        velocityX = 0f
        velocityY = 0f
        return true
    }

    override fun onShowPress(e: MotionEvent) {}

    override fun onSingleTapUp(e: MotionEvent): Boolean = false

    override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
        // 在滚动时不做特殊处理，因为 NestedScrollView 和 HorizontalScrollView 会处理滚动
        return false
    }

    override fun onLongPress(e: MotionEvent) {}

    override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
        // 记录速度以便于惯性滚动
        this.velocityX = velocityX / 1000 // 缩小速度以获得更好的效果
        this.velocityY = velocityY / 1000
        return true
    }
}
