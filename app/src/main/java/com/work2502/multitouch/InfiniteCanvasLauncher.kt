package com.work2502.multitouch

import android.content.Context
import android.content.Intent
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * 用于启动InfiniteCanvasActivity的辅助类
 */
object InfiniteCanvasLauncher {

    /**
     * 创建一个启动InfiniteCanvasActivity的按钮
     * @param context 上下文
     * @return 配置好的按钮
     */
    fun createLauncherButton(context: Context): Button {
        val button = Button(context)
        button.text = "无限画布示例"
        button.setOnClickListener {
            val intent = Intent(context, InfiniteCanvasActivity::class.java)
            context.startActivity(intent)
        }
        return button
    }

    /**
     * 直接启动InfiniteCanvasActivity
     * @param activity 当前Activity
     */
    fun launch(activity: AppCompatActivity) {
        val intent = Intent(activity, InfiniteCanvasActivity::class.java)
        activity.startActivity(intent)
    }
}
