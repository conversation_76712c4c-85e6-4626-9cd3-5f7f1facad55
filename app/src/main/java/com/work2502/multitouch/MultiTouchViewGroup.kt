package com.work2502.multitouch

import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.RectF
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.Interpolator
import android.widget.FrameLayout
import android.widget.HorizontalScrollView
import android.widget.OverScroller
import android.widget.RelativeLayout
import android.widget.ScrollView
import androidx.core.view.ViewCompat
import androidx.core.view.children
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 多触摸交互ViewGroup
 * 支持上下滑动、左右滑动、缩放等多种手势操作
 * 所有子View会随着ViewGroup一起滚动和缩放
 */
class MultiTouchViewGroup @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 变换矩阵，用于应用缩放和平移
    private val transformMatrix = Matrix()

    // 用于保存矩阵的值
    private val matrixValues = FloatArray(9)

    // 内容的边界
    private val contentBounds = RectF()

    // 视图的边界
    private val viewBounds = RectF()

    // 缩放手势检测器
    private val scaleGestureDetector: ScaleGestureDetector

    // 普通手势检测器
    private val gestureDetector: GestureDetector

    // 用于惯性滚动
    private val scroller: OverScroller

    // 用于平滑动画
    private val interpolator: Interpolator = AccelerateDecelerateInterpolator()

    // 用于提高绘制质量
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)

    // 最后一次触摸点
    private val lastTouch = PointF()

    // 缩放的中心点
    private val scaleCenter = PointF()

    // 是否正在拖动
    private var isDragging = false

    // 最小和最大缩放比例
    private var minScale = 0.1f
    private var maxScale = 10.0f

    // 当前缩放比例
    private var currentScale = 1.0f

    // 上次缩放比例，用于平滑过渡
    private var lastScale = 1.0f

    // 是否启用确切绘制
    private var highQualityRendering = true

    // 是否已经初始化
    private var isInitialized = false

    // 是否正在缩放
    private var isScaling = false

    // 是否处理子视图点击
    private var isChildTapped = false

    // 触摸滑动阈值
    private val touchSlop: Int

    // 最小移动距离，用于区分点击和拖动
    private val minMoveDistance: Int

    // 标记当前是否正在处理子视图的点击事件
    private var isHandlingChildClick = false

    // 记录按下时的坐标，用于判断是否是点击
    private val downPoint = PointF()

    // 初始化
    init {
        // 设置可获取焦点
        isFocusable = true

        // 获取系统定义的触摸滑动阈值
        val configuration = ViewConfiguration.get(context)
        touchSlop = configuration.scaledTouchSlop
        minMoveDistance = touchSlop * 2

        // 初始化缩放手势检测器
        scaleGestureDetector = ScaleGestureDetector(context, ScaleListener()).apply {
            // 设置快速缩放模式为false，提高精确度
            isQuickScaleEnabled = false
        }

        // 初始化普通手势检测器
        gestureDetector = GestureDetector(context, GestureListener())

        // 初始化滚动器，使用插值器提高滚动流畅度
        scroller = OverScroller(context, interpolator)

        // 设置绘制顺序
        setWillNotDraw(false)

        // 设置裁剪子视图
        clipChildren = false

        // 设置确切绘制模式
        setLayerType(LAYER_TYPE_HARDWARE, paint)

        // 设置绘制质量
        paint.isFilterBitmap = true
        paint.isAntiAlias = true
    }

    /**
     * 测量视图大小
     */
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        // 设置视图边界
        viewBounds.set(0f, 0f, measuredWidth.toFloat(), measuredHeight.toFloat())

        // 如果还没有初始化，则初始化内容边界
        if (!isInitialized && childCount > 0) {
            // 更新内容边界
            updateContentBounds()

            // 重置矩阵
            transformMatrix.reset()

            // 居中显示内容
            val dx = (viewBounds.width() - contentBounds.width()) / 2 - contentBounds.left
            val dy = (viewBounds.height() - contentBounds.height()) / 2 - contentBounds.top
            transformMatrix.postTranslate(dx, dy)

            // 标记为已初始化
            isInitialized = true
        }
    }

    /**
     * 布局子视图
     */
    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)

        // 如果布局发生变化，重新计算内容边界
        if (changed) {
            updateContentBounds()
        }
    }

    /**
     * 分发触摸事件
     */
    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        // 处理ACTION_DOWN事件，记录按下点
        if (ev.actionMasked == MotionEvent.ACTION_DOWN) {
            downPoint.set(ev.x, ev.y)
            isHandlingChildClick = false
            isChildTapped = false
        }

        // 创建一个新的事件，应用逆矩阵变换
        val transformedEvent = transformTouchEvent(ev)

        // 分发变换后的事件
        val result = super.dispatchTouchEvent(transformedEvent)

        // 如果子视图处理了事件，标记为子视图点击
        if (result && ev.actionMasked == MotionEvent.ACTION_DOWN) {
            isHandlingChildClick = true
            isChildTapped = true
        }

        // 回收变换后的事件
        transformedEvent.recycle()

        return result
    }

    /**
     * 变换触摸事件坐标
     */
    private fun transformTouchEvent(event: MotionEvent): MotionEvent {
        // 创建一个新的事件
        val transformedEvent = MotionEvent.obtain(event)

        // 创建一个逆矩阵
        val inverseMatrix = Matrix()
        transformMatrix.invert(inverseMatrix)

        // 应用逆矩阵变换
        transformedEvent.transform(inverseMatrix)

        return transformedEvent
    }

    /**
     * 拦截触摸事件
     */
    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        // 如果是缩放手势，则拦截事件
        if (ev.pointerCount > 1) {
            isScaling = true
            return true
        }

        // 如果正在缩放，继续拦截
        if (isScaling) {
            return true
        }

        when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 记录触摸点
                lastTouch.set(ev.x, ev.y)
                downPoint.set(ev.x, ev.y)
                isDragging = false

                // 停止惯性滚动
                if (!scroller.isFinished) {
                    scroller.abortAnimation()
                    return true
                }
            }

            MotionEvent.ACTION_MOVE -> {
                // 如果子视图正在处理点击，不拦截
                if (isHandlingChildClick) {
                    return false
                }

                // 计算移动距离
                val dx = abs(ev.x - downPoint.x)
                val dy = abs(ev.y - downPoint.y)

                // 如果移动距离超过阈值，则拦截事件
                if (dx > touchSlop || dy > touchSlop) {
                    isDragging = true
                    // 更新最后触摸点
                    lastTouch.set(ev.x, ev.y)
                    // 请求父视图不要拦截触摸事件
                    parent?.requestDisallowInterceptTouchEvent(true)
                    return true
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                // 重置状态
                isDragging = false
                isScaling = false
                // 允许父视图拦截触摸事件
                parent?.requestDisallowInterceptTouchEvent(false)
            }
        }

        return super.onInterceptTouchEvent(ev)
    }

    /**
     * 处理触摸事件
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 如果子视图正在处理点击，不处理触摸事件
        if (isHandlingChildClick && event.pointerCount == 1) {
            return false
        }

        // 将事件传递给手势检测器
        val scaleResult = scaleGestureDetector.onTouchEvent(event)
        val gestureResult = gestureDetector.onTouchEvent(event)

        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 停止惯性滚动
                if (!scroller.isFinished) {
                    scroller.abortAnimation()
                }

                // 记录触摸点
                lastTouch.set(event.x, event.y)
                downPoint.set(event.x, event.y)
                isDragging = false

                // 开启高质量渲染
                highQualityRendering = true

                // 请求父视图不要拦截触摸事件
                parent?.requestDisallowInterceptTouchEvent(true)
            }

            MotionEvent.ACTION_MOVE -> {
                // 如果正在缩放，标记状态
                if (scaleGestureDetector.isInProgress) {
                    isScaling = true
                }

                // 如果是单指拖动且不在缩放
                if (event.pointerCount == 1 && !isScaling) {
                    // 计算移动距离
                    val dx = abs(event.x - downPoint.x)
                    val dy = abs(event.y - downPoint.y)

                    // 如果超过滑动阈值，则认为是拖动
                    if (dx > touchSlop || dy > touchSlop) {
                        isDragging = true
                    }

                    // 如果正在拖动
                    if (isDragging) {
                        // 计算移动距离
                        val moveX = event.x - lastTouch.x
                        val moveY = event.y - lastTouch.y

                        // 应用平移
                        transformMatrix.postTranslate(moveX, moveY)

                        // 限制边界
                        constrainMatrix()

                        // 更新最后触摸点
                        lastTouch.set(event.x, event.y)

                        // 重绘视图
                        invalidate()
                    }
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                // 如果没有拖动过，则可能是点击
                if (!isDragging && !isScaling && event.actionMasked == MotionEvent.ACTION_UP) {
                    // 如果是点击事件，则传递给子视图
                    performClick()
                }

                // 重置状态
                isDragging = false
                isScaling = false

                // 允许父视图拦截触摸事件
                parent?.requestDisallowInterceptTouchEvent(false)

                // 延迟关闭高质量渲染
                postDelayed({ highQualityRendering = false }, 300)
            }
        }

        return scaleResult || gestureResult || super.onTouchEvent(event)
    }

    /**
     * 绘制视图
     */
    override fun dispatchDraw(canvas: Canvas) {
        // 保存画布状态
        canvas.save()

        // 如果启用高质量渲染，设置绘制质量
        if (highQualityRendering) {
            canvas.setDrawFilter(DRAW_FILTER)
        }

        // 应用变换矩阵
        canvas.concat(transformMatrix)

        // 调用父类的绘制方法
        super.dispatchDraw(canvas)

        // 恢复画布状态
        canvas.restore()
    }

    /**
     * 当视图大小变化时调用
     */
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // 更新视图边界
        viewBounds.set(0f, 0f, w.toFloat(), h.toFloat())

        // 重新计算内容边界
        updateContentBounds()
    }

    /**
     * 更新内容边界
     */
    private fun updateContentBounds() {
        if (childCount > 0) {
            // 重置内容边界
            contentBounds.set(Float.MAX_VALUE, Float.MAX_VALUE, Float.MIN_VALUE, Float.MIN_VALUE)

            // 遍历所有子视图，计算内容边界
            for (child in children) {
                val childLeft = child.left.toFloat()
                val childTop = child.top.toFloat()
                val childRight = childLeft + child.width
                val childBottom = childTop + child.height

                // 扩展内容边界以包含所有子视图
                contentBounds.left = min(contentBounds.left, childLeft)
                contentBounds.top = min(contentBounds.top, childTop)
                contentBounds.right = max(contentBounds.right, childRight)
                contentBounds.bottom = max(contentBounds.bottom, childBottom)
            }

            // 添加边距，使内容不会紧贴边缘
            contentBounds.inset(-50f, -50f)

            // 如果内容边界无效，使用视图边界
            if (contentBounds.isEmpty || contentBounds.left == Float.MAX_VALUE) {
                contentBounds.set(viewBounds)
            }
        } else {
            // 如果没有子视图，使用视图边界
            contentBounds.set(viewBounds)
        }
    }

    /**
     * 计算滚动
     */
    override fun computeScroll() {
        if (scroller.computeScrollOffset()) {
            // 获取当前矩阵的平移值
            transformMatrix.getValues(matrixValues)

            // 计算新的平移值
            val currX = scroller.currX.toFloat()
            val currY = scroller.currY.toFloat()

            // 如果当前没有处理子视图点击或缩放，才应用滚动
            if (!isHandlingChildClick && !isScaling) {
                // 应用平移
                transformMatrix.postTranslate(currX - matrixValues[Matrix.MTRANS_X], currY - matrixValues[Matrix.MTRANS_Y])

                // 限制边界
                constrainMatrix()

                // 重绘视图
                ViewCompat.postInvalidateOnAnimation(this)
            } else {
                // 如果正在处理子视图点击或缩放，停止滚动
                scroller.abortAnimation()
            }
        }
    }

    /**
     * 当子视图添加或移除时调用
     */
    override fun onViewAdded(child: View?) {
        super.onViewAdded(child)
        updateContentBounds()
    }

    /**
     * 当子视图移除时调用
     */
    override fun onViewRemoved(child: View?) {
        super.onViewRemoved(child)
        updateContentBounds()
    }

    /**
     * 限制矩阵，防止内容滑出可视区域
     */
    private fun constrainMatrix() {
        // 获取当前矩阵的值
        transformMatrix.getValues(matrixValues)

        // 获取当前的缩放和平移值
        val scale = matrixValues[Matrix.MSCALE_X]
        var transX = matrixValues[Matrix.MTRANS_X]
        var transY = matrixValues[Matrix.MTRANS_Y]

        // 记录原始值，用于检测是否发生变化
        val originalTransX = transX
        val originalTransY = transY

        // 计算内容的宽高
        val contentWidth = contentBounds.width() * scale
        val contentHeight = contentBounds.height() * scale

        // 计算内容的左上角和右下角坐标
        val contentLeft = contentBounds.left * scale + transX
        val contentTop = contentBounds.top * scale + transY
        val contentRight = contentLeft + contentWidth
        val contentBottom = contentTop + contentHeight

        // 限制水平方向的平移
        if (contentWidth <= viewBounds.width()) {
            // 如果内容宽度小于视图宽度，则居中显示
            transX = (viewBounds.width() - contentWidth) / 2 - contentBounds.left * scale
        } else {
            // 如果内容宽度大于视图宽度，则限制边界
            if (contentLeft > 0) {
                transX -= contentLeft
            } else if (contentRight < viewBounds.width()) {
                transX += viewBounds.width() - contentRight
            }
        }

        // 限制垂直方向的平移
        if (contentHeight <= viewBounds.height()) {
            // 如果内容高度小于视图高度，则居中显示
            transY = (viewBounds.height() - contentHeight) / 2 - contentBounds.top * scale
        } else {
            // 如果内容高度大于视图高度，则限制边界
            if (contentTop > 0) {
                transY -= contentTop
            } else if (contentBottom < viewBounds.height()) {
                transY += viewBounds.height() - contentBottom
            }
        }

        // 检测是否发生了显著变化
        val hasSignificantChange = abs(transX - originalTransX) > 0.1f || abs(transY - originalTransY) > 0.1f

        // 更新矩阵，使用平滑过渡避免抖动
        if (hasSignificantChange) {
            // 使用平滑过渡而不是直接重置矩阵
            val newMatrix = Matrix()
            newMatrix.postScale(scale, scale)
            newMatrix.postTranslate(transX, transY)

            // 平滑过渡到新矩阵
            transformMatrix.set(newMatrix)
        }
    }

    /**
     * 缩放监听器
     */
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            // 标记正在缩放
            isScaling = true

            // 记录缩放中心点
            scaleCenter.set(detector.focusX, detector.focusY)

            // 记录当前缩放值
            transformMatrix.getValues(matrixValues)
            lastScale = matrixValues[Matrix.MSCALE_X]

            // 开启确切绘制模式
            highQualityRendering = true

            return true
        }

        override fun onScale(detector: ScaleGestureDetector): Boolean {
            // 获取当前矩阵的值
            transformMatrix.getValues(matrixValues)

            // 获取当前的缩放值
            val currentScale = matrixValues[Matrix.MSCALE_X]

            // 计算新的缩放值，使用平滑因子减少抖动
            var newScale = currentScale * (1.0f + (detector.scaleFactor - 1.0f) * 0.5f)

            // 限制缩放范围
            newScale = max(minScale, min(newScale, maxScale))

            // 计算缩放因子
            val scaleFactor = newScale / currentScale

            // 获取缩放中心点，使用记录的中心点和当前中心点的加权平均
            val focusX = detector.focusX * 0.7f + scaleCenter.x * 0.3f
            val focusY = detector.focusY * 0.7f + scaleCenter.y * 0.3f

            // 更新缩放中心点
            scaleCenter.set(focusX, focusY)

            // 应用缩放
            transformMatrix.postScale(scaleFactor, scaleFactor, focusX, focusY)

            // 限制边界
            constrainMatrix()

            // 更新当前缩放值
            <EMAIL> = newScale

            // 重绘视图
            invalidate()

            return true
        }

        override fun onScaleEnd(detector: ScaleGestureDetector) {
            // 缩放结束后重置状态
            isScaling = false

            // 如果缩放比例非常接近 1.0，则重置为 1.0
            if (abs(currentScale - 1.0f) < 0.05f) {
                reset()
            }

            // 延迟关闭高质量渲染，以确保最后几帧仍然是高质量的
            postDelayed({ highQualityRendering = false }, 300)
        }
    }

    /**
     * 手势监听器
     */
    private inner class GestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onDown(e: MotionEvent): Boolean {
            return true
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            // 单击确认，如果没有子视图处理，可以在这里处理单击事件
            return false
        }

        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            // 如果正在缩放或处理子视图点击，不处理惯性滚动
            if (isScaling || isHandlingChildClick) {
                return false
            }

            // 获取当前矩阵的值
            transformMatrix.getValues(matrixValues)

            // 获取当前的平移值
            val transX = matrixValues[Matrix.MTRANS_X]
            val transY = matrixValues[Matrix.MTRANS_Y]

            // 获取当前的缩放值
            val scale = matrixValues[Matrix.MSCALE_X]

            // 计算内容的宽高
            val contentWidth = contentBounds.width() * scale
            val contentHeight = contentBounds.height() * scale

            // 计算最小和最大滚动范围
            val minX: Int
            val maxX: Int
            val minY: Int
            val maxY: Int

            if (contentWidth <= viewBounds.width()) {
                // 如果内容宽度小于视图宽度，不允许水平滚动
                minX = transX.toInt()
                maxX = transX.toInt()
            } else {
                // 计算水平滚动范围
                val leftBound = viewBounds.width() - contentWidth - contentBounds.left * scale
                val rightBound = -contentBounds.left * scale
                minX = leftBound.toInt()
                maxX = rightBound.toInt()
            }

            if (contentHeight <= viewBounds.height()) {
                // 如果内容高度小于视图高度，不允许垂直滚动
                minY = transY.toInt()
                maxY = transY.toInt()
            } else {
                // 计算垂直滚动范围
                val topBound = viewBounds.height() - contentHeight - contentBounds.top * scale
                val bottomBound = -contentBounds.top * scale
                minY = topBound.toInt()
                maxY = bottomBound.toInt()
            }

            // 启动惯性滚动，使用计算出的边界
            scroller.fling(
                transX.toInt(),
                transY.toInt(),
                velocityX.toInt() / 2,
                velocityY.toInt() / 2,
                minX,
                maxX,
                minY,
                maxY,
                50, // 边界回弹效果
                50
            )

            // 触发重绘
            ViewCompat.postInvalidateOnAnimation(this@MultiTouchViewGroup)

            return true
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
            // 双击时重置缩放
            reset()
            return true
        }
    }

    /**
     * 设置缩放范围
     */
    fun setScaleRange(minScale: Float, maxScale: Float) {
        this.minScale = minScale
        this.maxScale = maxScale
    }

    /**
     * 重置视图状态
     */
    fun reset() {
        // 停止所有正在进行的滚动
        if (!scroller.isFinished) {
            scroller.abortAnimation()
        }

        // 重置状态
        isDragging = false
        isScaling = false
        isChildTapped = false
        isHandlingChildClick = false

        // 开启高质量渲染
        highQualityRendering = true

        // 重置矩阵
        transformMatrix.reset()
        currentScale = 1.0f
        lastScale = 1.0f

        // 更新内容边界
        updateContentBounds()

        // 居中显示内容
        val dx = (viewBounds.width() - contentBounds.width()) / 2 - contentBounds.left
        val dy = (viewBounds.height() - contentBounds.height()) / 2 - contentBounds.top
        transformMatrix.postTranslate(dx, dy)

        // 重绘视图
        invalidate()

        // 延迟关闭高质量渲染
        postDelayed({ highQualityRendering = false }, 300)

        // 记录日志
        Log.d(TAG, "View reset to initial state")
    }

    companion object {
        // 日志标签
        private const val TAG = "MultiTouchViewGroup"

        // 绘制过滤器，用于提高绘制质量
        private val DRAW_FILTER = android.graphics.PaintFlagsDrawFilter(
            0, Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG
        )
    }
}
