package com.work2502.multitouch

import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.PointF
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.widget.OverScroller
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import com.work2502.compose.R
import kotlin.math.max
import kotlin.math.min

/**
 * 多触摸交互自定义View
 * 支持上下滑动、左右滑动、缩放等多种手势操作
 */
class MultiTouchView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 绘制内容的矩阵
    private val matrix = Matrix()
    
    // 用于保存矩阵的值
    private val matrixValues = FloatArray(9)
    
    // 内容的边界
    private val contentBounds = RectF()
    
    // 视图的边界
    private val viewBounds = RectF()
    
    // 缩放手势检测器
    private val scaleGestureDetector: ScaleGestureDetector
    
    // 普通手势检测器
    private val gestureDetector: GestureDetector
    
    // 用于惯性滚动
    private val scroller: OverScroller
    
    // 最后一次触摸点
    private val lastTouch = PointF()
    
    // 是否正在拖动
    private var isDragging = false
    
    // 最小和最大缩放比例
    private var minScale = 0.5f
    private var maxScale = 5.0f
    
    // 当前缩放比例
    private var currentScale = 1.0f
    
    // 示例图片
    private var drawable: Drawable? = null
    
    // 初始化
    init {
        // 设置可获取焦点
        isFocusable = true
        
        // 初始化缩放手势检测器
        scaleGestureDetector = ScaleGestureDetector(context, ScaleListener())
        
        // 初始化普通手势检测器
        gestureDetector = GestureDetector(context, GestureListener())
        
        // 初始化滚动器
        scroller = OverScroller(context)
        
        // 加载示例图片
        drawable = ContextCompat.getDrawable(context, R.drawable.ic_launcher_background)
    }
    
    /**
     * 测量视图大小
     */
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        
        // 设置视图边界
        viewBounds.set(0f, 0f, measuredWidth.toFloat(), measuredHeight.toFloat())
        
        // 设置内容边界
        drawable?.let {
            val drawableWidth = it.intrinsicWidth.toFloat()
            val drawableHeight = it.intrinsicHeight.toFloat()
            
            // 计算内容边界，使其居中
            val left = (measuredWidth - drawableWidth) / 2
            val top = (measuredHeight - drawableHeight) / 2
            contentBounds.set(left, top, left + drawableWidth, top + drawableHeight)
            
            // 重置矩阵
            matrix.reset()
            matrix.setRectToRect(contentBounds, viewBounds, Matrix.ScaleToFit.CENTER)
            
            // 更新内容边界
            matrix.mapRect(contentBounds)
        }
    }
    
    /**
     * 绘制视图
     */
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // 保存画布状态
        canvas.save()
        
        // 应用变换矩阵
        canvas.concat(matrix)
        
        // 绘制内容
        drawable?.let {
            it.setBounds(
                contentBounds.left.toInt(),
                contentBounds.top.toInt(),
                contentBounds.right.toInt(),
                contentBounds.bottom.toInt()
            )
            it.draw(canvas)
        }
        
        // 恢复画布状态
        canvas.restore()
    }
    
    /**
     * 处理触摸事件
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 将事件传递给手势检测器
        val scaleResult = scaleGestureDetector.onTouchEvent(event)
        val gestureResult = gestureDetector.onTouchEvent(event)
        
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 停止惯性滚动
                if (!scroller.isFinished) {
                    scroller.abortAnimation()
                }
                
                // 记录触摸点
                lastTouch.set(event.x, event.y)
                isDragging = true
            }
            
            MotionEvent.ACTION_MOVE -> {
                if (isDragging && event.pointerCount == 1 && !scaleGestureDetector.isInProgress) {
                    // 计算移动距离
                    val dx = event.x - lastTouch.x
                    val dy = event.y - lastTouch.y
                    
                    // 应用平移
                    matrix.postTranslate(dx, dy)
                    
                    // 限制边界
                    constrainMatrix()
                    
                    // 更新最后触摸点
                    lastTouch.set(event.x, event.y)
                    
                    // 重绘视图
                    invalidate()
                }
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isDragging = false
            }
        }
        
        return scaleResult || gestureResult || super.onTouchEvent(event)
    }
    
    /**
     * 计算滚动
     */
    override fun computeScroll() {
        if (scroller.computeScrollOffset()) {
            // 获取当前矩阵的平移值
            matrix.getValues(matrixValues)
            
            // 计算新的平移值
            val currX = scroller.currX.toFloat()
            val currY = scroller.currY.toFloat()
            
            // 应用平移
            matrix.postTranslate(currX - matrixValues[Matrix.MTRANS_X], currY - matrixValues[Matrix.MTRANS_Y])
            
            // 限制边界
            constrainMatrix()
            
            // 重绘视图
            ViewCompat.postInvalidateOnAnimation(this)
        }
    }
    
    /**
     * 限制矩阵，防止内容滑出可视区域
     */
    private fun constrainMatrix() {
        // 获取当前矩阵的值
        matrix.getValues(matrixValues)
        
        // 获取当前的缩放和平移值
        val scale = matrixValues[Matrix.MSCALE_X]
        var transX = matrixValues[Matrix.MTRANS_X]
        var transY = matrixValues[Matrix.MTRANS_Y]
        
        // 计算内容的宽高
        val contentWidth = contentBounds.width() * scale
        val contentHeight = contentBounds.height() * scale
        
        // 计算内容的左上角和右下角坐标
        val contentLeft = contentBounds.left * scale + transX
        val contentTop = contentBounds.top * scale + transY
        val contentRight = contentLeft + contentWidth
        val contentBottom = contentTop + contentHeight
        
        // 限制水平方向的平移
        if (contentWidth <= viewBounds.width()) {
            // 如果内容宽度小于视图宽度，则居中显示
            transX = (viewBounds.width() - contentWidth) / 2 - contentBounds.left * scale
        } else {
            // 如果内容宽度大于视图宽度，则限制边界
            if (contentLeft > 0) {
                transX -= contentLeft
            } else if (contentRight < viewBounds.width()) {
                transX += viewBounds.width() - contentRight
            }
        }
        
        // 限制垂直方向的平移
        if (contentHeight <= viewBounds.height()) {
            // 如果内容高度小于视图高度，则居中显示
            transY = (viewBounds.height() - contentHeight) / 2 - contentBounds.top * scale
        } else {
            // 如果内容高度大于视图高度，则限制边界
            if (contentTop > 0) {
                transY -= contentTop
            } else if (contentBottom < viewBounds.height()) {
                transY += viewBounds.height() - contentBottom
            }
        }
        
        // 更新矩阵
        matrix.reset()
        matrix.postScale(scale, scale)
        matrix.postTranslate(transX, transY)
    }
    
    /**
     * 缩放监听器
     */
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            // 获取当前矩阵的值
            matrix.getValues(matrixValues)
            
            // 获取当前的缩放值
            val currentScale = matrixValues[Matrix.MSCALE_X]
            
            // 计算新的缩放值
            var newScale = currentScale * detector.scaleFactor
            
            // 限制缩放范围
            newScale = max(minScale, min(newScale, maxScale))
            
            // 计算缩放因子
            val scaleFactor = newScale / currentScale
            
            // 获取缩放中心点
            val focusX = detector.focusX
            val focusY = detector.focusY
            
            // 应用缩放
            matrix.postScale(scaleFactor, scaleFactor, focusX, focusY)
            
            // 限制边界
            constrainMatrix()
            
            // 更新当前缩放值
            <EMAIL> = newScale
            
            // 重绘视图
            invalidate()
            
            return true
        }
    }
    
    /**
     * 手势监听器
     */
    private inner class GestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onDown(e: MotionEvent): Boolean {
            return true
        }
        
        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            // 获取当前矩阵的值
            matrix.getValues(matrixValues)
            
            // 获取当前的平移值
            val transX = matrixValues[Matrix.MTRANS_X]
            val transY = matrixValues[Matrix.MTRANS_Y]
            
            // 启动惯性滚动
            scroller.fling(
                transX.toInt(),
                transY.toInt(),
                velocityX.toInt() / 2, // 减小速度以获得更好的用户体验
                velocityY.toInt() / 2,
                Int.MIN_VALUE,
                Int.MAX_VALUE,
                Int.MIN_VALUE,
                Int.MAX_VALUE
            )
            
            // 触发重绘
            ViewCompat.postInvalidateOnAnimation(this@MultiTouchView)
            
            return true
        }
        
        override fun onDoubleTap(e: MotionEvent): Boolean {
            // 双击时重置缩放
            matrix.reset()
            matrix.setRectToRect(contentBounds, viewBounds, Matrix.ScaleToFit.CENTER)
            currentScale = 1.0f
            invalidate()
            return true
        }
    }
    
    /**
     * 设置图片资源
     */
    fun setImageResource(resId: Int) {
        drawable = ContextCompat.getDrawable(context, resId)
        requestLayout()
        invalidate()
    }
    
    /**
     * 设置图片
     */
    fun setImageDrawable(drawable: Drawable?) {
        this.drawable = drawable
        requestLayout()
        invalidate()
    }
    
    /**
     * 设置缩放范围
     */
    fun setScaleRange(minScale: Float, maxScale: Float) {
        this.minScale = minScale
        this.maxScale = maxScale
    }
    
    /**
     * 重置视图状态
     */
    fun reset() {
        matrix.reset()
        matrix.setRectToRect(contentBounds, viewBounds, Matrix.ScaleToFit.CENTER)
        currentScale = 1.0f
        invalidate()
    }
}
