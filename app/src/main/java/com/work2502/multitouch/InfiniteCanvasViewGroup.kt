package com.work2502.multitouch

import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.PointF
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ViewConfiguration
import android.widget.FrameLayout
import android.widget.OverScroller
import androidx.core.view.ViewCompat
import androidx.core.view.children
import kotlin.math.abs

/**
 * 无限画布ViewGroup
 * 支持上下左右滑动，可以在无限空间中导航
 */
class InfiniteCanvasViewGroup @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 变换矩阵，用于应用平移
    private val transformMatrix = Matrix()
    
    // 用于保存矩阵的值
    private val matrixValues = FloatArray(9)
    
    // 视图的边界
    private val viewBounds = PointF()
    
    // 内容的边界
    private val contentBounds = PointF()
    
    // 普通手势检测器
    private val gestureDetector: GestureDetector
    
    // 用于惯性滚动
    private val scroller: OverScroller
    
    // 最后一次触摸点
    private val lastTouch = PointF()
    
    // 按下时的触摸点
    private val downPoint = PointF()
    
    // 是否正在拖动
    private var isDragging = false
    
    // 是否处理子视图点击
    private var isHandlingChildClick = false
    
    // 触摸滑动阈值
    private val touchSlop: Int
    
    // 最小移动距离，用于区分点击和拖动
    private val minMoveDistance: Int
    
    // 是否已经初始化
    private var isInitialized = false
    
    // 当前偏移量
    private var offsetX = 0f
    private var offsetY = 0f
    
    // 是否启用边界限制
    private var enableBoundaryLimit = false
    
    // 边界限制的边距
    private var boundaryMargin = 100f
    
    // 初始化
    init {
        // 设置可获取焦点
        isFocusable = true
        
        // 获取系统定义的触摸滑动阈值
        val configuration = ViewConfiguration.get(context)
        touchSlop = configuration.scaledTouchSlop
        minMoveDistance = touchSlop * 2
        
        // 初始化普通手势检测器
        gestureDetector = GestureDetector(context, GestureListener())
        
        // 初始化滚动器
        scroller = OverScroller(context)
        
        // 设置绘制顺序
        setWillNotDraw(false)
        
        // 设置裁剪子视图
        clipChildren = false
    }
    
    /**
     * 测量视图大小
     */
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        
        // 设置视图边界
        viewBounds.set(measuredWidth.toFloat(), measuredHeight.toFloat())
        
        // 如果还没有初始化，则初始化内容边界
        if (!isInitialized && childCount > 0) {
            // 更新内容边界
            updateContentBounds()
            
            // 重置矩阵
            transformMatrix.reset()
            
            // 标记为已初始化
            isInitialized = true
        }
    }
    
    /**
     * 布局子视图
     */
    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        
        // 如果布局发生变化，重新计算内容边界
        if (changed) {
            updateContentBounds()
        }
    }
    
    /**
     * 更新内容边界
     */
    private fun updateContentBounds() {
        if (childCount > 0) {
            // 重置内容边界
            var minX = Float.MAX_VALUE
            var minY = Float.MAX_VALUE
            var maxX = Float.MIN_VALUE
            var maxY = Float.MIN_VALUE
            
            // 遍历所有子视图，计算内容边界
            for (child in children) {
                val childLeft = child.left.toFloat()
                val childTop = child.top.toFloat()
                val childRight = childLeft + child.width
                val childBottom = childTop + child.height
                
                // 扩展内容边界以包含所有子视图
                minX = minX.coerceAtMost(childLeft)
                minY = minY.coerceAtMost(childTop)
                maxX = maxX.coerceAtLeast(childRight)
                maxY = maxY.coerceAtLeast(childBottom)
            }
            
            // 设置内容边界
            contentBounds.set(maxX - minX, maxY - minY)
            
            // 如果内容边界无效，使用视图边界
            if (contentBounds.x <= 0 || contentBounds.y <= 0) {
                contentBounds.set(viewBounds.x, viewBounds.y)
            }
        } else {
            // 如果没有子视图，使用视图边界
            contentBounds.set(viewBounds.x, viewBounds.y)
        }
    }
    
    /**
     * 分发触摸事件
     */
    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        // 处理ACTION_DOWN事件，记录按下点
        if (ev.actionMasked == MotionEvent.ACTION_DOWN) {
            downPoint.set(ev.x, ev.y)
            isHandlingChildClick = false
        }
        
        // 创建一个新的事件，应用逆矩阵变换
        val transformedEvent = transformTouchEvent(ev)
        
        // 分发变换后的事件
        val result = super.dispatchTouchEvent(transformedEvent)
        
        // 如果子视图处理了事件，标记为子视图点击
        if (result && ev.actionMasked == MotionEvent.ACTION_DOWN) {
            isHandlingChildClick = true
        }
        
        // 回收变换后的事件
        transformedEvent.recycle()
        
        return result
    }
    
    /**
     * 变换触摸事件坐标
     */
    private fun transformTouchEvent(event: MotionEvent): MotionEvent {
        // 创建一个新的事件
        val transformedEvent = MotionEvent.obtain(event)
        
        // 创建一个逆矩阵
        val inverseMatrix = Matrix()
        transformMatrix.invert(inverseMatrix)
        
        // 应用逆矩阵变换
        transformedEvent.transform(inverseMatrix)
        
        return transformedEvent
    }
    
    /**
     * 拦截触摸事件
     */
    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 记录触摸点
                lastTouch.set(ev.x, ev.y)
                downPoint.set(ev.x, ev.y)
                isDragging = false
                
                // 停止惯性滚动
                if (!scroller.isFinished) {
                    scroller.abortAnimation()
                    return true
                }
            }
            
            MotionEvent.ACTION_MOVE -> {
                // 如果子视图正在处理点击，不拦截
                if (isHandlingChildClick) {
                    return false
                }
                
                // 计算移动距离
                val dx = abs(ev.x - downPoint.x)
                val dy = abs(ev.y - downPoint.y)
                
                // 如果移动距离超过阈值，则拦截事件
                if (dx > touchSlop || dy > touchSlop) {
                    isDragging = true
                    // 更新最后触摸点
                    lastTouch.set(ev.x, ev.y)
                    // 请求父视图不要拦截触摸事件
                    parent?.requestDisallowInterceptTouchEvent(true)
                    return true
                }
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                // 重置状态
                isDragging = false
                // 允许父视图拦截触摸事件
                parent?.requestDisallowInterceptTouchEvent(false)
            }
        }
        
        return super.onInterceptTouchEvent(ev)
    }
    
    /**
     * 处理触摸事件
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 如果子视图正在处理点击，不处理触摸事件
        if (isHandlingChildClick && event.pointerCount == 1) {
            return false
        }
        
        // 将事件传递给手势检测器
        val gestureResult = gestureDetector.onTouchEvent(event)
        
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 停止惯性滚动
                if (!scroller.isFinished) {
                    scroller.abortAnimation()
                }
                
                // 记录触摸点
                lastTouch.set(event.x, event.y)
                downPoint.set(event.x, event.y)
                isDragging = false
                
                // 请求父视图不要拦截触摸事件
                parent?.requestDisallowInterceptTouchEvent(true)
            }
            
            MotionEvent.ACTION_MOVE -> {
                // 计算移动距离
                val dx = abs(event.x - downPoint.x)
                val dy = abs(event.y - downPoint.y)
                
                // 如果超过滑动阈值，则认为是拖动
                if (dx > touchSlop || dy > touchSlop) {
                    isDragging = true
                }
                
                // 如果正在拖动
                if (isDragging) {
                    // 计算移动距离
                    val moveX = event.x - lastTouch.x
                    val moveY = event.y - lastTouch.y
                    
                    // 更新偏移量
                    offsetX += moveX
                    offsetY += moveY
                    
                    // 应用平移
                    transformMatrix.postTranslate(moveX, moveY)
                    
                    // 如果启用边界限制，则限制边界
                    if (enableBoundaryLimit) {
                        constrainMatrix()
                    }
                    
                    // 更新最后触摸点
                    lastTouch.set(event.x, event.y)
                    
                    // 重绘视图
                    invalidate()
                }
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                // 如果没有拖动过，则可能是点击
                if (!isDragging && event.actionMasked == MotionEvent.ACTION_UP) {
                    // 如果是点击事件，则传递给子视图
                    performClick()
                }
                
                // 重置状态
                isDragging = false
                
                // 允许父视图拦截触摸事件
                parent?.requestDisallowInterceptTouchEvent(false)
            }
        }
        
        return gestureResult || super.onTouchEvent(event)
    }
    
    /**
     * 绘制视图
     */
    override fun dispatchDraw(canvas: Canvas) {
        // 保存画布状态
        canvas.save()
        
        // 应用变换矩阵
        canvas.concat(transformMatrix)
        
        // 调用父类的绘制方法
        super.dispatchDraw(canvas)
        
        // 恢复画布状态
        canvas.restore()
    }
    
    /**
     * 计算滚动
     */
    override fun computeScroll() {
        if (scroller.computeScrollOffset()) {
            // 获取当前矩阵的平移值
            transformMatrix.getValues(matrixValues)
            
            // 计算新的平移值
            val currX = scroller.currX.toFloat()
            val currY = scroller.currY.toFloat()
            
            // 如果当前没有处理子视图点击，才应用滚动
            if (!isHandlingChildClick) {
                // 更新偏移量
                offsetX = currX
                offsetY = currY
                
                // 应用平移
                transformMatrix.setTranslate(currX, currY)
                
                // 如果启用边界限制，则限制边界
                if (enableBoundaryLimit) {
                    constrainMatrix()
                }
                
                // 重绘视图
                ViewCompat.postInvalidateOnAnimation(this)
            } else {
                // 如果正在处理子视图点击，停止滚动
                scroller.abortAnimation()
            }
        }
    }
    
    /**
     * 限制矩阵，防止内容滑出可视区域
     */
    private fun constrainMatrix() {
        // 获取当前矩阵的值
        transformMatrix.getValues(matrixValues)
        
        // 获取当前的平移值
        var transX = matrixValues[Matrix.MTRANS_X]
        var transY = matrixValues[Matrix.MTRANS_Y]
        
        // 记录原始值，用于检测是否发生变化
        val originalTransX = transX
        val originalTransY = transY
        
        // 计算边界
        val minX = -(contentBounds.x + boundaryMargin)
        val maxX = boundaryMargin
        val minY = -(contentBounds.y + boundaryMargin)
        val maxY = boundaryMargin
        
        // 限制平移值
        transX = transX.coerceIn(minX, maxX)
        transY = transY.coerceIn(minY, maxY)
        
        // 检测是否发生了显著变化
        val hasSignificantChange = abs(transX - originalTransX) > 0.1f || abs(transY - originalTransY) > 0.1f
        
        // 更新矩阵
        if (hasSignificantChange) {
            // 更新偏移量
            offsetX = transX
            offsetY = transY
            
            // 更新矩阵
            transformMatrix.setTranslate(transX, transY)
        }
    }
    
    /**
     * 手势监听器
     */
    private inner class GestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onDown(e: MotionEvent): Boolean {
            return true
        }
        
        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            // 单击确认，如果没有子视图处理，可以在这里处理单击事件
            return false
        }
        
        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            // 如果正在处理子视图点击，不处理惯性滚动
            if (isHandlingChildClick) {
                return false
            }
            
            // 启动惯性滚动
            scroller.fling(
                offsetX.toInt(),
                offsetY.toInt(),
                velocityX.toInt() / 2,
                velocityY.toInt() / 2,
                Int.MIN_VALUE,
                Int.MAX_VALUE,
                Int.MIN_VALUE,
                Int.MAX_VALUE
            )
            
            // 触发重绘
            ViewCompat.postInvalidateOnAnimation(this@InfiniteCanvasViewGroup)
            
            return true
        }
        
        override fun onDoubleTap(e: MotionEvent): Boolean {
            // 双击时重置位置
            reset()
            return true
        }
    }
    
    /**
     * 重置视图状态
     */
    fun reset() {
        // 停止所有正在进行的滚动
        if (!scroller.isFinished) {
            scroller.abortAnimation()
        }
        
        // 重置状态
        isDragging = false
        isHandlingChildClick = false
        
        // 重置偏移量
        offsetX = 0f
        offsetY = 0f
        
        // 重置矩阵
        transformMatrix.reset()
        
        // 重绘视图
        invalidate()
        
        // 记录日志
        Log.d(TAG, "View reset to initial state")
    }
    
    /**
     * 设置是否启用边界限制
     */
    fun setEnableBoundaryLimit(enable: Boolean) {
        this.enableBoundaryLimit = enable
    }
    
    /**
     * 设置边界限制的边距
     */
    fun setBoundaryMargin(margin: Float) {
        this.boundaryMargin = margin
    }
    
    /**
     * 获取当前X轴偏移量
     */
    fun getOffsetX(): Float {
        return offsetX
    }
    
    /**
     * 获取当前Y轴偏移量
     */
    fun getOffsetY(): Float {
        return offsetY
    }
    
    /**
     * 设置偏移量
     */
    fun setOffset(x: Float, y: Float) {
        offsetX = x
        offsetY = y
        transformMatrix.setTranslate(x, y)
        invalidate()
    }
    
    /**
     * 当子视图添加时调用
     */
    override fun onViewAdded(child: android.view.View?) {
        super.onViewAdded(child)
        updateContentBounds()
    }
    
    /**
     * 当子视图移除时调用
     */
    override fun onViewRemoved(child: android.view.View?) {
        super.onViewRemoved(child)
        updateContentBounds()
    }
    
    companion object {
        // 日志标签
        private const val TAG = "InfiniteCanvasViewGroup"
    }
}
