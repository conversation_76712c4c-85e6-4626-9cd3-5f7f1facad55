package com.work2502.multitouch

import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.RenderEffect
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntSize
import kotlin.math.max
import kotlin.math.min

/**
 * 可缩放的Box组件
 * 支持缩放、平移等多点触控操作
 *
 * @param modifier 应用于Box的修饰符
 * @param minScale 最小缩放比例
 * @param maxScale 最大缩放比例
 * @param initialScale 初始缩放比例
 * @param enableInertia 是否启用惯性滚动
 * @param enableRotation 是否启用旋转
 * @param onScaleChanged 缩放比例变化时的回调
 * @param onOffsetChanged 偏移变化时的回调
 * @param content Box内容
 */
@Composable
fun ZoomableBox(
    modifier: Modifier = Modifier,
    minScale: Float = 0.5f,
    maxScale: Float = 5f,
    initialScale: Float = 1f,
    enableInertia: Boolean = true,
    enableRotation: Boolean = false,
    onScaleChanged: (Float) -> Unit = {},
    onOffsetChanged: (Offset) -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {
    // 记录当前的缩放比例
    var scale by remember { mutableFloatStateOf(initialScale) }
    
    // 记录当前的偏移量
    var offset by remember { mutableStateOf(Offset.Zero) }
    
    // 记录当前的旋转角度
    var rotation by remember { mutableFloatStateOf(0f) }
    
    // 记录容器大小
    var containerSize by remember { mutableStateOf(IntSize.Zero) }
    
    // 获取当前密度
    val density = LocalDensity.current
    
    // 创建Box组件
    Box(
        modifier = modifier
            .clipToBounds() // 裁剪超出边界的内容
            .onSizeChanged { containerSize = it } // 记录容器大小
            .pointerInput(Unit) {
                // 检测变换手势（缩放、旋转、平移）
                detectTransformGestures(
                    onGesture = { centroid, pan, gestureZoom, gestureRotation ->
                        // 计算新的缩放比例
                        val newScale = (scale * gestureZoom).coerceIn(minScale, maxScale)
                        
                        // 计算新的偏移量
                        val newOffset = offset + pan
                        
                        // 计算新的旋转角度（如果启用）
                        val newRotation = if (enableRotation) {
                            rotation + gestureRotation
                        } else {
                            rotation
                        }
                        
                        // 应用新的变换
                        scale = newScale
                        offset = constrainOffset(newOffset, newScale, containerSize)
                        rotation = newRotation
                        
                        // 调用回调
                        onScaleChanged(newScale)
                        onOffsetChanged(offset)
                    }
                )
            },
        content = {
            // 创建内容容器
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .graphicsLayer {
                        // 应用变换
//                        scaleX = scale
//                        scaleY = scale
                        translationX = offset.x
                        translationY = offset.y
//                        rotationZ = rotation
                        
                        // 设置变换原点为中心
                        transformOrigin = androidx.compose.ui.graphics.TransformOrigin(0.5f, 0.5f)
                        
                        // 设置裁剪模式
                        clip = true
                    }
            ) {
                // 渲染内容
                content()
            }
        }
    )
}

/**
 * 约束偏移量，防止内容滑出可视区域
 */
private fun constrainOffset(offset: Offset, scale: Float, containerSize: IntSize): Offset {
    // 计算内容的有效区域
    val contentWidth = containerSize.width * scale
    val contentHeight = containerSize.height * scale
    
    // 计算最大可滑动范围
    val maxX = (contentWidth - containerSize.width) / 2
    val maxY = (contentHeight - containerSize.height) / 2
    
    // 如果内容小于容器，则居中显示
    return if (scale <= 1.0f) {
        Offset.Zero
    } else {
        // 约束X轴偏移
        val constrainedX = offset.x.coerceIn(-maxX, maxX)
        // 约束Y轴偏移
        val constrainedY = offset.y.coerceIn(-maxY, maxY)
        
        Offset(constrainedX, constrainedY)
    }
}

/**
 * 重置缩放和偏移
 */
@Composable
fun rememberZoomableState(initialScale: Float = 1f) {
    var scale by remember { mutableFloatStateOf(initialScale) }
    var offset by remember { mutableStateOf(Offset.Zero) }
    var rotation by remember { mutableFloatStateOf(0f) }
    
    fun reset() {
        scale = initialScale
        offset = Offset.Zero
        rotation = 0f
    }
}
