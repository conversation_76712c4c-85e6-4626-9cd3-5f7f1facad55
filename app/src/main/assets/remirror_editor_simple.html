<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
<!--    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, viewport-fit=cover">
    <title>Remirror.js 富文本编辑器</title>

    <style>
        html, body {
            font-family: system-ui, -apple-system, sans-serif;
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        body {
            display: flex;
            flex-direction: column;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .editor-container {
            display: flex;
            flex-direction: column;
            flex: 1;
            height: 100%;
            background-color: white;
            overflow: hidden;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .editor-title {
            font-size: 20px;
            font-weight: 500;
            color: #333;
        }

        .editor-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 0;
            margin: 0;
            position: relative;
            transition: padding-bottom 0.3s ease;
        }

        /* 添加软键盘适配样式 */
        .keyboard-open .editor-content {
            padding-bottom: 0; /* 会被动态设置 */
        }

        .editor-toolbar {
            display: flex;
            flex-wrap: wrap;
            padding: 8px;
            background-color: #f9f9f9;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .toolbar-button {
            background-color: transparent;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            cursor: pointer;
            color: #333;
        }

        .toolbar-button:active {
            background-color: #e0e0e0;
        }

        .toolbar-button.is-active {
            background-color: #e0e0e0;
            border-color: #bbb;
        }

        .editor-area {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            outline: none;
            min-height: 200px;
        }

        .editor-actions {
            display: flex;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-top: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .editor-btn {
            background-color: #4285f4;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            margin-right: 12px;
            font-weight: 500;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }

        .editor-btn:active {
            background-color: #3367d6;
        }

        /* 待办事项样式 */
        .task-list {
            list-style-type: none;
            padding-left: 0;
        }

        .task-list-item {
            display: flex;
            align-items: flex-start;
            margin: 0.5em 0;
        }

        .task-checkbox {
            margin-right: 0.5em;
            margin-top: 0.25em;
        }

        .task-content {
            flex: 1;
        }

        .task-completed {
            text-decoration: line-through;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <div class="editor-header">
            <div class="editor-title">简化版富文本编辑器</div>
        </div>

        <div class="editor-toolbar" id="editor-toolbar">
            <button class="toolbar-button" data-command="bold" title="粗体">粗体22</button>
            <button class="toolbar-button" data-command="italic" title="斜体">斜体</button>
            <button class="toolbar-button" data-command="underline" title="下划线">下划线</button>
            <button class="toolbar-button" data-command="strikeThrough" title="删除线">删除线</button>
            <button class="toolbar-button" data-command="formatBlock" data-value="h1" title="标题1">标题1</button>
            <button class="toolbar-button" data-command="formatBlock" data-value="h2" title="标题2">标题2</button>
            <button class="toolbar-button" data-command="insertUnorderedList" title="无序列表">无序列表</button>
            <button class="toolbar-button" data-command="insertOrderedList" title="有序列表">有序列表</button>
            <button class="toolbar-button" data-command="insertHTML" data-value="<div class='task-list'><div class='task-list-item'><input type='checkbox' class='task-checkbox'><span class='task-content'>新待办事项</span></div></div>" title="待办事项">待办事项</button>
            <button class="toolbar-button" data-command="formatBlock" data-value="blockquote" title="引用">引用</button>
            <button class="toolbar-button" data-command="formatBlock" data-value="pre" title="代码块">代码块</button>
            <button class="toolbar-button" data-command="createLink" title="链接">链接</button>
            <button class="toolbar-button" data-command="removeFormat" title="清除格式">清除格式</button>
        </div>

        <div class="editor-content">
            <div class="editor-area" id="editor" contenteditable="true"></div>
        </div>

        <div class="editor-actions">
            <button class="editor-btn" onclick="saveContent()">保存内容</button>
            <button class="editor-btn" onclick="getHtml()">获取HTML</button>
            <button class="editor-btn" onclick="getText()">获取纯文本</button>
            <button class="editor-btn" onclick="clearCompletedTodos()" style="background-color: #ff5722;">清除已完成事项</button>
        </div>
    </div>

    <script>
        window.addEventListener('resize', () => {
  const activeElement = document.activeElement;
  if (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA') {
    activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
});
        // 初始化编辑器
        document.addEventListener('DOMContentLoaded', function() {
            // 设置初始内容
            document.getElementById('editor').innerHTML = `
                <h1>简化版富文本编辑器示例</h1>
                <p>这是一个简化版的富文本编辑器示例。</p>
                <h2>主要功能：</h2>
                <ul>
                    <li>文本格式化（粗体、斜体、下划线、删除线等）</li>
                    <li>标题（一级标题、二级标题）</li>
                    <li>列表（有序和无序）</li>
                    <li>待办事项列表</li>
                    <li>代码和引用块</li>
                    <li>链接</li>
                </ul>
                <p><em>试试看各种编辑功能吧！</em></p>
                <h2>待办事项示例：</h2>
                <div class="task-list">
                    <div class="task-list-item">
                        <input type="checkbox" class="task-checkbox">
                        <span class="task-content">这是一个待办事项</span>
                    </div>
                    <div class="task-list-item">
                        <input type="checkbox" class="task-checkbox" checked>
                        <span class="task-content task-completed">这是一个已完成的待办事项</span>
                    </div>
                    <div class="task-list-item">
                        <input type="checkbox" class="task-checkbox">
                        <span class="task-content">点击工具栏中的待办事项按钮添加新的待办事项</span>
                    </div>
                </div>
            `;

            // 初始化工具栏
            initToolbar();

            // 添加待办事项复选框事件监听
            document.getElementById('editor').addEventListener('change', function(e) {
                if (e.target.classList.contains('task-checkbox')) {
                    const content = e.target.nextElementSibling;
                    if (e.target.checked) {
                        content.classList.add('task-completed');
                    } else {
                        content.classList.remove('task-completed');
                    }
                }
            });

            // 处理软键盘适配
            window.adjustEditorForKeyboard = function(keyboardHeight) {
                const editorContent = document.querySelector('.editor-content');
                const editorContainer = document.querySelector('.editor-container');

                if (editorContent) {
                    if (keyboardHeight > 0) {
                        // 软键盘打开
                        document.body.classList.add('keyboard-open');
                        editorContent.style.paddingBottom = keyboardHeight + 'px';

                        // 滚动到当前焦点位置
                        setTimeout(() => {
                            const selection = window.getSelection();
                            if (selection.rangeCount > 0) {
                                const range = selection.getRangeAt(0);
                                const rect = range.getBoundingClientRect();

                                // 如果选区在视口下方，滚动到可见位置
                                if (rect.bottom > window.innerHeight - keyboardHeight) {
                                    const scrollTop = editorContent.scrollTop + (rect.bottom - (window.innerHeight - keyboardHeight)) + 20; // 额外20px空间
                                    editorContent.scrollTop = scrollTop;
                                }
                            }
                        }, 300); // 延迟确保键盘完全显示
                    } else {
                        // 软键盘关闭
                        document.body.classList.remove('keyboard-open');
                        editorContent.style.paddingBottom = '0';
                    }
                }
            };

            // 监听视口大小变化，自动检测软键盘
            const originalHeight = window.innerHeight;
            window.addEventListener('resize', function() {
                // 如果高度明显减小，可能是软键盘打开
                const heightDiff = originalHeight - window.innerHeight;
                if (heightDiff > 100) { // 通常软键盘高度大于100px
                    window.adjustEditorForKeyboard(heightDiff);
                } else {
                    window.adjustEditorForKeyboard(0);
                }
            });

            // 监听焦点变化，确保输入区域可见
            document.getElementById('editor').addEventListener('focus', function(e) {
                if (e.target.tagName === 'INPUT' || e.target.isContentEditable) {
                    // 延迟执行，确保软键盘已经打开
                    setTimeout(() => {
                        const heightDiff = originalHeight - window.innerHeight;
                        if (heightDiff > 100) {
                            window.adjustEditorForKeyboard(heightDiff);
                        }
                    }, 300);
                }
            }, true);

            window.resetEditorLayout = function() {
                const editorContent = document.querySelector('.editor-content');
                if (editorContent) {
                    editorContent.style.paddingBottom = '0px';
                }
            };
        });

        // 初始化工具栏
        function initToolbar() {
            const toolbar = document.getElementById('editor-toolbar');
            const buttons = toolbar.querySelectorAll('button');

            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    const command = this.dataset.command;
                    const value = this.dataset.value || '';

                    if (command === 'createLink') {
                        const url = prompt('请输入链接地址:', 'https://');
                        if (url) {
                            document.execCommand(command, false, url);
                        }
                    } else if (command === 'insertHTML') {
                        document.execCommand(command, false, value);
                    } else {
                        document.execCommand(command, false, value);
                    }

                    // 更新按钮状态
                    updateToolbarState();
                });
            });

            // 编辑器内容变化时更新工具栏状态
            document.getElementById('editor').addEventListener('keyup', updateToolbarState);
            document.getElementById('editor').addEventListener('mouseup', updateToolbarState);
        }

        // 更新工具栏状态
        function updateToolbarState() {
            const toolbar = document.getElementById('editor-toolbar');
            const buttons = toolbar.querySelectorAll('button');

            buttons.forEach(button => {
                const command = button.dataset.command;

                if (command === 'formatBlock') {
                    const value = button.dataset.value;
                    const formatValue = document.queryCommandValue('formatBlock');
                    button.classList.toggle('is-active', formatValue === value);
                } else if (command !== 'insertHTML' && command !== 'createLink' && command !== 'removeFormat') {
                    button.classList.toggle('is-active', document.queryCommandState(command));
                }
            });
        }

        // 保存内容到 Android
        function saveContent() {
            const content = document.getElementById('editor').innerHTML;
            // 调用 Android 的接口保存内容
            if (typeof Android !== 'undefined') {
                Android.saveContent(content);
            } else {
                alert('内容已保存：\n' + content.substring(0, 100) + '...');
            }
        }

        // 获取 HTML 内容
        function getHtml() {
            const html = document.getElementById('editor').innerHTML;
            if (typeof Android !== 'undefined') {
                Android.getHtmlContent(html);
            } else {
                alert('HTML内容：\n' + html.substring(0, 100) + '...');
            }
        }

        // 获取纯文本内容
        function getText() {
            const text = document.getElementById('editor').textContent;
            if (typeof Android !== 'undefined') {
                Android.getTextContent(text);
            } else {
                alert('纯文本内容：\n' + text.substring(0, 100) + '...');
            }
        }

        // 清除已完成的待办事项
        function clearCompletedTodos() {
            const editor = document.getElementById('editor');
            const completedItems = editor.querySelectorAll('.task-list-item input[type="checkbox"]:checked');

            if (completedItems.length === 0) {
                alert('没有已完成的待办事项');
                return;
            }

            completedItems.forEach(checkbox => {
                const item = checkbox.closest('.task-list-item');
                if (item) {
                    item.remove();
                }
            });

            alert(`已清除 ${completedItems.length} 个已完成的待办事项`);
        }
    </script>
</body>
</html>
