var e=Object.defineProperty,t=(t,n,r)=>((t,n,r)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r)(t,"symbol"!=typeof n?n+"":n,r);function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var r,o,i={exports:{}},s={};var l,a,c=(o||(o=1,i.exports=function(){if(r)return s;r=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function n(t,n,r){var o=null;if(void 0!==r&&(o=""+r),void 0!==n.key&&(o=""+n.key),"key"in n)for(var i in r={},n)"key"!==i&&(r[i]=n[i]);else r=n;return n=r.ref,{$$typeof:e,type:t,key:o,ref:void 0!==n?n:null,props:r}}return s.Fragment=t,s.jsx=n,s.jsxs=n,s}()),i.exports),u={exports:{}},d={};function f(){if(l)return d;l=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),s=Symbol.for("react.context"),a=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),u=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var k=b.prototype=new v;k.constructor=b,m(k,y.prototype),k.isPureReactComponent=!0;var w=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},x=Object.prototype.hasOwnProperty;function C(t,n,r,o,i,s){return r=s.ref,{$$typeof:e,type:t,key:n,ref:void 0!==r?r:null,props:s}}function E(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}var M=/\/+/g;function T(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function O(){}function N(n,r,o,i,s){var l=typeof n;"undefined"!==l&&"boolean"!==l||(n=null);var a,c,u=!1;if(null===n)u=!0;else switch(l){case"bigint":case"string":case"number":u=!0;break;case"object":switch(n.$$typeof){case e:case t:u=!0;break;case f:return N((u=n._init)(n._payload),r,o,i,s)}}if(u)return s=s(n),u=""===i?"."+T(n,0):i,w(s)?(o="",null!=u&&(o=u.replace(M,"$&/")+"/"),N(s,r,o,"",(function(e){return e}))):null!=s&&(E(s)&&(a=s,c=o+(null==s.key||n&&n.key===s.key?"":(""+s.key).replace(M,"$&/")+"/")+u,s=C(a.type,c,void 0,0,0,a.props)),r.push(s)),1;u=0;var d,p=""===i?".":i+":";if(w(n))for(var m=0;m<n.length;m++)u+=N(i=n[m],r,o,l=p+T(i,m),s);else if("function"==typeof(m=null===(d=n)||"object"!=typeof d?null:"function"==typeof(d=h&&d[h]||d["@@iterator"])?d:null))for(n=m.call(n),m=0;!(i=n.next()).done;)u+=N(i=i.value,r,o,l=p+T(i,m++),s);else if("object"===l){if("function"==typeof n.then)return N(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(O,O):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(n),r,o,i,s);throw r=String(n),Error("Objects are not valid as a React child (found: "+("[object Object]"===r?"object with keys {"+Object.keys(n).join(", ")+"}":r)+"). If you meant to render a collection of children, use an array instead.")}return u}function A(e,t,n){if(null==e)return e;var r=[],o=0;return N(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function D(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var P="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function R(){}return d.Children={map:A,forEach:function(e,t,n){A(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return A(e,(function(){t++})),t},toArray:function(e){return A(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},d.Component=y,d.Fragment=n,d.Profiler=o,d.PureComponent=b,d.StrictMode=r,d.Suspense=c,d.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,d.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},d.cache=function(e){return function(){return e.apply(null,arguments)}},d.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),o=e.key;if(null!=t)for(i in void 0!==t.ref&&void 0,void 0!==t.key&&(o=""+t.key),t)!x.call(t,i)||"key"===i||"__self"===i||"__source"===i||"ref"===i&&void 0===t.ref||(r[i]=t[i]);var i=arguments.length-2;if(1===i)r.children=n;else if(1<i){for(var s=Array(i),l=0;l<i;l++)s[l]=arguments[l+2];r.children=s}return C(e.type,o,void 0,0,0,r)},d.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},d.createElement=function(e,t,n){var r,o={},i=null;if(null!=t)for(r in void 0!==t.key&&(i=""+t.key),t)x.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var s=arguments.length-2;if(1===s)o.children=n;else if(1<s){for(var l=Array(s),a=0;a<s;a++)l[a]=arguments[a+2];o.children=l}if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===o[r]&&(o[r]=s[r]);return C(e,i,void 0,0,0,o)},d.createRef=function(){return{current:null}},d.forwardRef=function(e){return{$$typeof:a,render:e}},d.isValidElement=E,d.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:D}},d.memo=function(e,t){return{$$typeof:u,type:e,compare:void 0===t?null:t}},d.startTransition=function(e){var t=S.T,n={};S.T=n;try{var r=e(),o=S.S;null!==o&&o(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(R,P)}catch(i){P(i)}finally{S.T=t}},d.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},d.use=function(e){return S.H.use(e)},d.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},d.useCallback=function(e,t){return S.H.useCallback(e,t)},d.useContext=function(e){return S.H.useContext(e)},d.useDebugValue=function(){},d.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},d.useEffect=function(e,t,n){var r=S.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},d.useId=function(){return S.H.useId()},d.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},d.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},d.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},d.useMemo=function(e,t){return S.H.useMemo(e,t)},d.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},d.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},d.useRef=function(e){return S.H.useRef(e)},d.useState=function(e){return S.H.useState(e)},d.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},d.useTransition=function(){return S.H.useTransition()},d.version="19.1.0",d}function h(){return a||(a=1,u.exports=f()),u.exports}var p=h();const m=n(p);var g,y,v={exports:{}},b={},k={exports:{}},w={};function S(){return y||(y=1,k.exports=(g||(g=1,function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,i=e[r];if(!(0<o(i,t)))break e;e[r]=t,e[n]=i,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length,s=i>>>1;r<s;){var l=2*(r+1)-1,a=e[l],c=l+1,u=e[c];if(0>o(a,n))c<i&&0>o(u,a)?(e[r]=u,e[c]=n,r=c):(e[r]=a,e[l]=n,r=l);else{if(!(c<i&&0>o(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(e.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],c=[],u=1,d=null,f=3,h=!1,p=!1,m=!1,g=!1,y="function"==typeof setTimeout?setTimeout:null,v="function"==typeof clearTimeout?clearTimeout:null,b="undefined"!=typeof setImmediate?setImmediate:null;function k(e){for(var o=n(c);null!==o;){if(null===o.callback)r(c);else{if(!(o.startTime<=e))break;r(c),o.sortIndex=o.expirationTime,t(a,o)}o=n(c)}}function w(e){if(m=!1,k(e),!p)if(null!==n(a))p=!0,x||(x=!0,S());else{var t=n(c);null!==t&&D(w,t.startTime-e)}}var S,x=!1,C=-1,E=5,M=-1;function T(){return!(!g&&e.unstable_now()-M<E)}function O(){if(g=!1,x){var t=e.unstable_now();M=t;var o=!0;try{e:{p=!1,m&&(m=!1,v(C),C=-1),h=!0;var i=f;try{t:{for(k(t),d=n(a);null!==d&&!(d.expirationTime>t&&T());){var s=d.callback;if("function"==typeof s){d.callback=null,f=d.priorityLevel;var l=s(d.expirationTime<=t);if(t=e.unstable_now(),"function"==typeof l){d.callback=l,k(t),o=!0;break t}d===n(a)&&r(a),k(t)}else r(a);d=n(a)}if(null!==d)o=!0;else{var u=n(c);null!==u&&D(w,u.startTime-t),o=!1}}break e}finally{d=null,f=i,h=!1}o=void 0}}finally{o?S():x=!1}}}if("function"==typeof b)S=function(){b(O)};else if("undefined"!=typeof MessageChannel){var N=new MessageChannel,A=N.port2;N.port1.onmessage=O,S=function(){A.postMessage(null)}}else S=function(){y(O,0)};function D(t,n){C=y((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<e?Math.floor(1e3/e):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_requestPaint=function(){g=!0},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(r,o,i){var s=e.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?s+i:s,r){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return r={id:u++,callback:o,priorityLevel:r,startTime:i,expirationTime:l=i+l,sortIndex:-1},i>s?(r.sortIndex=i,t(c,r),null===n(a)&&r===n(c)&&(m?(v(C),C=-1):m=!0,D(w,i-s))):(r.sortIndex=l,t(a,r),p||h||(p=!0,x||(x=!0,S()))),r},e.unstable_shouldYield=T,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(w)),w)),k.exports}var x,C,E,M,T={exports:{}},O={};function N(){if(x)return O;x=1;var e=h();function t(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function n(){}var r={d:{f:n,r:function(){throw Error(t(522))},D:n,C:n,L:n,m:n,X:n,S:n,M:n},p:0,findDOMNode:null},o=Symbol.for("react.portal");var i=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function s(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}return O.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,O.createPortal=function(e,n){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(t(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:o,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,n,null,r)},O.flushSync=function(e){var t=i.T,n=r.p;try{if(i.T=null,r.p=2,e)return e()}finally{i.T=t,r.p=n,r.d.f()}},O.preconnect=function(e,t){"string"==typeof e&&(t?t="string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,r.d.C(e,t))},O.prefetchDNS=function(e){"string"==typeof e&&r.d.D(e)},O.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,o=s(n,t.crossOrigin),i="string"==typeof t.integrity?t.integrity:void 0,l="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?r.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:o,integrity:i,fetchPriority:l}):"script"===n&&r.d.X(e,{crossOrigin:o,integrity:i,fetchPriority:l,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},O.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=s(t.as,t.crossOrigin);r.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&r.d.M(e)},O.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,o=s(n,t.crossOrigin);r.d.L(e,n,{crossOrigin:o,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},O.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=s(t.as,t.crossOrigin);r.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else r.d.m(e)},O.requestFormReset=function(e){r.d.r(e)},O.unstable_batchedUpdates=function(e,t){return e(t)},O.useFormState=function(e,t,n){return i.H.useFormState(e,t,n)},O.useFormStatus=function(){return i.H.useHostTransitionStatus()},O.version="19.1.0",O}function A(){if(C)return T.exports;return C=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),T.exports=N(),T.exports}
/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function D(){if(E)return b;E=1;var e=S(),t=h(),n=A();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function i(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function s(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function l(e){if(i(e)!==e)throw Error(r(188))}function a(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=a(e)))return t;e=e.sibling}return null}var c=Object.assign,u=Symbol.for("react.element"),d=Symbol.for("react.transitional.element"),f=Symbol.for("react.portal"),p=Symbol.for("react.fragment"),m=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),y=Symbol.for("react.provider"),v=Symbol.for("react.consumer"),k=Symbol.for("react.context"),w=Symbol.for("react.forward_ref"),x=Symbol.for("react.suspense"),C=Symbol.for("react.suspense_list"),M=Symbol.for("react.memo"),T=Symbol.for("react.lazy"),O=Symbol.for("react.activity"),N=Symbol.for("react.memo_cache_sentinel"),D=Symbol.iterator;function P(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=D&&e[D]||e["@@iterator"])?e:null}var R=Symbol.for("react.client.reference");function L(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===R?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case p:return"Fragment";case g:return"Profiler";case m:return"StrictMode";case x:return"Suspense";case C:return"SuspenseList";case O:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case f:return"Portal";case k:return(e.displayName||"Context")+".Provider";case v:return(e._context.displayName||"Context")+".Consumer";case w:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case M:return null!==(t=e.displayName||null)?t:L(e.type)||"Memo";case T:t=e._payload,e=e._init;try{return L(e(t))}catch(n){}}return null}var z=Array.isArray,I=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,_=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$={pending:!1,data:null,method:null,action:null},F=[],B=-1;function j(e){return{current:e}}function H(e){0>B||(e.current=F[B],F[B]=null,B--)}function V(e,t){B++,F[B]=e.current,e.current=t}var U=j(null),W=j(null),q=j(null),K=j(null);function J(e,t){switch(V(q,t),V(W,e),V(U,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?sd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ld(t=sd(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}H(U),V(U,e)}function Q(){H(U),H(W),H(q)}function G(e){null!==e.memoizedState&&V(K,e);var t=U.current,n=ld(t,e.type);t!==n&&(V(W,e),V(U,n))}function Y(e){W.current===e&&(H(U),H(W)),K.current===e&&(H(K),Gd._currentValue=$)}var X=Object.prototype.hasOwnProperty,Z=e.unstable_scheduleCallback,ee=e.unstable_cancelCallback,te=e.unstable_shouldYield,ne=e.unstable_requestPaint,re=e.unstable_now,oe=e.unstable_getCurrentPriorityLevel,ie=e.unstable_ImmediatePriority,se=e.unstable_UserBlockingPriority,le=e.unstable_NormalPriority,ae=e.unstable_LowPriority,ce=e.unstable_IdlePriority,ue=e.log,de=e.unstable_setDisableYieldValue,fe=null,he=null;function pe(e){if("function"==typeof ue&&de(e),he&&"function"==typeof he.setStrictMode)try{he.setStrictMode(fe,e)}catch(t){}}var me=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(ge(e)/ye|0)|0},ge=Math.log,ye=Math.LN2;var ve=256,be=4194304;function ke(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function we(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var o=0,i=e.suspendedLanes,s=e.pingedLanes;e=e.warmLanes;var l=134217727&r;return 0!==l?0!==(r=l&~i)?o=ke(r):0!==(s&=l)?o=ke(s):n||0!==(n=l&~e)&&(o=ke(n)):0!==(l=r&~i)?o=ke(l):0!==s?o=ke(s):n||0!==(n=r&~e)&&(o=ke(n)),0===o?0:0!==t&&t!==o&&0===(t&i)&&((i=o&-o)>=(n=t&-t)||32===i&&4194048&n)?t:o}function Se(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function xe(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function Ce(){var e=ve;return!(4194048&(ve<<=1))&&(ve=256),e}function Ee(){var e=be;return!(62914560&(be<<=1))&&(be=4194304),e}function Me(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Te(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Oe(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-me(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Ne(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-me(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}function Ae(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function De(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function Pe(){var e=_.p;return 0!==e?e:void 0===(e=window.event)?32:ff(e.type)}var Re=Math.random().toString(36).slice(2),Le="__reactFiber$"+Re,ze="__reactProps$"+Re,Ie="__reactContainer$"+Re,_e="__reactEvents$"+Re,$e="__reactListeners$"+Re,Fe="__reactHandles$"+Re,Be="__reactResources$"+Re,je="__reactMarker$"+Re;function He(e){delete e[Le],delete e[ze],delete e[_e],delete e[$e],delete e[Fe]}function Ve(e){var t=e[Le];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ie]||n[Le]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wd(e);null!==e;){if(n=e[Le])return n;e=wd(e)}return t}n=(e=n).parentNode}return null}function Ue(e){if(e=e[Le]||e[Ie]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function We(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(r(33))}function qe(e){var t=e[Be];return t||(t=e[Be]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ke(e){e[je]=!0}var Je=new Set,Qe={};function Ge(e,t){Ye(e,t),Ye(e+"Capture",t)}function Ye(e,t){for(Qe[e]=t,e=0;e<t.length;e++)Je.add(t[e])}var Xe,Ze,et=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),tt={},nt={};function rt(e,t,n){if(o=t,X.call(nt,o)||!X.call(tt,o)&&(et.test(o)?nt[o]=!0:(tt[o]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var o}function ot(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function it(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function st(e){if(void 0===Xe)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Xe=t&&t[1]||"",Ze=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Xe+e+Ze}var lt=!1;function at(e,t){if(!e||lt)return"";lt=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(o){var r=o}Reflect.construct(e,[],n)}else{try{n.call()}catch(i){r=i}e.call(n.prototype)}}else{try{throw Error()}catch(s){r=s}(n=e())&&"function"==typeof n.catch&&n.catch((function(){}))}}catch(l){if(l&&r&&"string"==typeof l.stack)return[l.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=r.DetermineComponentFrameRoot(),s=i[0],l=i[1];if(s&&l){var a=s.split("\n"),c=l.split("\n");for(o=r=0;r<a.length&&!a[r].includes("DetermineComponentFrameRoot");)r++;for(;o<c.length&&!c[o].includes("DetermineComponentFrameRoot");)o++;if(r===a.length||o===c.length)for(r=a.length-1,o=c.length-1;1<=r&&0<=o&&a[r]!==c[o];)o--;for(;1<=r&&0<=o;r--,o--)if(a[r]!==c[o]){if(1!==r||1!==o)do{if(r--,0>--o||a[r]!==c[o]){var u="\n"+a[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=o);break}}}finally{lt=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?st(n):""}function ct(e){switch(e.tag){case 26:case 27:case 5:return st(e.type);case 16:return st("Lazy");case 13:return st("Suspense");case 19:return st("SuspenseList");case 0:case 15:return at(e.type,!1);case 11:return at(e.type.render,!1);case 1:return at(e.type,!0);case 31:return st("Activity");default:return""}}function ut(e){try{var t="";do{t+=ct(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function dt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ft(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function ht(e){e._valueTracker||(e._valueTracker=function(e){var t=ft(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function pt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ft(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function mt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var gt=/[\n"\\]/g;function yt(e){return e.replace(gt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function vt(e,t,n,r,o,i,s,l){e.name="",null!=s&&"function"!=typeof s&&"symbol"!=typeof s&&"boolean"!=typeof s?e.type=s:e.removeAttribute("type"),null!=t?"number"===s?(0===t&&""===e.value||e.value!=t)&&(e.value=""+dt(t)):e.value!==""+dt(t)&&(e.value=""+dt(t)):"submit"!==s&&"reset"!==s||e.removeAttribute("value"),null!=t?kt(e,s,dt(t)):null!=n?kt(e,s,dt(n)):null!=r&&e.removeAttribute("value"),null==o&&null!=i&&(e.defaultChecked=!!i),null!=o&&(e.checked=o&&"function"!=typeof o&&"symbol"!=typeof o),null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l?e.name=""+dt(l):e.removeAttribute("name")}function bt(e,t,n,r,o,i,s,l){if(null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i&&(e.type=i),null!=t||null!=n){if(("submit"===i||"reset"===i)&&null==t)return;n=null!=n?""+dt(n):"",t=null!=t?""+dt(t):n,l||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:o)&&"symbol"!=typeof r&&!!r,e.checked=l?e.checked:!!r,e.defaultChecked=!!r,null!=s&&"function"!=typeof s&&"symbol"!=typeof s&&"boolean"!=typeof s&&(e.name=s)}function kt(e,t,n){"number"===t&&mt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function wt(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+dt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function St(e,t,n){null==t||((t=""+dt(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+dt(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function xt(e,t,n,o){if(null==t){if(null!=o){if(null!=n)throw Error(r(92));if(z(o)){if(1<o.length)throw Error(r(93));o=o[0]}n=o}null==n&&(n=""),t=n}n=dt(t),e.defaultValue=n,(o=e.textContent)===n&&""!==o&&null!==o&&(e.value=o)}function Ct(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var Et=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Mt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||Et.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Tt(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(r(62));if(e=e.style,null!=n){for(var o in n)!n.hasOwnProperty(o)||null!=t&&t.hasOwnProperty(o)||(0===o.indexOf("--")?e.setProperty(o,""):"float"===o?e.cssFloat="":e[o]="");for(var i in t)o=t[i],t.hasOwnProperty(i)&&n[i]!==o&&Mt(e,i,o)}else for(var s in t)t.hasOwnProperty(s)&&Mt(e,s,t[s])}function Ot(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Nt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),At=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Dt(e){return At.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Pt=null;function Rt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Lt=null,zt=null;function It(e){var t=Ue(e);if(t&&(e=t.stateNode)){var n=e[ze]||null;e:switch(e=t.stateNode,t.type){case"input":if(vt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+yt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var o=n[t];if(o!==e&&o.form===e.form){var i=o[ze]||null;if(!i)throw Error(r(90));vt(o,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)(o=n[t]).form===e.form&&pt(o)}break e;case"textarea":St(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&wt(e,!!n.multiple,t,!1)}}}var _t=!1;function $t(e,t,n){if(_t)return e(t,n);_t=!0;try{return e(t)}finally{if(_t=!1,(null!==Lt||null!==zt)&&(Vc(),Lt&&(t=Lt,e=zt,zt=Lt=null,It(t),e)))for(t=0;t<e.length;t++)It(e[t])}}function Ft(e,t){var n=e.stateNode;if(null===n)return null;var o=n[ze]||null;if(null===o)return null;n=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(o=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!o;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(r(231,t,typeof n));return n}var Bt=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),jt=!1;if(Bt)try{var Ht={};Object.defineProperty(Ht,"passive",{get:function(){jt=!0}}),window.addEventListener("test",Ht,Ht),window.removeEventListener("test",Ht,Ht)}catch(If){jt=!1}var Vt=null,Ut=null,Wt=null;function qt(){if(Wt)return Wt;var e,t,n=Ut,r=n.length,o="value"in Vt?Vt.value:Vt.textContent,i=o.length;for(e=0;e<r&&n[e]===o[e];e++);var s=r-e;for(t=1;t<=s&&n[r-t]===o[i-t];t++);return Wt=o.slice(e,1<t?1-t:void 0)}function Kt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Jt(){return!0}function Qt(){return!1}function Gt(e){function t(t,n,r,o,i){for(var s in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(o):o[s]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?Jt:Qt,this.isPropagationStopped=Qt,this}return c(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Jt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Jt)},persist:function(){},isPersistent:Jt}),t}var Yt,Xt,Zt,en={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},tn=Gt(en),nn=c({},en,{view:0,detail:0}),rn=Gt(nn),on=c({},nn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Zt&&(Zt&&"mousemove"===e.type?(Yt=e.screenX-Zt.screenX,Xt=e.screenY-Zt.screenY):Xt=Yt=0,Zt=e),Yt)},movementY:function(e){return"movementY"in e?e.movementY:Xt}}),sn=Gt(on),ln=Gt(c({},on,{dataTransfer:0})),an=Gt(c({},nn,{relatedTarget:0})),cn=Gt(c({},en,{animationName:0,elapsedTime:0,pseudoElement:0})),un=Gt(c({},en,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),dn=Gt(c({},en,{data:0})),fn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},pn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function mn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=pn[e])&&!!t[e]}function gn(){return mn}var yn=Gt(c({},nn,{key:function(e){if(e.key){var t=fn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Kt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?hn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gn,charCode:function(e){return"keypress"===e.type?Kt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Kt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),vn=Gt(c({},on,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),bn=Gt(c({},nn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gn})),kn=Gt(c({},en,{propertyName:0,elapsedTime:0,pseudoElement:0})),wn=Gt(c({},on,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),Sn=Gt(c({},en,{newState:0,oldState:0})),xn=[9,13,27,32],Cn=Bt&&"CompositionEvent"in window,En=null;Bt&&"documentMode"in document&&(En=document.documentMode);var Mn=Bt&&"TextEvent"in window&&!En,Tn=Bt&&(!Cn||En&&8<En&&11>=En),On=String.fromCharCode(32),Nn=!1;function An(e,t){switch(e){case"keyup":return-1!==xn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Dn(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Pn=!1;var Rn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ln(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Rn[e.type]:"textarea"===t}function zn(e,t,n,r){Lt?zt?zt.push(r):zt=[r]:Lt=r,0<(t=qu(t,"onChange")).length&&(n=new tn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var In=null,_n=null;function $n(e){$u(e,0)}function Fn(e){if(pt(We(e)))return e}function Bn(e,t){if("change"===e)return t}var jn=!1;if(Bt){var Hn;if(Bt){var Vn="oninput"in document;if(!Vn){var Un=document.createElement("div");Un.setAttribute("oninput","return;"),Vn="function"==typeof Un.oninput}Hn=Vn}else Hn=!1;jn=Hn&&(!document.documentMode||9<document.documentMode)}function Wn(){In&&(In.detachEvent("onpropertychange",qn),_n=In=null)}function qn(e){if("value"===e.propertyName&&Fn(_n)){var t=[];zn(t,_n,e,Rt(e)),$t($n,t)}}function Kn(e,t,n){"focusin"===e?(Wn(),_n=n,(In=t).attachEvent("onpropertychange",qn)):"focusout"===e&&Wn()}function Jn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Fn(_n)}function Qn(e,t){if("click"===e)return Fn(t)}function Gn(e,t){if("input"===e||"change"===e)return Fn(t)}var Yn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function Xn(e,t){if(Yn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!X.call(t,o)||!Yn(e[o],t[o]))return!1}return!0}function Zn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function er(e,t){var n,r=Zn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Zn(r)}}function tr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?tr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function nr(e){for(var t=mt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=mt((e=t.contentWindow).document)}return t}function rr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var or=Bt&&"documentMode"in document&&11>=document.documentMode,ir=null,sr=null,lr=null,ar=!1;function cr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ar||null==ir||ir!==mt(r)||("selectionStart"in(r=ir)&&rr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},lr&&Xn(lr,r)||(lr=r,0<(r=qu(sr,"onSelect")).length&&(t=new tn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=ir)))}function ur(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var dr={animationend:ur("Animation","AnimationEnd"),animationiteration:ur("Animation","AnimationIteration"),animationstart:ur("Animation","AnimationStart"),transitionrun:ur("Transition","TransitionRun"),transitionstart:ur("Transition","TransitionStart"),transitioncancel:ur("Transition","TransitionCancel"),transitionend:ur("Transition","TransitionEnd")},fr={},hr={};function pr(e){if(fr[e])return fr[e];if(!dr[e])return e;var t,n=dr[e];for(t in n)if(n.hasOwnProperty(t)&&t in hr)return fr[e]=n[t];return e}Bt&&(hr=document.createElement("div").style,"AnimationEvent"in window||(delete dr.animationend.animation,delete dr.animationiteration.animation,delete dr.animationstart.animation),"TransitionEvent"in window||delete dr.transitionend.transition);var mr=pr("animationend"),gr=pr("animationiteration"),yr=pr("animationstart"),vr=pr("transitionrun"),br=pr("transitionstart"),kr=pr("transitioncancel"),wr=pr("transitionend"),Sr=new Map,xr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Cr(e,t){Sr.set(e,t),Ge(t,[e])}xr.push("scrollEnd");var Er=new WeakMap;function Mr(e,t){if("object"==typeof e&&null!==e){var n=Er.get(e);return void 0!==n?n:(t={value:e,source:t,stack:ut(t)},Er.set(e,t),t)}return{value:e,source:t,stack:ut(t)}}var Tr=[],Or=0,Nr=0;function Ar(){for(var e=Or,t=Nr=Or=0;t<e;){var n=Tr[t];Tr[t++]=null;var r=Tr[t];Tr[t++]=null;var o=Tr[t];Tr[t++]=null;var i=Tr[t];if(Tr[t++]=null,null!==r&&null!==o){var s=r.pending;null===s?o.next=o:(o.next=s.next,s.next=o),r.pending=o}0!==i&&Lr(n,o,i)}}function Dr(e,t,n,r){Tr[Or++]=e,Tr[Or++]=t,Tr[Or++]=n,Tr[Or++]=r,Nr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Pr(e,t,n,r){return Dr(e,t,n,r),zr(e)}function Rr(e,t){return Dr(e,null,null,t),zr(e)}function Lr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var o=!1,i=e.return;null!==i;)i.childLanes|=n,null!==(r=i.alternate)&&(r.childLanes|=n),22===i.tag&&(null===(e=i.stateNode)||1&e._visibility||(o=!0)),e=i,i=i.return;return 3===e.tag?(i=e.stateNode,o&&null!==t&&(o=31-me(n),null===(r=(e=i.hiddenUpdates)[o])?e[o]=[t]:r.push(t),t.lane=536870912|n),i):null}function zr(e){if(50<Lc)throw Lc=0,zc=null,Error(r(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Ir={};function _r(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $r(e,t,n,r){return new _r(e,t,n,r)}function Fr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Br(e,t){var n=e.alternate;return null===n?((n=$r(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function jr(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Hr(e,t,n,o,i,s){var l=0;if(o=e,"function"==typeof e)Fr(e)&&(l=1);else if("string"==typeof e)l=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,U.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case O:return(e=$r(31,n,t,i)).elementType=O,e.lanes=s,e;case p:return Vr(n.children,i,s,t);case m:l=8,i|=24;break;case g:return(e=$r(12,n,t,2|i)).elementType=g,e.lanes=s,e;case x:return(e=$r(13,n,t,i)).elementType=x,e.lanes=s,e;case C:return(e=$r(19,n,t,i)).elementType=C,e.lanes=s,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case y:case k:l=10;break e;case v:l=9;break e;case w:l=11;break e;case M:l=14;break e;case T:l=16,o=null;break e}l=29,n=Error(r(130,null===e?"null":typeof e,"")),o=null}return(t=$r(l,n,t,i)).elementType=e,t.type=o,t.lanes=s,t}function Vr(e,t,n,r){return(e=$r(7,e,r,t)).lanes=n,e}function Ur(e,t,n){return(e=$r(6,e,null,t)).lanes=n,e}function Wr(e,t,n){return(t=$r(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var qr=[],Kr=0,Jr=null,Qr=0,Gr=[],Yr=0,Xr=null,Zr=1,eo="";function to(e,t){qr[Kr++]=Qr,qr[Kr++]=Jr,Jr=e,Qr=t}function no(e,t,n){Gr[Yr++]=Zr,Gr[Yr++]=eo,Gr[Yr++]=Xr,Xr=e;var r=Zr;e=eo;var o=32-me(r)-1;r&=~(1<<o),n+=1;var i=32-me(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Zr=1<<32-me(t)+o|n<<o|r,eo=i+e}else Zr=1<<i|n<<o|r,eo=e}function ro(e){null!==e.return&&(to(e,1),no(e,1,0))}function oo(e){for(;e===Jr;)Jr=qr[--Kr],qr[Kr]=null,Qr=qr[--Kr],qr[Kr]=null;for(;e===Xr;)Xr=Gr[--Yr],Gr[Yr]=null,eo=Gr[--Yr],Gr[Yr]=null,Zr=Gr[--Yr],Gr[Yr]=null}var io=null,so=null,lo=!1,ao=null,co=!1,uo=Error(r(519));function fo(e){throw vo(Mr(Error(r(418,"")),e)),uo}function ho(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Le]=e,t[ze]=r,n){case"dialog":Fu("cancel",t),Fu("close",t);break;case"iframe":case"object":case"embed":Fu("load",t);break;case"video":case"audio":for(n=0;n<Iu.length;n++)Fu(Iu[n],t);break;case"source":Fu("error",t);break;case"img":case"image":case"link":Fu("error",t),Fu("load",t);break;case"details":Fu("toggle",t);break;case"input":Fu("invalid",t),bt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),ht(t);break;case"select":Fu("invalid",t);break;case"textarea":Fu("invalid",t),xt(t,r.value,r.defaultValue,r.children),ht(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Xu(t.textContent,n)?(null!=r.popover&&(Fu("beforetoggle",t),Fu("toggle",t)),null!=r.onScroll&&Fu("scroll",t),null!=r.onScrollEnd&&Fu("scrollend",t),null!=r.onClick&&(t.onclick=Zu),t=!0):t=!1,t||fo(e)}function po(e){for(io=e.return;io;)switch(io.tag){case 5:case 13:return void(co=!1);case 27:case 3:return void(co=!0);default:io=io.return}}function mo(e){if(e!==io)return!1;if(!lo)return po(e),lo=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||ad(e.type,e.memoizedProps)),t=!t),t&&so&&fo(e),po(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(r(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){so=bd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}so=null}}else 27===n?(n=so,md(e.type)?(e=kd,kd=null,so=e):so=n):so=io?bd(e.stateNode.nextSibling):null;return!0}function go(){so=io=null,lo=!1}function yo(){var e=ao;return null!==e&&(null===wc?wc=e:wc.push.apply(wc,e),ao=null),e}function vo(e){null===ao?ao=[e]:ao.push(e)}var bo=j(null),ko=null,wo=null;function So(e,t,n){V(bo,t._currentValue),t._currentValue=n}function xo(e){e._currentValue=bo.current,H(bo)}function Co(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Eo(e,t,n,o){var i=e.child;for(null!==i&&(i.return=e);null!==i;){var s=i.dependencies;if(null!==s){var l=i.child;s=s.firstContext;e:for(;null!==s;){var a=s;s=i;for(var c=0;c<t.length;c++)if(a.context===t[c]){s.lanes|=n,null!==(a=s.alternate)&&(a.lanes|=n),Co(s.return,n,e),o||(l=null);break e}s=a.next}}else if(18===i.tag){if(null===(l=i.return))throw Error(r(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Co(l,n,e),l=null}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===e){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}}function Mo(e,t,n,o){e=null;for(var i=t,s=!1;null!==i;){if(!s)if(524288&i.flags)s=!0;else if(262144&i.flags)break;if(10===i.tag){var l=i.alternate;if(null===l)throw Error(r(387));if(null!==(l=l.memoizedProps)){var a=i.type;Yn(i.pendingProps.value,l.value)||(null!==e?e.push(a):e=[a])}}else if(i===K.current){if(null===(l=i.alternate))throw Error(r(387));l.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(null!==e?e.push(Gd):e=[Gd])}i=i.return}null!==e&&Eo(t,e,n,o),t.flags|=262144}function To(e){for(e=e.firstContext;null!==e;){if(!Yn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Oo(e){ko=e,wo=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function No(e){return Do(ko,e)}function Ao(e,t){return null===ko&&Oo(e),Do(e,t)}function Do(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===wo){if(null===e)throw Error(r(308));wo=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else wo=wo.next=t;return n}var Po="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Ro=e.unstable_scheduleCallback,Lo=e.unstable_NormalPriority,zo={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Io(){return{controller:new Po,data:new Map,refCount:0}}function _o(e){e.refCount--,0===e.refCount&&Ro(Lo,(function(){e.controller.abort()}))}var $o=null,Fo=0,Bo=0,jo=null;function Ho(){if(0===--Fo&&null!==$o){null!==jo&&(jo.status="fulfilled");var e=$o;$o=null,Bo=0,jo=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Vo=I.S;I.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===$o){var n=$o=[];Fo=0,Bo=Du(),jo={status:"pending",value:void 0,then:function(e){n.push(e)}}}Fo++,t.then(Ho,Ho)}(0,t),null!==Vo&&Vo(e,t)};var Uo=j(null);function Wo(){var e=Uo.current;return null!==e?e:ic.pooledCache}function qo(e,t){V(Uo,null===t?Uo.current:t.pool)}function Ko(){var e=Wo();return null===e?null:{parent:zo._currentValue,pool:e}}var Jo=Error(r(460)),Qo=Error(r(474)),Go=Error(r(542)),Yo={then:function(){}};function Xo(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Zo(){}function ei(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Zo,Zo),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw ri(e=t.reason),e;default:if("string"==typeof t.status)t.then(Zo,Zo);else{if(null!==(e=ic)&&100<e.shellSuspendCounter)throw Error(r(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw ri(e=t.reason),e}throw ti=t,Jo}}var ti=null;function ni(){if(null===ti)throw Error(r(459));var e=ti;return ti=null,e}function ri(e){if(e===Jo||e===Go)throw Error(r(483))}var oi=!1;function ii(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function si(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function li(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ai(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&oc){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,t=zr(e),Lr(e,null,n),t}return Dr(e,r,t,n),zr(e)}function ci(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ne(e,n)}}function ui(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var s={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===i?o=i=s:i=i.next=s,n=n.next}while(null!==n);null===i?o=i=t:i=i.next=t}else o=i=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var di=!1;function fi(){if(di){if(null!==jo)throw jo}}function hi(e,t,n,r){di=!1;var o=e.updateQueue;oi=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var a=l,u=a.next;a.next=null,null===s?i=u:s.next=u,s=a;var d=e.alternate;null!==d&&((l=(d=d.updateQueue).lastBaseUpdate)!==s&&(null===l?d.firstBaseUpdate=u:l.next=u,d.lastBaseUpdate=a))}if(null!==i){var f=o.baseState;for(s=0,d=u=a=null,l=i;;){var h=-536870913&l.lane,p=h!==l.lane;if(p?(lc&h)===h:(r&h)===h){0!==h&&h===Bo&&(di=!0),null!==d&&(d=d.next={lane:0,tag:l.tag,payload:l.payload,callback:null,next:null});e:{var m=e,g=l;h=t;var y=n;switch(g.tag){case 1:if("function"==typeof(m=g.payload)){f=m.call(y,f,h);break e}f=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(h="function"==typeof(m=g.payload)?m.call(y,f,h):m))break e;f=c({},f,h);break e;case 2:oi=!0}}null!==(h=l.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=o.callbacks)?o.callbacks=[h]:p.push(h))}else p={lane:h,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===d?(u=d=p,a=f):d=d.next=p,s|=h;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(p=l).next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}null===d&&(a=f),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=d,null===i&&(o.shared.lanes=0),mc|=s,e.lanes=s,e.memoizedState=f}}function pi(e,t){if("function"!=typeof e)throw Error(r(191,e));e.call(t)}function mi(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)pi(n[e],t)}var gi=j(null),yi=j(0);function vi(e,t){V(yi,e=hc),V(gi,t),hc=e|t.baseLanes}function bi(){V(yi,hc),V(gi,gi.current)}function ki(){hc=yi.current,H(gi),H(yi)}var wi=0,Si=null,xi=null,Ci=null,Ei=!1,Mi=!1,Ti=!1,Oi=0,Ni=0,Ai=null,Di=0;function Pi(){throw Error(r(321))}function Ri(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Yn(e[n],t[n]))return!1;return!0}function Li(e,t,n,r,o,i){return wi=i,Si=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,I.H=null===e||null===e.memoizedState?Js:Qs,Ti=!1,i=n(r,o),Ti=!1,Mi&&(i=Ii(t,n,r,o)),zi(e),i}function zi(e){I.H=Ks;var t=null!==xi&&null!==xi.next;if(wi=0,Ci=xi=Si=null,Ei=!1,Ni=0,Ai=null,t)throw Error(r(300));null===e||Nl||null!==(e=e.dependencies)&&To(e)&&(Nl=!0)}function Ii(e,t,n,o){Si=e;var i=0;do{if(Mi&&(Ai=null),Ni=0,Mi=!1,25<=i)throw Error(r(301));if(i+=1,Ci=xi=null,null!=e.updateQueue){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,null!=s.memoCache&&(s.memoCache.index=0)}I.H=Gs,s=t(n,o)}while(Mi);return s}function _i(){var e=I.H,t=e.useState()[0];return t="function"==typeof t.then?Vi(t):t,e=e.useState()[0],(null!==xi?xi.memoizedState:null)!==e&&(Si.flags|=1024),t}function $i(){var e=0!==Oi;return Oi=0,e}function Fi(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Bi(e){if(Ei){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Ei=!1}wi=0,Ci=xi=Si=null,Mi=!1,Ni=Oi=0,Ai=null}function ji(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Ci?Si.memoizedState=Ci=e:Ci=Ci.next=e,Ci}function Hi(){if(null===xi){var e=Si.alternate;e=null!==e?e.memoizedState:null}else e=xi.next;var t=null===Ci?Si.memoizedState:Ci.next;if(null!==t)Ci=t,xi=e;else{if(null===e){if(null===Si.alternate)throw Error(r(467));throw Error(r(310))}e={memoizedState:(xi=e).memoizedState,baseState:xi.baseState,baseQueue:xi.baseQueue,queue:xi.queue,next:null},null===Ci?Si.memoizedState=Ci=e:Ci=Ci.next=e}return Ci}function Vi(e){var t=Ni;return Ni+=1,null===Ai&&(Ai=[]),e=ei(Ai,e,t),t=Si,null===(null===Ci?t.memoizedState:Ci.next)&&(t=t.alternate,I.H=null===t||null===t.memoizedState?Js:Qs),e}function Ui(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Vi(e);if(e.$$typeof===k)return No(e)}throw Error(r(438,String(e)))}function Wi(e){var t=null,n=Si.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=Si.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},Si.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=N;return t.index++,n}function qi(e,t){return"function"==typeof t?t(e):t}function Ki(e){return Ji(Hi(),xi,e)}function Ji(e,t,n){var o=e.queue;if(null===o)throw Error(r(311));o.lastRenderedReducer=n;var i=e.baseQueue,s=o.pending;if(null!==s){if(null!==i){var l=i.next;i.next=s.next,s.next=l}t.baseQueue=i=s,o.pending=null}if(s=e.baseState,null===i)e.memoizedState=s;else{var a=l=null,c=null,u=t=i.next,d=!1;do{var f=-536870913&u.lane;if(f!==u.lane?(lc&f)===f:(wi&f)===f){var h=u.revertLane;if(0===h)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===Bo&&(d=!0);else{if((wi&h)===h){u=u.next,h===Bo&&(d=!0);continue}f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(a=c=f,l=s):c=c.next=f,Si.lanes|=h,mc|=h}f=u.action,Ti&&n(s,f),s=u.hasEagerState?u.eagerState:n(s,f)}else h={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(a=c=h,l=s):c=c.next=h,Si.lanes|=f,mc|=f;u=u.next}while(null!==u&&u!==t);if(null===c?l=s:c.next=a,!Yn(s,e.memoizedState)&&(Nl=!0,d&&null!==(n=jo)))throw n;e.memoizedState=s,e.baseState=l,e.baseQueue=c,o.lastRenderedState=s}return null===i&&(o.lanes=0),[e.memoizedState,o.dispatch]}function Qi(e){var t=Hi(),n=t.queue;if(null===n)throw Error(r(311));n.lastRenderedReducer=e;var o=n.dispatch,i=n.pending,s=t.memoizedState;if(null!==i){n.pending=null;var l=i=i.next;do{s=e(s,l.action),l=l.next}while(l!==i);Yn(s,t.memoizedState)||(Nl=!0),t.memoizedState=s,null===t.baseQueue&&(t.baseState=s),n.lastRenderedState=s}return[s,o]}function Gi(e,t,n){var o=Si,i=Hi(),s=lo;if(s){if(void 0===n)throw Error(r(407));n=n()}else n=t();var l=!Yn((xi||i).memoizedState,n);if(l&&(i.memoizedState=n,Nl=!0),i=i.queue,bs(2048,8,Zi.bind(null,o,i,e),[e]),i.getSnapshot!==t||l||null!==Ci&&1&Ci.memoizedState.tag){if(o.flags|=2048,gs(9,{destroy:void 0,resource:void 0},Xi.bind(null,o,i,n,t),null),null===ic)throw Error(r(349));s||124&wi||Yi(o,t,n)}return n}function Yi(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=Si.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},Si.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Xi(e,t,n,r){t.value=n,t.getSnapshot=r,es(t)&&ts(e)}function Zi(e,t,n){return n((function(){es(t)&&ts(e)}))}function es(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Yn(e,n)}catch(r){return!0}}function ts(e){var t=Rr(e,2);null!==t&&$c(t,e,2)}function ns(e){var t=ji();if("function"==typeof e){var n=e;if(e=n(),Ti){pe(!0);try{n()}finally{pe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qi,lastRenderedState:e},t}function rs(e,t,n,r){return e.baseState=n,Ji(e,xi,"function"==typeof r?r:qi)}function os(e,t,n,o,i){if(Us(e))throw Error(r(485));if(null!==(e=t.action)){var s={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){s.listeners.push(e)}};null!==I.T?n(!0):s.isTransition=!1,o(s),null===(n=t.pending)?(s.next=t.pending=s,is(t,s)):(s.next=n.next,t.pending=n.next=s)}}function is(e,t){var n=t.action,r=t.payload,o=e.state;if(t.isTransition){var i=I.T,s={};I.T=s;try{var l=n(o,r),a=I.S;null!==a&&a(s,l),ss(e,t,l)}catch(c){as(e,t,c)}finally{I.T=i}}else try{ss(e,t,i=n(o,r))}catch(u){as(e,t,u)}}function ss(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then((function(n){ls(e,t,n)}),(function(n){return as(e,t,n)})):ls(e,t,n)}function ls(e,t,n){t.status="fulfilled",t.value=n,cs(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,is(e,n)))}function as(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,cs(t),t=t.next}while(t!==r)}e.action=null}function cs(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function us(e,t){return t}function ds(e,t){if(lo){var n=ic.formState;if(null!==n){e:{var r=Si;if(lo){if(so){t:{for(var o=so,i=co;8!==o.nodeType;){if(!i){o=null;break t}if(null===(o=bd(o.nextSibling))){o=null;break t}}o="F!"===(i=o.data)||"F"===i?o:null}if(o){so=bd(o.nextSibling),r="F!"===o.data;break e}}fo(r)}r=!1}r&&(t=n[0])}}return(n=ji()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:us,lastRenderedState:t},n.queue=r,n=js.bind(null,Si,r),r.dispatch=n,r=ns(!1),i=Vs.bind(null,Si,!1,r.queue),o={state:t,dispatch:null,action:e,pending:null},(r=ji()).queue=o,n=os.bind(null,Si,o,i,n),o.dispatch=n,r.memoizedState=e,[t,n,!1]}function fs(e){return hs(Hi(),xi,e)}function hs(e,t,n){if(t=Ji(e,t,us)[0],e=Ki(qi)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=Vi(t)}catch(s){if(s===Jo)throw Go;throw s}else r=t;var o=(t=Hi()).queue,i=o.dispatch;return n!==t.memoizedState&&(Si.flags|=2048,gs(9,{destroy:void 0,resource:void 0},ps.bind(null,o,n),null)),[r,i,e]}function ps(e,t){e.action=t}function ms(e){var t=Hi(),n=xi;if(null!==n)return hs(t,n,e);Hi(),t=t.memoizedState;var r=(n=Hi()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function gs(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=Si.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},Si.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ys(){return Hi().memoizedState}function vs(e,t,n,r){var o=ji();r=void 0===r?null:r,Si.flags|=e,o.memoizedState=gs(1|t,{destroy:void 0,resource:void 0},n,r)}function bs(e,t,n,r){var o=Hi();r=void 0===r?null:r;var i=o.memoizedState.inst;null!==xi&&null!==r&&Ri(r,xi.memoizedState.deps)?o.memoizedState=gs(t,i,n,r):(Si.flags|=e,o.memoizedState=gs(1|t,i,n,r))}function ks(e,t){vs(8390656,8,e,t)}function ws(e,t){bs(2048,8,e,t)}function Ss(e,t){return bs(4,2,e,t)}function xs(e,t){return bs(4,4,e,t)}function Cs(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function Es(e,t,n){n=null!=n?n.concat([e]):null,bs(4,4,Cs.bind(null,t,e),n)}function Ms(){}function Ts(e,t){var n=Hi();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Ri(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Os(e,t){var n=Hi();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Ri(t,r[1]))return r[0];if(r=e(),Ti){pe(!0);try{e()}finally{pe(!1)}}return n.memoizedState=[r,t],r}function Ns(e,t,n){return void 0===n||1073741824&wi?e.memoizedState=t:(e.memoizedState=n,e=_c(),Si.lanes|=e,mc|=e,n)}function As(e,t,n,r){return Yn(n,t)?n:null!==gi.current?(e=Ns(e,n,r),Yn(e,t)||(Nl=!0),e):42&wi?(e=_c(),Si.lanes|=e,mc|=e,t):(Nl=!0,e.memoizedState=n)}function Ds(e,t,n,r,o){var i=_.p;_.p=0!==i&&8>i?i:8;var s,l,a,c=I.T,u={};I.T=u,Vs(e,!1,t,n);try{var d=o(),f=I.S;if(null!==f&&f(u,d),null!==d&&"object"==typeof d&&"function"==typeof d.then){var h=(s=r,l=[],a={status:"pending",value:null,reason:null,then:function(e){l.push(e)}},d.then((function(){a.status="fulfilled",a.value=s;for(var e=0;e<l.length;e++)(0,l[e])(s)}),(function(e){for(a.status="rejected",a.reason=e,e=0;e<l.length;e++)(0,l[e])(void 0)})),a);Hs(e,t,h,Ic())}else Hs(e,t,r,Ic())}catch(p){Hs(e,t,{then:function(){},status:"rejected",reason:p},Ic())}finally{_.p=i,I.T=c}}function Ps(){}function Rs(e,t,n,o){if(5!==e.tag)throw Error(r(476));var i=Ls(e).queue;Ds(e,i,t,$,null===n?Ps:function(){return zs(e),n(o)})}function Ls(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:$,baseState:$,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:qi,lastRenderedState:$},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:qi,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function zs(e){Hs(e,Ls(e).next.queue,{},Ic())}function Is(){return No(Gd)}function _s(){return Hi().memoizedState}function $s(){return Hi().memoizedState}function Fs(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Ic(),r=ai(t,e=li(n),n);return null!==r&&($c(r,t,n),ci(r,t,n)),t={cache:Io()},void(e.payload=t)}t=t.return}}function Bs(e,t,n){var r=Ic();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Us(e)?Ws(t,n):null!==(n=Pr(e,t,n,r))&&($c(n,e,r),qs(n,t,r))}function js(e,t,n){Hs(e,t,n,Ic())}function Hs(e,t,n,r){var o={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Us(e))Ws(t,o);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,Yn(l,s))return Dr(e,t,o,0),null===ic&&Ar(),!1}catch(a){}if(null!==(n=Pr(e,t,o,r)))return $c(n,e,r),qs(n,t,r),!0}return!1}function Vs(e,t,n,o){if(o={lane:2,revertLane:Du(),action:o,hasEagerState:!1,eagerState:null,next:null},Us(e)){if(t)throw Error(r(479))}else null!==(t=Pr(e,n,o,2))&&$c(t,e,2)}function Us(e){var t=e.alternate;return e===Si||null!==t&&t===Si}function Ws(e,t){Mi=Ei=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function qs(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ne(e,n)}}var Ks={readContext:No,use:Ui,useCallback:Pi,useContext:Pi,useEffect:Pi,useImperativeHandle:Pi,useLayoutEffect:Pi,useInsertionEffect:Pi,useMemo:Pi,useReducer:Pi,useRef:Pi,useState:Pi,useDebugValue:Pi,useDeferredValue:Pi,useTransition:Pi,useSyncExternalStore:Pi,useId:Pi,useHostTransitionStatus:Pi,useFormState:Pi,useActionState:Pi,useOptimistic:Pi,useMemoCache:Pi,useCacheRefresh:Pi},Js={readContext:No,use:Ui,useCallback:function(e,t){return ji().memoizedState=[e,void 0===t?null:t],e},useContext:No,useEffect:ks,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,vs(4194308,4,Cs.bind(null,t,e),n)},useLayoutEffect:function(e,t){return vs(4194308,4,e,t)},useInsertionEffect:function(e,t){vs(4,2,e,t)},useMemo:function(e,t){var n=ji();t=void 0===t?null:t;var r=e();if(Ti){pe(!0);try{e()}finally{pe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=ji();if(void 0!==n){var o=n(t);if(Ti){pe(!0);try{n(t)}finally{pe(!1)}}}else o=t;return r.memoizedState=r.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},r.queue=e,e=e.dispatch=Bs.bind(null,Si,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},ji().memoizedState=e},useState:function(e){var t=(e=ns(e)).queue,n=js.bind(null,Si,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Ms,useDeferredValue:function(e,t){return Ns(ji(),e,t)},useTransition:function(){var e=ns(!1);return e=Ds.bind(null,Si,e.queue,!0,!1),ji().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var o=Si,i=ji();if(lo){if(void 0===n)throw Error(r(407));n=n()}else{if(n=t(),null===ic)throw Error(r(349));124&lc||Yi(o,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,ks(Zi.bind(null,o,s,e),[e]),o.flags|=2048,gs(9,{destroy:void 0,resource:void 0},Xi.bind(null,o,s,n,t),null),n},useId:function(){var e=ji(),t=ic.identifierPrefix;if(lo){var n=eo;t="«"+t+"R"+(n=(Zr&~(1<<32-me(Zr)-1)).toString(32)+n),0<(n=Oi++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=Di++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Is,useFormState:ds,useActionState:ds,useOptimistic:function(e){var t=ji();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Vs.bind(null,Si,!0,n),n.dispatch=t,[e,t]},useMemoCache:Wi,useCacheRefresh:function(){return ji().memoizedState=Fs.bind(null,Si)}},Qs={readContext:No,use:Ui,useCallback:Ts,useContext:No,useEffect:ws,useImperativeHandle:Es,useInsertionEffect:Ss,useLayoutEffect:xs,useMemo:Os,useReducer:Ki,useRef:ys,useState:function(){return Ki(qi)},useDebugValue:Ms,useDeferredValue:function(e,t){return As(Hi(),xi.memoizedState,e,t)},useTransition:function(){var e=Ki(qi)[0],t=Hi().memoizedState;return["boolean"==typeof e?e:Vi(e),t]},useSyncExternalStore:Gi,useId:_s,useHostTransitionStatus:Is,useFormState:fs,useActionState:fs,useOptimistic:function(e,t){return rs(Hi(),0,e,t)},useMemoCache:Wi,useCacheRefresh:$s},Gs={readContext:No,use:Ui,useCallback:Ts,useContext:No,useEffect:ws,useImperativeHandle:Es,useInsertionEffect:Ss,useLayoutEffect:xs,useMemo:Os,useReducer:Qi,useRef:ys,useState:function(){return Qi(qi)},useDebugValue:Ms,useDeferredValue:function(e,t){var n=Hi();return null===xi?Ns(n,e,t):As(n,xi.memoizedState,e,t)},useTransition:function(){var e=Qi(qi)[0],t=Hi().memoizedState;return["boolean"==typeof e?e:Vi(e),t]},useSyncExternalStore:Gi,useId:_s,useHostTransitionStatus:Is,useFormState:ms,useActionState:ms,useOptimistic:function(e,t){var n=Hi();return null!==xi?rs(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Wi,useCacheRefresh:$s},Ys=null,Xs=0;function Zs(e){var t=Xs;return Xs+=1,null===Ys&&(Ys=[]),ei(Ys,e,t)}function el(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function tl(e,t){if(t.$$typeof===u)throw Error(r(525));throw e=Object.prototype.toString.call(t),Error(r(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function nl(e){return(0,e._init)(e._payload)}function rl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function o(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function i(e,t){return(e=Br(e,t)).index=0,e.sibling=null,e}function s(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function a(e,t,n,r){return null===t||6!==t.tag?((t=Ur(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function c(e,t,n,r){var o=n.type;return o===p?h(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"==typeof o&&null!==o&&o.$$typeof===T&&nl(o)===t.type)?(el(t=i(t,n.props),n),t.return=e,t):(el(t=Hr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Wr(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function h(e,t,n,r,o){return null===t||7!==t.tag?((t=Vr(n,e.mode,r,o)).return=e,t):((t=i(t,n)).return=e,t)}function m(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=Ur(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case d:return el(n=Hr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case f:return(t=Wr(t,e.mode,n)).return=e,t;case T:return m(e,t=(0,t._init)(t._payload),n)}if(z(t)||P(t))return(t=Vr(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return m(e,Zs(t),n);if(t.$$typeof===k)return m(e,Ao(e,t),n);tl(e,t)}return null}function g(e,t,n,r){var o=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==o?null:a(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case d:return n.key===o?c(e,t,n,r):null;case f:return n.key===o?u(e,t,n,r):null;case T:return g(e,t,n=(o=n._init)(n._payload),r)}if(z(n)||P(n))return null!==o?null:h(e,t,n,r,null);if("function"==typeof n.then)return g(e,t,Zs(n),r);if(n.$$typeof===k)return g(e,t,Ao(e,n),r);tl(e,n)}return null}function y(e,t,n,r,o){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return a(t,e=e.get(n)||null,""+r,o);if("object"==typeof r&&null!==r){switch(r.$$typeof){case d:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case f:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case T:return y(e,t,n,r=(0,r._init)(r._payload),o)}if(z(r)||P(r))return h(t,e=e.get(n)||null,r,o,null);if("function"==typeof r.then)return y(e,t,n,Zs(r),o);if(r.$$typeof===k)return y(e,t,n,Ao(t,r),o);tl(t,r)}return null}function v(a,c,u,h){if("object"==typeof u&&null!==u&&u.type===p&&null===u.key&&(u=u.props.children),"object"==typeof u&&null!==u){switch(u.$$typeof){case d:e:{for(var b=u.key;null!==c;){if(c.key===b){if((b=u.type)===p){if(7===c.tag){n(a,c.sibling),(h=i(c,u.props.children)).return=a,a=h;break e}}else if(c.elementType===b||"object"==typeof b&&null!==b&&b.$$typeof===T&&nl(b)===c.type){n(a,c.sibling),el(h=i(c,u.props),u),h.return=a,a=h;break e}n(a,c);break}t(a,c),c=c.sibling}u.type===p?((h=Vr(u.props.children,a.mode,h,u.key)).return=a,a=h):(el(h=Hr(u.type,u.key,u.props,null,a.mode,h),u),h.return=a,a=h)}return l(a);case f:e:{for(b=u.key;null!==c;){if(c.key===b){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(a,c.sibling),(h=i(c,u.children||[])).return=a,a=h;break e}n(a,c);break}t(a,c),c=c.sibling}(h=Wr(u,a.mode,h)).return=a,a=h}return l(a);case T:return v(a,c,u=(b=u._init)(u._payload),h)}if(z(u))return function(r,i,l,a){for(var c=null,u=null,d=i,f=i=0,h=null;null!==d&&f<l.length;f++){d.index>f?(h=d,d=null):h=d.sibling;var p=g(r,d,l[f],a);if(null===p){null===d&&(d=h);break}e&&d&&null===p.alternate&&t(r,d),i=s(p,i,f),null===u?c=p:u.sibling=p,u=p,d=h}if(f===l.length)return n(r,d),lo&&to(r,f),c;if(null===d){for(;f<l.length;f++)null!==(d=m(r,l[f],a))&&(i=s(d,i,f),null===u?c=d:u.sibling=d,u=d);return lo&&to(r,f),c}for(d=o(d);f<l.length;f++)null!==(h=y(d,r,f,l[f],a))&&(e&&null!==h.alternate&&d.delete(null===h.key?f:h.key),i=s(h,i,f),null===u?c=h:u.sibling=h,u=h);return e&&d.forEach((function(e){return t(r,e)})),lo&&to(r,f),c}(a,c,u,h);if(P(u)){if("function"!=typeof(b=P(u)))throw Error(r(150));return function(i,l,a,c){if(null==a)throw Error(r(151));for(var u=null,d=null,f=l,h=l=0,p=null,v=a.next();null!==f&&!v.done;h++,v=a.next()){f.index>h?(p=f,f=null):p=f.sibling;var b=g(i,f,v.value,c);if(null===b){null===f&&(f=p);break}e&&f&&null===b.alternate&&t(i,f),l=s(b,l,h),null===d?u=b:d.sibling=b,d=b,f=p}if(v.done)return n(i,f),lo&&to(i,h),u;if(null===f){for(;!v.done;h++,v=a.next())null!==(v=m(i,v.value,c))&&(l=s(v,l,h),null===d?u=v:d.sibling=v,d=v);return lo&&to(i,h),u}for(f=o(f);!v.done;h++,v=a.next())null!==(v=y(f,i,h,v.value,c))&&(e&&null!==v.alternate&&f.delete(null===v.key?h:v.key),l=s(v,l,h),null===d?u=v:d.sibling=v,d=v);return e&&f.forEach((function(e){return t(i,e)})),lo&&to(i,h),u}(a,c,u=b.call(u),h)}if("function"==typeof u.then)return v(a,c,Zs(u),h);if(u.$$typeof===k)return v(a,c,Ao(a,u),h);tl(a,u)}return"string"==typeof u&&""!==u||"number"==typeof u||"bigint"==typeof u?(u=""+u,null!==c&&6===c.tag?(n(a,c.sibling),(h=i(c,u)).return=a,a=h):(n(a,c),(h=Ur(u,a.mode,h)).return=a,a=h),l(a)):n(a,c)}return function(e,t,n,r){try{Xs=0;var o=v(e,t,n,r);return Ys=null,o}catch(s){if(s===Jo||s===Go)throw s;var i=$r(29,s,null,e.mode);return i.lanes=r,i.return=e,i}}}var ol=rl(!0),il=rl(!1),sl=j(null),ll=null;function al(e){var t=e.alternate;V(fl,1&fl.current),V(sl,e),null===ll&&(null===t||null!==gi.current||null!==t.memoizedState)&&(ll=e)}function cl(e){if(22===e.tag){if(V(fl,fl.current),V(sl,e),null===ll){var t=e.alternate;null!==t&&null!==t.memoizedState&&(ll=e)}}else ul()}function ul(){V(fl,fl.current),V(sl,sl.current)}function dl(e){H(sl),ll===e&&(ll=null),H(fl)}var fl=j(0);function hl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||vd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function pl(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:c({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ml={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ic(),o=li(r);o.payload=t,null!=n&&(o.callback=n),null!==(t=ai(e,o,r))&&($c(t,e,r),ci(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ic(),o=li(r);o.tag=1,o.payload=t,null!=n&&(o.callback=n),null!==(t=ai(e,o,r))&&($c(t,e,r),ci(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ic(),r=li(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=ai(e,r,n))&&($c(t,e,n),ci(t,e,n))}};function gl(e,t,n,r,o,i,s){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,s):!t.prototype||!t.prototype.isPureReactComponent||(!Xn(n,r)||!Xn(o,i))}function yl(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ml.enqueueReplaceState(t,t.state,null)}function vl(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var o in n===t&&(n=c({},n)),e)void 0===n[o]&&(n[o]=e[o]);return n}var bl="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function kl(e){bl(e)}function wl(e){console.error(e)}function Sl(e){bl(e)}function xl(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function Cl(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function El(e,t,n){return(n=li(n)).tag=3,n.payload={element:null},n.callback=function(){xl(e,t)},n}function Ml(e){return(e=li(e)).tag=3,e}function Tl(e,t,n,r){var o=n.type.getDerivedStateFromError;if("function"==typeof o){var i=r.value;e.payload=function(){return o(i)},e.callback=function(){Cl(t,n,r)}}var s=n.stateNode;null!==s&&"function"==typeof s.componentDidCatch&&(e.callback=function(){Cl(t,n,r),"function"!=typeof o&&(null===Mc?Mc=new Set([this]):Mc.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ol=Error(r(461)),Nl=!1;function Al(e,t,n,r){t.child=null===e?il(t,null,n,r):ol(t,e.child,n,r)}function Dl(e,t,n,r,o){n=n.render;var i=t.ref;if("ref"in r){var s={};for(var l in r)"ref"!==l&&(s[l]=r[l])}else s=r;return Oo(t),r=Li(e,t,n,s,i,o),l=$i(),null===e||Nl?(lo&&l&&ro(t),t.flags|=1,Al(e,t,r,o),t.child):(Fi(e,t,o),Yl(e,t,o))}function Pl(e,t,n,r,o){if(null===e){var i=n.type;return"function"!=typeof i||Fr(i)||void 0!==i.defaultProps||null!==n.compare?((e=Hr(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Rl(e,t,i,r,o))}if(i=e.child,!Xl(e,o)){var s=i.memoizedProps;if((n=null!==(n=n.compare)?n:Xn)(s,r)&&e.ref===t.ref)return Yl(e,t,o)}return t.flags|=1,(e=Br(i,r)).ref=t.ref,e.return=t,t.child=e}function Rl(e,t,n,r,o){if(null!==e){var i=e.memoizedProps;if(Xn(i,r)&&e.ref===t.ref){if(Nl=!1,t.pendingProps=r=i,!Xl(e,o))return t.lanes=e.lanes,Yl(e,t,o);131072&e.flags&&(Nl=!0)}}return _l(e,t,n,r,o)}function Ll(e,t,n){var r=t.pendingProps,o=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==i?i.baseLanes|n:n,null!==e){for(o=t.child=e.child,i=0;null!==o;)i=i|o.lanes|o.childLanes,o=o.sibling;t.childLanes=i&~r}else t.childLanes=0,t.child=null;return zl(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,zl(e,t,null!==i?i.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&qo(0,null!==i?i.cachePool:null),null!==i?vi(t,i):bi(),cl(t)}else null!==i?(qo(0,i.cachePool),vi(t,i),ul(),t.memoizedState=null):(null!==e&&qo(0,null),bi(),ul());return Al(e,t,o,n),t.child}function zl(e,t,n,r){var o=Wo();return o=null===o?null:{parent:zo._currentValue,pool:o},t.memoizedState={baseLanes:n,cachePool:o},null!==e&&qo(0,null),bi(),cl(t),null!==e&&Mo(e,t,r,!0),null}function Il(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(r(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function _l(e,t,n,r,o){return Oo(t),n=Li(e,t,n,r,void 0,o),r=$i(),null===e||Nl?(lo&&r&&ro(t),t.flags|=1,Al(e,t,n,o),t.child):(Fi(e,t,o),Yl(e,t,o))}function $l(e,t,n,r,o,i){return Oo(t),t.updateQueue=null,n=Ii(t,r,n,o),zi(e),r=$i(),null===e||Nl?(lo&&r&&ro(t),t.flags|=1,Al(e,t,n,i),t.child):(Fi(e,t,i),Yl(e,t,i))}function Fl(e,t,n,r,o){if(Oo(t),null===t.stateNode){var i=Ir,s=n.contextType;"object"==typeof s&&null!==s&&(i=No(s)),i=new n(r,i),t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,i.updater=ml,t.stateNode=i,i._reactInternals=t,(i=t.stateNode).props=r,i.state=t.memoizedState,i.refs={},ii(t),s=n.contextType,i.context="object"==typeof s&&null!==s?No(s):Ir,i.state=t.memoizedState,"function"==typeof(s=n.getDerivedStateFromProps)&&(pl(t,n,s,r),i.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof i.getSnapshotBeforeUpdate||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||(s=i.state,"function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),s!==i.state&&ml.enqueueReplaceState(i,i.state,null),hi(t,r,i,o),fi(),i.state=t.memoizedState),"function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){i=t.stateNode;var l=t.memoizedProps,a=vl(n,l);i.props=a;var c=i.context,u=n.contextType;s=Ir,"object"==typeof u&&null!==u&&(s=No(u));var d=n.getDerivedStateFromProps;u="function"==typeof d||"function"==typeof i.getSnapshotBeforeUpdate,l=t.pendingProps!==l,u||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l||c!==s)&&yl(t,i,r,s),oi=!1;var f=t.memoizedState;i.state=f,hi(t,r,i,o),fi(),c=t.memoizedState,l||f!==c||oi?("function"==typeof d&&(pl(t,n,d,r),c=t.memoizedState),(a=oi||gl(t,n,a,r,f,c,s))?(u||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),i.props=r,i.state=c,i.context=s,r=a):("function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,si(e,t),u=vl(n,s=t.memoizedProps),i.props=u,d=t.pendingProps,f=i.context,c=n.contextType,a=Ir,"object"==typeof c&&null!==c&&(a=No(c)),(c="function"==typeof(l=n.getDerivedStateFromProps)||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(s!==d||f!==a)&&yl(t,i,r,a),oi=!1,f=t.memoizedState,i.state=f,hi(t,r,i,o),fi();var h=t.memoizedState;s!==d||f!==h||oi||null!==e&&null!==e.dependencies&&To(e.dependencies)?("function"==typeof l&&(pl(t,n,l,r),h=t.memoizedState),(u=oi||gl(t,n,u,r,f,h,a)||null!==e&&null!==e.dependencies&&To(e.dependencies))?(c||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,a),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,a)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=a,r=u):("function"!=typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return i=r,Il(e,t),r=!!(128&t.flags),i||r?(i=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:i.render(),t.flags|=1,null!==e&&r?(t.child=ol(t,e.child,null,o),t.child=ol(t,null,n,o)):Al(e,t,n,o),t.memoizedState=i.state,e=t.child):e=Yl(e,t,o),e}function Bl(e,t,n,r){return go(),t.flags|=256,Al(e,t,n,r),t.child}var jl={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Hl(e){return{baseLanes:e,cachePool:Ko()}}function Vl(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=vc),e}function Ul(e,t,n){var o,i=t.pendingProps,s=!1,l=!!(128&t.flags);if((o=l)||(o=(null===e||null!==e.memoizedState)&&!!(2&fl.current)),o&&(s=!0,t.flags&=-129),o=!!(32&t.flags),t.flags&=-33,null===e){if(lo){if(s?al(t):ul(),lo){var a,c=so;if(a=c){e:{for(a=c,c=co;8!==a.nodeType;){if(!c){c=null;break e}if(null===(a=bd(a.nextSibling))){c=null;break e}}c=a}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Xr?{id:Zr,overflow:eo}:null,retryLane:536870912,hydrationErrors:null},(a=$r(18,null,null,0)).stateNode=c,a.return=t,t.child=a,io=t,so=null,a=!0):a=!1}a||fo(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return vd(c)?t.lanes=32:t.lanes=536870912,null;dl(t)}return c=i.children,i=i.fallback,s?(ul(),c=ql({mode:"hidden",children:c},s=t.mode),i=Vr(i,s,n,null),c.return=t,i.return=t,c.sibling=i,t.child=c,(s=t.child).memoizedState=Hl(n),s.childLanes=Vl(e,o,n),t.memoizedState=jl,i):(al(t),Wl(t,c))}if(null!==(a=e.memoizedState)&&null!==(c=a.dehydrated)){if(l)256&t.flags?(al(t),t.flags&=-257,t=Kl(e,t,n)):null!==t.memoizedState?(ul(),t.child=e.child,t.flags|=128,t=null):(ul(),s=i.fallback,c=t.mode,i=ql({mode:"visible",children:i.children},c),(s=Vr(s,c,n,null)).flags|=2,i.return=t,s.return=t,i.sibling=s,t.child=i,ol(t,e.child,null,n),(i=t.child).memoizedState=Hl(n),i.childLanes=Vl(e,o,n),t.memoizedState=jl,t=s);else if(al(t),vd(c)){if(o=c.nextSibling&&c.nextSibling.dataset)var u=o.dgst;o=u,(i=Error(r(419))).stack="",i.digest=o,vo({value:i,source:null,stack:null}),t=Kl(e,t,n)}else if(Nl||Mo(e,t,n,!1),o=0!==(n&e.childLanes),Nl||o){if(null!==(o=ic)&&(0!==(i=0!==((i=42&(i=n&-n)?1:Ae(i))&(o.suspendedLanes|n))?0:i)&&i!==a.retryLane))throw a.retryLane=i,Rr(e,i),$c(o,e,i),Ol;"$?"===c.data||Qc(),t=Kl(e,t,n)}else"$?"===c.data?(t.flags|=192,t.child=e.child,t=null):(e=a.treeContext,so=bd(c.nextSibling),io=t,lo=!0,ao=null,co=!1,null!==e&&(Gr[Yr++]=Zr,Gr[Yr++]=eo,Gr[Yr++]=Xr,Zr=e.id,eo=e.overflow,Xr=t),(t=Wl(t,i.children)).flags|=4096);return t}return s?(ul(),s=i.fallback,c=t.mode,u=(a=e.child).sibling,(i=Br(a,{mode:"hidden",children:i.children})).subtreeFlags=65011712&a.subtreeFlags,null!==u?s=Br(u,s):(s=Vr(s,c,n,null)).flags|=2,s.return=t,i.return=t,i.sibling=s,t.child=i,i=s,s=t.child,null===(c=e.child.memoizedState)?c=Hl(n):(null!==(a=c.cachePool)?(u=zo._currentValue,a=a.parent!==u?{parent:u,pool:u}:a):a=Ko(),c={baseLanes:c.baseLanes|n,cachePool:a}),s.memoizedState=c,s.childLanes=Vl(e,o,n),t.memoizedState=jl,i):(al(t),e=(n=e.child).sibling,(n=Br(n,{mode:"visible",children:i.children})).return=t,n.sibling=null,null!==e&&(null===(o=t.deletions)?(t.deletions=[e],t.flags|=16):o.push(e)),t.child=n,t.memoizedState=null,n)}function Wl(e,t){return(t=ql({mode:"visible",children:t},e.mode)).return=e,e.child=t}function ql(e,t){return(e=$r(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Kl(e,t,n){return ol(t,e.child,null,n),(e=Wl(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Jl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Co(e.return,t,n)}function Ql(e,t,n,r,o){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Gl(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Al(e,t,r.children,n),2&(r=fl.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Jl(e,n,t);else if(19===e.tag)Jl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(V(fl,r),o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===hl(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ql(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===hl(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ql(t,!0,n,null,i);break;case"together":Ql(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Yl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),mc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Mo(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(r(153));if(null!==t.child){for(n=Br(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Br(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Xl(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!To(e))}function Zl(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Nl=!0;else{if(!(Xl(e,n)||128&t.flags))return Nl=!1,function(e,t,n){switch(t.tag){case 3:J(t,t.stateNode.containerInfo),So(0,zo,e.memoizedState.cache),go();break;case 27:case 5:G(t);break;case 4:J(t,t.stateNode.containerInfo);break;case 10:So(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(al(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Ul(e,t,n):(al(t),null!==(e=Yl(e,t,n))?e.sibling:null);al(t);break;case 19:var o=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(Mo(e,t,n,!1),r=0!==(n&t.childLanes)),o){if(r)return Gl(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),V(fl,fl.current),r)break;return null;case 22:case 23:return t.lanes=0,Ll(e,t,n);case 24:So(0,zo,e.memoizedState.cache)}return Yl(e,t,n)}(e,t,n);Nl=!!(131072&e.flags)}else Nl=!1,lo&&1048576&t.flags&&no(t,Qr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var o=t.elementType,i=o._init;if(o=i(o._payload),t.type=o,"function"!=typeof o){if(null!=o){if((i=o.$$typeof)===w){t.tag=11,t=Dl(null,t,o,e,n);break e}if(i===M){t.tag=14,t=Pl(null,t,o,e,n);break e}}throw t=L(o)||o,Error(r(306,t,""))}Fr(o)?(e=vl(o,e),t.tag=1,t=Fl(null,t,o,e,n)):(t.tag=0,t=_l(null,t,o,e,n))}return t;case 0:return _l(e,t,t.type,t.pendingProps,n);case 1:return Fl(e,t,o=t.type,i=vl(o,t.pendingProps),n);case 3:e:{if(J(t,t.stateNode.containerInfo),null===e)throw Error(r(387));o=t.pendingProps;var s=t.memoizedState;i=s.element,si(e,t),hi(t,o,null,n);var l=t.memoizedState;if(o=l.cache,So(0,zo,o),o!==s.cache&&Eo(t,[zo],n,!0),fi(),o=l.element,s.isDehydrated){if(s={element:o,isDehydrated:!1,cache:l.cache},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=Bl(e,t,o,n);break e}if(o!==i){vo(i=Mr(Error(r(424)),t)),t=Bl(e,t,o,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(so=bd(e.firstChild),io=t,lo=!0,ao=null,co=!0,n=il(t,null,o,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(go(),o===i){t=Yl(e,t,n);break e}Al(e,t,o,n)}t=t.child}return t;case 26:return Il(e,t),null===e?(n=Ad(t.type,null,t.pendingProps,null))?t.memoizedState=n:lo||(n=t.type,e=t.pendingProps,(o=id(q.current).createElement(n))[Le]=t,o[ze]=e,nd(o,n,e),Ke(o),t.stateNode=o):t.memoizedState=Ad(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return G(t),null===e&&lo&&(o=t.stateNode=Sd(t.type,t.pendingProps,q.current),io=t,co=!0,i=so,md(t.type)?(kd=i,so=bd(o.firstChild)):so=i),Al(e,t,t.pendingProps.children,n),Il(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&lo&&((i=o=so)&&(null!==(o=function(e,t,n,r){for(;1===e.nodeType;){var o=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[je])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(i=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(i!==o.rel||e.getAttribute("href")!==(null==o.href||""===o.href?null:o.href)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin)||e.getAttribute("title")!==(null==o.title?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((i=e.getAttribute("src"))!==(null==o.src?null:o.src)||e.getAttribute("type")!==(null==o.type?null:o.type)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var i=null==o.name?null:""+o.name;if("hidden"===o.type&&e.getAttribute("name")===i)return e}if(null===(e=bd(e.nextSibling)))break}return null}(o,t.type,t.pendingProps,co))?(t.stateNode=o,io=t,so=bd(o.firstChild),co=!1,i=!0):i=!1),i||fo(t)),G(t),i=t.type,s=t.pendingProps,l=null!==e?e.memoizedProps:null,o=s.children,ad(i,s)?o=null:null!==l&&ad(i,l)&&(t.flags|=32),null!==t.memoizedState&&(i=Li(e,t,_i,null,null,n),Gd._currentValue=i),Il(e,t),Al(e,t,o,n),t.child;case 6:return null===e&&lo&&((e=n=so)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=bd(e.nextSibling)))return null}return e}(n,t.pendingProps,co))?(t.stateNode=n,io=t,so=null,e=!0):e=!1),e||fo(t)),null;case 13:return Ul(e,t,n);case 4:return J(t,t.stateNode.containerInfo),o=t.pendingProps,null===e?t.child=ol(t,null,o,n):Al(e,t,o,n),t.child;case 11:return Dl(e,t,t.type,t.pendingProps,n);case 7:return Al(e,t,t.pendingProps,n),t.child;case 8:case 12:return Al(e,t,t.pendingProps.children,n),t.child;case 10:return o=t.pendingProps,So(0,t.type,o.value),Al(e,t,o.children,n),t.child;case 9:return i=t.type._context,o=t.pendingProps.children,Oo(t),o=o(i=No(i)),t.flags|=1,Al(e,t,o,n),t.child;case 14:return Pl(e,t,t.type,t.pendingProps,n);case 15:return Rl(e,t,t.type,t.pendingProps,n);case 19:return Gl(e,t,n);case 31:return o=t.pendingProps,n=t.mode,o={mode:o.mode,children:o.children},null===e?((n=ql(o,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Br(e.child,o)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Ll(e,t,n);case 24:return Oo(t),o=No(zo),null===e?(null===(i=Wo())&&(i=ic,s=Io(),i.pooledCache=s,s.refCount++,null!==s&&(i.pooledCacheLanes|=n),i=s),t.memoizedState={parent:o,cache:i},ii(t),So(0,zo,i)):(0!==(e.lanes&n)&&(si(e,t),hi(t,null,null,n),fi()),i=e.memoizedState,s=t.memoizedState,i.parent!==o?(i={parent:o,cache:o},t.memoizedState=i,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=i),So(0,zo,o)):(o=s.cache,So(0,zo,o),o!==i.cache&&Eo(t,[zo],n,!0))),Al(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function ea(e){e.flags|=4}function ta(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!Vd(t)){if(null!==(t=sl.current)&&((4194048&lc)===lc?null!==ll:(62914560&lc)!==lc&&!(536870912&lc)||t!==ll))throw ti=Yo,Qo;e.flags|=8192}}function na(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Ee():536870912,e.lanes|=t,bc|=t)}function ra(e,t){if(!lo)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function oa(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=65011712&o.subtreeFlags,r|=65011712&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ia(e,t,n){var o=t.pendingProps;switch(oo(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return oa(t),null;case 3:return n=t.stateNode,o=null,null!==e&&(o=e.memoizedState.cache),t.memoizedState.cache!==o&&(t.flags|=2048),xo(zo),Q(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(mo(t)?ea(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,yo())),oa(t),null;case 26:return n=t.memoizedState,null===e?(ea(t),null!==n?(oa(t),ta(t,n)):(oa(t),t.flags&=-16777217)):n?n!==e.memoizedState?(ea(t),oa(t),ta(t,n)):(oa(t),t.flags&=-16777217):(e.memoizedProps!==o&&ea(t),oa(t),t.flags&=-16777217),null;case 27:Y(t),n=q.current;var i=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==o&&ea(t);else{if(!o){if(null===t.stateNode)throw Error(r(166));return oa(t),null}e=U.current,mo(t)?ho(t):(e=Sd(i,o,n),t.stateNode=e,ea(t))}return oa(t),null;case 5:if(Y(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==o&&ea(t);else{if(!o){if(null===t.stateNode)throw Error(r(166));return oa(t),null}if(e=U.current,mo(t))ho(t);else{switch(i=id(q.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=i.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof o.is?i.createElement("select",{is:o.is}):i.createElement("select"),o.multiple?e.multiple=!0:o.size&&(e.size=o.size);break;default:e="string"==typeof o.is?i.createElement(n,{is:o.is}):i.createElement(n)}}e[Le]=t,e[ze]=o;e:for(i=t.child;null!==i;){if(5===i.tag||6===i.tag)e.appendChild(i.stateNode);else if(4!==i.tag&&27!==i.tag&&null!==i.child){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;null===i.sibling;){if(null===i.return||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(nd(e,n,o),n){case"button":case"input":case"select":case"textarea":e=!!o.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&ea(t)}}return oa(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==o&&ea(t);else{if("string"!=typeof o&&null===t.stateNode)throw Error(r(166));if(e=q.current,mo(t)){if(e=t.stateNode,n=t.memoizedProps,o=null,null!==(i=io))switch(i.tag){case 27:case 5:o=i.memoizedProps}e[Le]=t,(e=!!(e.nodeValue===n||null!==o&&!0===o.suppressHydrationWarning||Xu(e.nodeValue,n)))||fo(t)}else(e=id(e).createTextNode(o))[Le]=t,t.stateNode=e}return oa(t),null;case 13:if(o=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(i=mo(t),null!==o&&null!==o.dehydrated){if(null===e){if(!i)throw Error(r(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(r(317));i[Le]=t}else go(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;oa(t),i=!1}else i=yo(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return 256&t.flags?(dl(t),t):(dl(t),null)}if(dl(t),128&t.flags)return t.lanes=n,t;if(n=null!==o,e=null!==e&&null!==e.memoizedState,n){i=null,null!==(o=t.child).alternate&&null!==o.alternate.memoizedState&&null!==o.alternate.memoizedState.cachePool&&(i=o.alternate.memoizedState.cachePool.pool);var s=null;null!==o.memoizedState&&null!==o.memoizedState.cachePool&&(s=o.memoizedState.cachePool.pool),s!==i&&(o.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),na(t,t.updateQueue),oa(t),null;case 4:return Q(),null===e&&Hu(t.stateNode.containerInfo),oa(t),null;case 10:return xo(t.type),oa(t),null;case 19:if(H(fl),null===(i=t.memoizedState))return oa(t),null;if(o=!!(128&t.flags),null===(s=i.rendering))if(o)ra(i,!1);else{if(0!==pc||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(s=hl(e))){for(t.flags|=128,ra(i,!1),e=s.updateQueue,t.updateQueue=e,na(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)jr(n,e),n=n.sibling;return V(fl,1&fl.current|2),t.child}e=e.sibling}null!==i.tail&&re()>Cc&&(t.flags|=128,o=!0,ra(i,!1),t.lanes=4194304)}else{if(!o)if(null!==(e=hl(s))){if(t.flags|=128,o=!0,e=e.updateQueue,t.updateQueue=e,na(t,e),ra(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!lo)return oa(t),null}else 2*re()-i.renderingStartTime>Cc&&536870912!==n&&(t.flags|=128,o=!0,ra(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(e=i.last)?e.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=re(),t.sibling=null,e=fl.current,V(fl,o?1&e|2:1&e),t):(oa(t),null);case 22:case 23:return dl(t),ki(),o=null!==t.memoizedState,null!==e?null!==e.memoizedState!==o&&(t.flags|=8192):o&&(t.flags|=8192),o?!!(536870912&n)&&!(128&t.flags)&&(oa(t),6&t.subtreeFlags&&(t.flags|=8192)):oa(t),null!==(n=t.updateQueue)&&na(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),o=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(o=t.memoizedState.cachePool.pool),o!==n&&(t.flags|=2048),null!==e&&H(Uo),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),xo(zo),oa(t),null;case 25:case 30:return null}throw Error(r(156,t.tag))}function sa(e,t){switch(oo(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return xo(zo),Q(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Y(t),null;case 13:if(dl(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(r(340));go()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return H(fl),null;case 4:return Q(),null;case 10:return xo(t.type),null;case 22:case 23:return dl(t),ki(),null!==e&&H(Uo),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return xo(zo),null;default:return null}}function la(e,t){switch(oo(t),t.tag){case 3:xo(zo),Q();break;case 26:case 27:case 5:Y(t);break;case 4:Q();break;case 13:dl(t);break;case 19:H(fl);break;case 10:xo(t.type);break;case 22:case 23:dl(t),ki(),null!==e&&H(Uo);break;case 24:xo(zo)}}function aa(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var o=r.next;n=o;do{if((n.tag&e)===e){r=void 0;var i=n.create,s=n.inst;r=i(),s.destroy=r}n=n.next}while(n!==o)}}catch(l){fu(t,t.return,l)}}function ca(e,t,n){try{var r=t.updateQueue,o=null!==r?r.lastEffect:null;if(null!==o){var i=o.next;r=i;do{if((r.tag&e)===e){var s=r.inst,l=s.destroy;if(void 0!==l){s.destroy=void 0,o=t;var a=n,c=l;try{c()}catch(u){fu(o,a,u)}}}r=r.next}while(r!==i)}}catch(u){fu(t,t.return,u)}}function ua(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{mi(t,n)}catch(r){fu(e,e.return,r)}}}function da(e,t,n){n.props=vl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){fu(e,t,r)}}function fa(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(o){fu(e,t,o)}}function ha(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(o){fu(e,t,o)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(i){fu(e,t,i)}else n.current=null}function pa(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(o){fu(e,e.return,o)}}function ma(e,t,n){try{var o=e.stateNode;!function(e,t,n,o){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,s=null,l=null,a=null,c=null,u=null,d=null;for(p in n){var f=n[p];if(n.hasOwnProperty(p)&&null!=f)switch(p){case"checked":case"value":break;case"defaultValue":c=f;default:o.hasOwnProperty(p)||ed(e,t,p,null,o,f)}}for(var h in o){var p=o[h];if(f=n[h],o.hasOwnProperty(h)&&(null!=p||null!=f))switch(h){case"type":s=p;break;case"name":i=p;break;case"checked":u=p;break;case"defaultChecked":d=p;break;case"value":l=p;break;case"defaultValue":a=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(r(137,t));break;default:p!==f&&ed(e,t,h,p,o,f)}}return void vt(e,l,a,c,u,d,s,i);case"select":for(s in p=l=a=h=null,n)if(c=n[s],n.hasOwnProperty(s)&&null!=c)switch(s){case"value":break;case"multiple":p=c;default:o.hasOwnProperty(s)||ed(e,t,s,null,o,c)}for(i in o)if(s=o[i],c=n[i],o.hasOwnProperty(i)&&(null!=s||null!=c))switch(i){case"value":h=s;break;case"defaultValue":a=s;break;case"multiple":l=s;default:s!==c&&ed(e,t,i,s,o,c)}return t=a,n=l,o=p,void(null!=h?wt(e,!!n,h,!1):!!o!=!!n&&(null!=t?wt(e,!!n,t,!0):wt(e,!!n,n?[]:"",!1)));case"textarea":for(a in p=h=null,n)if(i=n[a],n.hasOwnProperty(a)&&null!=i&&!o.hasOwnProperty(a))switch(a){case"value":case"children":break;default:ed(e,t,a,null,o,i)}for(l in o)if(i=o[l],s=n[l],o.hasOwnProperty(l)&&(null!=i||null!=s))switch(l){case"value":h=i;break;case"defaultValue":p=i;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=i)throw Error(r(91));break;default:i!==s&&ed(e,t,l,i,o,s)}return void St(e,h,p);case"option":for(var m in n)if(h=n[m],n.hasOwnProperty(m)&&null!=h&&!o.hasOwnProperty(m))if("selected"===m)e.selected=!1;else ed(e,t,m,null,o,h);for(c in o)if(h=o[c],p=n[c],o.hasOwnProperty(c)&&h!==p&&(null!=h||null!=p))if("selected"===c)e.selected=h&&"function"!=typeof h&&"symbol"!=typeof h;else ed(e,t,c,h,o,p);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)h=n[g],n.hasOwnProperty(g)&&null!=h&&!o.hasOwnProperty(g)&&ed(e,t,g,null,o,h);for(u in o)if(h=o[u],p=n[u],o.hasOwnProperty(u)&&h!==p&&(null!=h||null!=p))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(r(137,t));break;default:ed(e,t,u,h,o,p)}return;default:if(Ot(t)){for(var y in n)h=n[y],n.hasOwnProperty(y)&&void 0!==h&&!o.hasOwnProperty(y)&&td(e,t,y,void 0,o,h);for(d in o)h=o[d],p=n[d],!o.hasOwnProperty(d)||h===p||void 0===h&&void 0===p||td(e,t,d,h,o,p);return}}for(var v in n)h=n[v],n.hasOwnProperty(v)&&null!=h&&!o.hasOwnProperty(v)&&ed(e,t,v,null,o,h);for(f in o)h=o[f],p=n[f],!o.hasOwnProperty(f)||h===p||null==h&&null==p||ed(e,t,f,h,o,p)}(o,e.type,n,t),o[ze]=t}catch(i){fu(e,e.return,i)}}function ga(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&md(e.type)||4===e.tag}function ya(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ga(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&md(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function va(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Zu));else if(4!==r&&(27===r&&md(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(va(e,t,n),e=e.sibling;null!==e;)va(e,t,n),e=e.sibling}function ba(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&md(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(ba(e,t,n),e=e.sibling;null!==e;)ba(e,t,n),e=e.sibling}function ka(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,o=t.attributes;o.length;)t.removeAttributeNode(o[0]);nd(t,r,n),t[Le]=e,t[ze]=n}catch(i){fu(e,e.return,i)}}var wa=!1,Sa=!1,xa=!1,Ca="function"==typeof WeakSet?WeakSet:Set,Ea=null;function Ma(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Fa(e,n),4&r&&aa(5,n);break;case 1:if(Fa(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(s){fu(n,n.return,s)}else{var o=vl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(l){fu(n,n.return,l)}}64&r&&ua(n),512&r&&fa(n,n.return);break;case 3:if(Fa(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{mi(e,t)}catch(s){fu(n,n.return,s)}}break;case 27:null===t&&4&r&&ka(n);case 26:case 5:Fa(e,n),null===t&&4&r&&pa(n),512&r&&fa(n,n.return);break;case 12:Fa(e,n);break;case 13:Fa(e,n),4&r&&Pa(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=gu.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||wa)){t=null!==t&&null!==t.memoizedState||Sa,o=wa;var i=Sa;wa=r,(Sa=t)&&!i?ja(e,n,!!(8772&n.subtreeFlags)):Fa(e,n),wa=o,Sa=i}break;case 30:break;default:Fa(e,n)}}function Ta(e){var t=e.alternate;null!==t&&(e.alternate=null,Ta(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&He(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Oa=null,Na=!1;function Aa(e,t,n){for(n=n.child;null!==n;)Da(e,t,n),n=n.sibling}function Da(e,t,n){if(he&&"function"==typeof he.onCommitFiberUnmount)try{he.onCommitFiberUnmount(fe,n)}catch(i){}switch(n.tag){case 26:Sa||ha(n,t),Aa(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:Sa||ha(n,t);var r=Oa,o=Na;md(n.type)&&(Oa=n.stateNode,Na=!1),Aa(e,t,n),xd(n.stateNode),Oa=r,Na=o;break;case 5:Sa||ha(n,t);case 6:if(r=Oa,o=Na,Oa=null,Aa(e,t,n),Na=o,null!==(Oa=r))if(Na)try{(9===Oa.nodeType?Oa.body:"HTML"===Oa.nodeName?Oa.ownerDocument.body:Oa).removeChild(n.stateNode)}catch(s){fu(n,t,s)}else try{Oa.removeChild(n.stateNode)}catch(s){fu(n,t,s)}break;case 18:null!==Oa&&(Na?(gd(9===(e=Oa).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Af(e)):gd(Oa,n.stateNode));break;case 4:r=Oa,o=Na,Oa=n.stateNode.containerInfo,Na=!0,Aa(e,t,n),Oa=r,Na=o;break;case 0:case 11:case 14:case 15:Sa||ca(2,n,t),Sa||ca(4,n,t),Aa(e,t,n);break;case 1:Sa||(ha(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&da(n,t,r)),Aa(e,t,n);break;case 21:Aa(e,t,n);break;case 22:Sa=(r=Sa)||null!==n.memoizedState,Aa(e,t,n),Sa=r;break;default:Aa(e,t,n)}}function Pa(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Af(e)}catch(n){fu(t,t.return,n)}}function Ra(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new Ca),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new Ca),t;default:throw Error(r(435,e.tag))}}(e);t.forEach((function(t){var r=yu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function La(e,t){var n=t.deletions;if(null!==n)for(var o=0;o<n.length;o++){var i=n[o],s=e,l=t,a=l;e:for(;null!==a;){switch(a.tag){case 27:if(md(a.type)){Oa=a.stateNode,Na=!1;break e}break;case 5:Oa=a.stateNode,Na=!1;break e;case 3:case 4:Oa=a.stateNode.containerInfo,Na=!0;break e}a=a.return}if(null===Oa)throw Error(r(160));Da(s,l,i),Oa=null,Na=!1,null!==(s=i.alternate)&&(s.return=null),i.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Ia(t,e),t=t.sibling}var za=null;function Ia(e,t){var n=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:La(t,e),_a(e),4&o&&(ca(3,e,e.return),aa(3,e),ca(5,e,e.return));break;case 1:La(t,e),_a(e),512&o&&(Sa||null===n||ha(n,n.return)),64&o&&wa&&(null!==(e=e.updateQueue)&&(null!==(o=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?o:n.concat(o))));break;case 26:var i=za;if(La(t,e),_a(e),512&o&&(Sa||null===n||ha(n,n.return)),4&o){var s=null!==n?n.memoizedState:null;if(o=e.memoizedState,null===n)if(null===o)if(null===e.stateNode){e:{o=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(o){case"title":(!(s=i.getElementsByTagName("title")[0])||s[je]||s[Le]||"http://www.w3.org/2000/svg"===s.namespaceURI||s.hasAttribute("itemprop"))&&(s=i.createElement(o),i.head.insertBefore(s,i.querySelector("head > title"))),nd(s,o,n),s[Le]=e,Ke(s),o=s;break e;case"link":var l=jd("link","href",i).get(o+(n.href||""));if(l)for(var a=0;a<l.length;a++)if((s=l[a]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&s.getAttribute("rel")===(null==n.rel?null:n.rel)&&s.getAttribute("title")===(null==n.title?null:n.title)&&s.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){l.splice(a,1);break t}nd(s=i.createElement(o),o,n),i.head.appendChild(s);break;case"meta":if(l=jd("meta","content",i).get(o+(n.content||"")))for(a=0;a<l.length;a++)if((s=l[a]).getAttribute("content")===(null==n.content?null:""+n.content)&&s.getAttribute("name")===(null==n.name?null:n.name)&&s.getAttribute("property")===(null==n.property?null:n.property)&&s.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&s.getAttribute("charset")===(null==n.charSet?null:n.charSet)){l.splice(a,1);break t}nd(s=i.createElement(o),o,n),i.head.appendChild(s);break;default:throw Error(r(468,o))}s[Le]=e,Ke(s),o=s}e.stateNode=o}else Hd(i,e.type,e.stateNode);else e.stateNode=Id(i,o,e.memoizedProps);else s!==o?(null===s?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):s.count--,null===o?Hd(i,e.type,e.stateNode):Id(i,o,e.memoizedProps)):null===o&&null!==e.stateNode&&ma(e,e.memoizedProps,n.memoizedProps)}break;case 27:La(t,e),_a(e),512&o&&(Sa||null===n||ha(n,n.return)),null!==n&&4&o&&ma(e,e.memoizedProps,n.memoizedProps);break;case 5:if(La(t,e),_a(e),512&o&&(Sa||null===n||ha(n,n.return)),32&e.flags){i=e.stateNode;try{Ct(i,"")}catch(p){fu(e,e.return,p)}}4&o&&null!=e.stateNode&&ma(e,i=e.memoizedProps,null!==n?n.memoizedProps:i),1024&o&&(xa=!0);break;case 6:if(La(t,e),_a(e),4&o){if(null===e.stateNode)throw Error(r(162));o=e.memoizedProps,n=e.stateNode;try{n.nodeValue=o}catch(p){fu(e,e.return,p)}}break;case 3:if(Bd=null,i=za,za=Md(t.containerInfo),La(t,e),za=i,_a(e),4&o&&null!==n&&n.memoizedState.isDehydrated)try{Af(t.containerInfo)}catch(p){fu(e,e.return,p)}xa&&(xa=!1,$a(e));break;case 4:o=za,za=Md(e.stateNode.containerInfo),La(t,e),_a(e),za=o;break;case 12:default:La(t,e),_a(e);break;case 13:La(t,e),_a(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(xc=re()),4&o&&(null!==(o=e.updateQueue)&&(e.updateQueue=null,Ra(e,o)));break;case 22:i=null!==e.memoizedState;var c=null!==n&&null!==n.memoizedState,u=wa,d=Sa;if(wa=u||i,Sa=d||c,La(t,e),Sa=d,wa=u,_a(e),8192&o)e:for(t=e.stateNode,t._visibility=i?-2&t._visibility:1|t._visibility,i&&(null===n||c||wa||Sa||Ba(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){c=n=t;try{if(s=c.stateNode,i)"function"==typeof(l=s.style).setProperty?l.setProperty("display","none","important"):l.display="none";else{a=c.stateNode;var f=c.memoizedProps.style,h=null!=f&&f.hasOwnProperty("display")?f.display:null;a.style.display=null==h||"boolean"==typeof h?"":(""+h).trim()}}catch(p){fu(c,c.return,p)}}}else if(6===t.tag){if(null===n){c=t;try{c.stateNode.nodeValue=i?"":c.memoizedProps}catch(p){fu(c,c.return,p)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&o&&(null!==(o=e.updateQueue)&&(null!==(n=o.retryQueue)&&(o.retryQueue=null,Ra(e,n))));break;case 19:La(t,e),_a(e),4&o&&(null!==(o=e.updateQueue)&&(e.updateQueue=null,Ra(e,o)));case 30:case 21:}}function _a(e){var t=e.flags;if(2&t){try{for(var n,o=e.return;null!==o;){if(ga(o)){n=o;break}o=o.return}if(null==n)throw Error(r(160));switch(n.tag){case 27:var i=n.stateNode;ba(e,ya(e),i);break;case 5:var s=n.stateNode;32&n.flags&&(Ct(s,""),n.flags&=-33),ba(e,ya(e),s);break;case 3:case 4:var l=n.stateNode.containerInfo;va(e,ya(e),l);break;default:throw Error(r(161))}}catch(a){fu(e,e.return,a)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function $a(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;$a(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Fa(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Ma(e,t.alternate,t),t=t.sibling}function Ba(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ca(4,t,t.return),Ba(t);break;case 1:ha(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&da(t,t.return,n),Ba(t);break;case 27:xd(t.stateNode);case 26:case 5:ha(t,t.return),Ba(t);break;case 22:null===t.memoizedState&&Ba(t);break;default:Ba(t)}e=e.sibling}}function ja(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,o=e,i=t,s=i.flags;switch(i.tag){case 0:case 11:case 15:ja(o,i,n),aa(4,i);break;case 1:if(ja(o,i,n),"function"==typeof(o=(r=i).stateNode).componentDidMount)try{o.componentDidMount()}catch(c){fu(r,r.return,c)}if(null!==(o=(r=i).updateQueue)){var l=r.stateNode;try{var a=o.shared.hiddenCallbacks;if(null!==a)for(o.shared.hiddenCallbacks=null,o=0;o<a.length;o++)pi(a[o],l)}catch(c){fu(r,r.return,c)}}n&&64&s&&ua(i),fa(i,i.return);break;case 27:ka(i);case 26:case 5:ja(o,i,n),n&&null===r&&4&s&&pa(i),fa(i,i.return);break;case 12:ja(o,i,n);break;case 13:ja(o,i,n),n&&4&s&&Pa(o,i);break;case 22:null===i.memoizedState&&ja(o,i,n),fa(i,i.return);break;case 30:break;default:ja(o,i,n)}t=t.sibling}}function Ha(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&_o(n))}function Va(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&_o(e))}function Ua(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Wa(e,t,n,r),t=t.sibling}function Wa(e,t,n,r){var o=t.flags;switch(t.tag){case 0:case 11:case 15:Ua(e,t,n,r),2048&o&&aa(9,t);break;case 1:case 13:default:Ua(e,t,n,r);break;case 3:Ua(e,t,n,r),2048&o&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&_o(e)));break;case 12:if(2048&o){Ua(e,t,n,r),e=t.stateNode;try{var i=t.memoizedProps,s=i.id,l=i.onPostCommit;"function"==typeof l&&l(s,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(a){fu(t,t.return,a)}}else Ua(e,t,n,r);break;case 23:break;case 22:i=t.stateNode,s=t.alternate,null!==t.memoizedState?2&i._visibility?Ua(e,t,n,r):Ka(e,t):2&i._visibility?Ua(e,t,n,r):(i._visibility|=2,qa(e,t,n,r,!!(10256&t.subtreeFlags))),2048&o&&Ha(s,t);break;case 24:Ua(e,t,n,r),2048&o&&Va(t.alternate,t)}}function qa(e,t,n,r,o){for(o=o&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var i=e,s=t,l=n,a=r,c=s.flags;switch(s.tag){case 0:case 11:case 15:qa(i,s,l,a,o),aa(8,s);break;case 23:break;case 22:var u=s.stateNode;null!==s.memoizedState?2&u._visibility?qa(i,s,l,a,o):Ka(i,s):(u._visibility|=2,qa(i,s,l,a,o)),o&&2048&c&&Ha(s.alternate,s);break;case 24:qa(i,s,l,a,o),o&&2048&c&&Va(s.alternate,s);break;default:qa(i,s,l,a,o)}t=t.sibling}}function Ka(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,o=r.flags;switch(r.tag){case 22:Ka(n,r),2048&o&&Ha(r.alternate,r);break;case 24:Ka(n,r),2048&o&&Va(r.alternate,r);break;default:Ka(n,r)}t=t.sibling}}var Ja=8192;function Qa(e){if(e.subtreeFlags&Ja)for(e=e.child;null!==e;)Ga(e),e=e.sibling}function Ga(e){switch(e.tag){case 26:Qa(e),e.flags&Ja&&null!==e.memoizedState&&function(e,t,n){if(null===Ud)throw Error(r(475));var o=Ud;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var i=Dd(n.href),s=e.querySelector(Pd(i));if(s)return null!==(e=s._p)&&"object"==typeof e&&"function"==typeof e.then&&(o.count++,o=qd.bind(o),e.then(o,o)),t.state.loading|=4,t.instance=s,void Ke(s);s=e.ownerDocument||e,n=Rd(n),(i=Cd.get(i))&&$d(n,i),Ke(s=s.createElement("link"));var l=s;l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),nd(s,"link",n),t.instance=s}null===o.stylesheets&&(o.stylesheets=new Map),o.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(o.count++,t=qd.bind(o),e.addEventListener("load",t),e.addEventListener("error",t))}}(za,e.memoizedState,e.memoizedProps);break;case 5:default:Qa(e);break;case 3:case 4:var t=za;za=Md(e.stateNode.containerInfo),Qa(e),za=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Ja,Ja=16777216,Qa(e),Ja=t):Qa(e))}}function Ya(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Xa(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ea=r,tc(r,e)}Ya(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Za(e),e=e.sibling}function Za(e){switch(e.tag){case 0:case 11:case 15:Xa(e),2048&e.flags&&ca(9,e,e.return);break;case 3:case 12:default:Xa(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,ec(e)):Xa(e)}}function ec(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ea=r,tc(r,e)}Ya(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:ca(8,t,t.return),ec(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,ec(t));break;default:ec(t)}e=e.sibling}}function tc(e,t){for(;null!==Ea;){var n=Ea;switch(n.tag){case 0:case 11:case 15:ca(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:_o(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Ea=r;else e:for(n=e;null!==Ea;){var o=(r=Ea).sibling,i=r.return;if(Ta(r),r===n){Ea=null;break e}if(null!==o){o.return=i,Ea=o;break e}Ea=i}}}var nc={getCacheForType:function(e){var t=No(zo),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},rc="function"==typeof WeakMap?WeakMap:Map,oc=0,ic=null,sc=null,lc=0,ac=0,cc=null,uc=!1,dc=!1,fc=!1,hc=0,pc=0,mc=0,gc=0,yc=0,vc=0,bc=0,kc=null,wc=null,Sc=!1,xc=0,Cc=1/0,Ec=null,Mc=null,Tc=0,Oc=null,Nc=null,Ac=0,Dc=0,Pc=null,Rc=null,Lc=0,zc=null;function Ic(){if(2&oc&&0!==lc)return lc&-lc;if(null!==I.T){return 0!==Bo?Bo:Du()}return Pe()}function _c(){0===vc&&(vc=536870912&lc&&!lo?536870912:Ce());var e=sl.current;return null!==e&&(e.flags|=32),vc}function $c(e,t,n){(e!==ic||2!==ac&&9!==ac)&&null===e.cancelPendingCommit||(Wc(e,0),Hc(e,lc,vc,!1)),Te(e,n),2&oc&&e===ic||(e===ic&&(!(2&oc)&&(gc|=n),4===pc&&Hc(e,lc,vc,!1)),Cu(e))}function Fc(e,t,n){if(6&oc)throw Error(r(327));for(var o=!n&&!(124&t)&&0===(t&e.expiredLanes)||Se(e,t),i=o?function(e,t){var n=oc;oc|=2;var o=Kc(),i=Jc();ic!==e||lc!==t?(Ec=null,Cc=re()+500,Wc(e,t)):dc=Se(e,t);e:for(;;)try{if(0!==ac&&null!==sc){t=sc;var s=cc;t:switch(ac){case 1:ac=0,cc=null,tu(e,t,s,1);break;case 2:case 9:if(Xo(s)){ac=0,cc=null,eu(t);break}t=function(){2!==ac&&9!==ac||ic!==e||(ac=7),Cu(e)},s.then(t,t);break e;case 3:ac=7;break e;case 4:ac=5;break e;case 7:Xo(s)?(ac=0,cc=null,eu(t)):(ac=0,cc=null,tu(e,t,s,7));break;case 5:var l=null;switch(sc.tag){case 26:l=sc.memoizedState;case 5:case 27:var a=sc;if(!l||Vd(l)){ac=0,cc=null;var c=a.sibling;if(null!==c)sc=c;else{var u=a.return;null!==u?(sc=u,nu(u)):sc=null}break t}}ac=0,cc=null,tu(e,t,s,5);break;case 6:ac=0,cc=null,tu(e,t,s,6);break;case 8:Uc(),pc=6;break e;default:throw Error(r(462))}}Xc();break}catch(d){qc(e,d)}return wo=ko=null,I.H=o,I.A=i,oc=n,null!==sc?0:(ic=null,lc=0,Ar(),pc)}(e,t):Gc(e,t,!0),s=o;;){if(0===i){dc&&!o&&Hc(e,t,0,!1);break}if(n=e.current.alternate,!s||jc(n)){if(2===i){if(s=t,e.errorRecoveryDisabledLanes&s)var l=0;else l=0!==(l=-536870913&e.pendingLanes)?l:536870912&l?536870912:0;if(0!==l){t=l;e:{var a=e;i=kc;var c=a.current.memoizedState.isDehydrated;if(c&&(Wc(a,l).flags|=256),2!==(l=Gc(a,l,!1))){if(fc&&!c){a.errorRecoveryDisabledLanes|=s,gc|=s,i=4;break e}s=wc,wc=i,null!==s&&(null===wc?wc=s:wc.push.apply(wc,s))}i=l}if(s=!1,2!==i)continue}}if(1===i){Wc(e,0),Hc(e,t,0,!0);break}e:{switch(o=e,s=i){case 0:case 1:throw Error(r(345));case 4:if((4194048&t)!==t)break;case 6:Hc(o,t,vc,!uc);break e;case 2:wc=null;break;case 3:case 5:break;default:throw Error(r(329))}if((62914560&t)===t&&10<(i=xc+300-re())){if(Hc(o,t,vc,!uc),0!==we(o,0,!0))break e;o.timeoutHandle=ud(Bc.bind(null,o,n,wc,Ec,Sc,t,vc,gc,bc,uc,s,2,-0,0),i)}else Bc(o,n,wc,Ec,Sc,t,vc,gc,bc,uc,s,0,-0,0)}break}i=Gc(e,t,!1),s=!1}Cu(e)}function Bc(e,t,n,o,i,s,l,a,c,u,d,f,h,p){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||!(16785408&~f))&&(Ud={stylesheets:null,count:0,unsuspend:Wd},Ga(t),null!==(f=function(){if(null===Ud)throw Error(r(475));var e=Ud;return e.stylesheets&&0===e.count&&Jd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Jd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(ou.bind(null,e,t,s,n,o,i,l,a,c,d,1,h,p)),void Hc(e,s,l,!u);ou(e,t,s,n,o,i,l,a,c)}function jc(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Yn(i(),o))return!1}catch(s){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Hc(e,t,n,r){t&=~yc,t&=~gc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var o=t;0<o;){var i=31-me(o),s=1<<i;r[i]=-1,o&=~s}0!==n&&Oe(e,n,t)}function Vc(){return!!(6&oc)||(Eu(0),!1)}function Uc(){if(null!==sc){if(0===ac)var e=sc.return;else wo=ko=null,Bi(e=sc),Ys=null,Xs=0,e=sc;for(;null!==e;)la(e.alternate,e),e=e.return;sc=null}}function Wc(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,dd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Uc(),ic=e,sc=n=Br(e.current,null),lc=t,ac=0,cc=null,uc=!1,dc=Se(e,t),fc=!1,bc=vc=yc=gc=mc=pc=0,wc=kc=null,Sc=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var o=31-me(r),i=1<<o;t|=e[o],r&=~i}return hc=t,Ar(),n}function qc(e,t){Si=null,I.H=Ks,t===Jo||t===Go?(t=ni(),ac=3):t===Qo?(t=ni(),ac=4):ac=t===Ol?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,cc=t,null===sc&&(pc=1,xl(e,Mr(t,e.current)))}function Kc(){var e=I.H;return I.H=Ks,null===e?Ks:e}function Jc(){var e=I.A;return I.A=nc,e}function Qc(){pc=4,uc||(4194048&lc)!==lc&&null!==sl.current||(dc=!0),!(134217727&mc)&&!(134217727&gc)||null===ic||Hc(ic,lc,vc,!1)}function Gc(e,t,n){var r=oc;oc|=2;var o=Kc(),i=Jc();ic===e&&lc===t||(Ec=null,Wc(e,t)),t=!1;var s=pc;e:for(;;)try{if(0!==ac&&null!==sc){var l=sc,a=cc;switch(ac){case 8:Uc(),s=6;break e;case 3:case 2:case 9:case 6:null===sl.current&&(t=!0);var c=ac;if(ac=0,cc=null,tu(e,l,a,c),n&&dc){s=0;break e}break;default:c=ac,ac=0,cc=null,tu(e,l,a,c)}}Yc(),s=pc;break}catch(u){qc(e,u)}return t&&e.shellSuspendCounter++,wo=ko=null,oc=r,I.H=o,I.A=i,null===sc&&(ic=null,lc=0,Ar()),s}function Yc(){for(;null!==sc;)Zc(sc)}function Xc(){for(;null!==sc&&!te();)Zc(sc)}function Zc(e){var t=Zl(e.alternate,e,hc);e.memoizedProps=e.pendingProps,null===t?nu(e):sc=t}function eu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=$l(n,t,t.pendingProps,t.type,void 0,lc);break;case 11:t=$l(n,t,t.pendingProps,t.type.render,t.ref,lc);break;case 5:Bi(t);default:la(n,t),t=Zl(n,t=sc=jr(t,hc),hc)}e.memoizedProps=e.pendingProps,null===t?nu(e):sc=t}function tu(e,t,n,o){wo=ko=null,Bi(t),Ys=null,Xs=0;var i=t.return;try{if(function(e,t,n,o,i){if(n.flags|=32768,null!==o&&"object"==typeof o&&"function"==typeof o.then){if(null!==(t=n.alternate)&&Mo(t,n,i,!0),null!==(n=sl.current)){switch(n.tag){case 13:return null===ll?Qc():null===n.alternate&&0===pc&&(pc=3),n.flags&=-257,n.flags|=65536,n.lanes=i,o===Yo?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([o]):t.add(o),hu(e,o,i)),!1;case 22:return n.flags|=65536,o===Yo?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([o])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([o]):n.add(o),hu(e,o,i)),!1}throw Error(r(435,n.tag))}return hu(e,o,i),Qc(),!1}if(lo)return null!==(t=sl.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=i,o!==uo&&vo(Mr(e=Error(r(422),{cause:o}),n))):(o!==uo&&vo(Mr(t=Error(r(423),{cause:o}),n)),(e=e.current.alternate).flags|=65536,i&=-i,e.lanes|=i,o=Mr(o,n),ui(e,i=El(e.stateNode,o,i)),4!==pc&&(pc=2)),!1;var s=Error(r(520),{cause:o});if(s=Mr(s,n),null===kc?kc=[s]:kc.push(s),4!==pc&&(pc=2),null===t)return!0;o=Mr(o,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,ui(n,e=El(n.stateNode,o,e)),!1;case 1:if(t=n.type,s=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===s||"function"!=typeof s.componentDidCatch||null!==Mc&&Mc.has(s))))return n.flags|=65536,i&=-i,n.lanes|=i,Tl(i=Ml(i),e,n,o),ui(n,i),!1}n=n.return}while(null!==n);return!1}(e,i,t,n,lc))return pc=1,xl(e,Mr(n,e.current)),void(sc=null)}catch(s){if(null!==i)throw sc=i,s;return pc=1,xl(e,Mr(n,e.current)),void(sc=null)}32768&t.flags?(lo||1===o?e=!0:dc||536870912&lc?e=!1:(uc=e=!0,(2===o||9===o||3===o||6===o)&&(null!==(o=sl.current)&&13===o.tag&&(o.flags|=16384))),ru(t,e)):nu(t)}function nu(e){var t=e;do{if(32768&t.flags)return void ru(t,uc);e=t.return;var n=ia(t.alternate,t,hc);if(null!==n)return void(sc=n);if(null!==(t=t.sibling))return void(sc=t);sc=t=e}while(null!==t);0===pc&&(pc=5)}function ru(e,t){do{var n=sa(e.alternate,e);if(null!==n)return n.flags&=32767,void(sc=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(sc=e);sc=e=n}while(null!==e);pc=6,sc=null}function ou(e,t,n,o,i,s,l,a,c){e.cancelPendingCommit=null;do{cu()}while(0!==Tc);if(6&oc)throw Error(r(327));if(null!==t){if(t===e.current)throw Error(r(177));if(s=t.lanes|t.childLanes,function(e,t,n,r,o,i){var s=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var l=e.entanglements,a=e.expirationTimes,c=e.hiddenUpdates;for(n=s&~n;0<n;){var u=31-me(n),d=1<<u;l[u]=0,a[u]=-1;var f=c[u];if(null!==f)for(c[u]=null,u=0;u<f.length;u++){var h=f[u];null!==h&&(h.lane&=-536870913)}n&=~d}0!==r&&Oe(e,r,0),0!==i&&0===o&&0!==e.tag&&(e.suspendedLanes|=i&~(s&~t))}(e,n,s|=Nr,l,a,c),e===ic&&(sc=ic=null,lc=0),Nc=t,Oc=e,Ac=n,Dc=s,Pc=i,Rc=o,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,Z(le,(function(){return uu(),null}))):(e.callbackNode=null,e.callbackPriority=0),o=!!(13878&t.flags),13878&t.subtreeFlags||o){o=I.T,I.T=null,i=_.p,_.p=2,l=oc,oc|=4;try{!function(e,t){if(e=e.containerInfo,rd=of,rr(e=nr(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var o=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(o&&0!==o.rangeCount){n=o.anchorNode;var i=o.anchorOffset,s=o.focusNode;o=o.focusOffset;try{n.nodeType,s.nodeType}catch(g){n=null;break e}var l=0,a=-1,c=-1,u=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==i&&3!==f.nodeType||(a=l+i),f!==s||0!==o&&3!==f.nodeType||(c=l+o),3===f.nodeType&&(l+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++u===i&&(a=l),h===s&&++d===o&&(c=l),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===a||-1===c?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(od={focusedElem:e,selectionRange:n},of=!1,Ea=t;null!==Ea;)if(e=(t=Ea).child,1024&t.subtreeFlags&&null!==e)e.return=t,Ea=e;else for(;null!==Ea;){switch(s=(t=Ea).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==s){e=void 0,n=t,i=s.memoizedProps,s=s.memoizedState,o=n.stateNode;try{var m=vl(n.type,i,(n.elementType,n.type));e=o.getSnapshotBeforeUpdate(m,s),o.__reactInternalSnapshotBeforeUpdate=e}catch(y){fu(n,n.return,y)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))yd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":yd(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(r(163))}if(null!==(e=t.sibling)){e.return=t.return,Ea=e;break}Ea=t.return}}(e,t)}finally{oc=l,_.p=i,I.T=o}}Tc=1,iu(),su(),lu()}}function iu(){if(1===Tc){Tc=0;var e=Oc,t=Nc,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=I.T,I.T=null;var r=_.p;_.p=2;var o=oc;oc|=4;try{Ia(t,e);var i=od,s=nr(e.containerInfo),l=i.focusedElem,a=i.selectionRange;if(s!==l&&l&&l.ownerDocument&&tr(l.ownerDocument.documentElement,l)){if(null!==a&&rr(l)){var c=a.start,u=a.end;if(void 0===u&&(u=c),"selectionStart"in l)l.selectionStart=c,l.selectionEnd=Math.min(u,l.value.length);else{var d=l.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var h=f.getSelection(),p=l.textContent.length,m=Math.min(a.start,p),g=void 0===a.end?m:Math.min(a.end,p);!h.extend&&m>g&&(s=g,g=m,m=s);var y=er(l,m),v=er(l,g);if(y&&v&&(1!==h.rangeCount||h.anchorNode!==y.node||h.anchorOffset!==y.offset||h.focusNode!==v.node||h.focusOffset!==v.offset)){var b=d.createRange();b.setStart(y.node,y.offset),h.removeAllRanges(),m>g?(h.addRange(b),h.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),h.addRange(b))}}}}for(d=[],h=l;h=h.parentNode;)1===h.nodeType&&d.push({element:h,left:h.scrollLeft,top:h.scrollTop});for("function"==typeof l.focus&&l.focus(),l=0;l<d.length;l++){var k=d[l];k.element.scrollLeft=k.left,k.element.scrollTop=k.top}}of=!!rd,od=rd=null}finally{oc=o,_.p=r,I.T=n}}e.current=t,Tc=2}}function su(){if(2===Tc){Tc=0;var e=Oc,t=Nc,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=I.T,I.T=null;var r=_.p;_.p=2;var o=oc;oc|=4;try{Ma(e,t.alternate,t)}finally{oc=o,_.p=r,I.T=n}}Tc=3}}function lu(){if(4===Tc||3===Tc){Tc=0,ne();var e=Oc,t=Nc,n=Ac,r=Rc;10256&t.subtreeFlags||10256&t.flags?Tc=5:(Tc=0,Nc=Oc=null,au(e,e.pendingLanes));var o=e.pendingLanes;if(0===o&&(Mc=null),De(n),t=t.stateNode,he&&"function"==typeof he.onCommitFiberRoot)try{he.onCommitFiberRoot(fe,t,void 0,!(128&~t.current.flags))}catch(a){}if(null!==r){t=I.T,o=_.p,_.p=2,I.T=null;try{for(var i=e.onRecoverableError,s=0;s<r.length;s++){var l=r[s];i(l.value,{componentStack:l.stack})}}finally{I.T=t,_.p=o}}3&Ac&&cu(),Cu(e),o=e.pendingLanes,4194090&n&&42&o?e===zc?Lc++:(Lc=0,zc=e):Lc=0,Eu(0)}}function au(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,_o(t)))}function cu(e){return iu(),su(),lu(),uu()}function uu(){if(5!==Tc)return!1;var e=Oc,t=Dc;Dc=0;var n=De(Ac),o=I.T,i=_.p;try{_.p=32>n?32:n,I.T=null,n=Pc,Pc=null;var s=Oc,l=Ac;if(Tc=0,Nc=Oc=null,Ac=0,6&oc)throw Error(r(331));var a=oc;if(oc|=4,Za(s.current),Wa(s,s.current,l,n),oc=a,Eu(0,!1),he&&"function"==typeof he.onPostCommitFiberRoot)try{he.onPostCommitFiberRoot(fe,s)}catch(c){}return!0}finally{_.p=i,I.T=o,au(e,t)}}function du(e,t,n){t=Mr(n,t),null!==(e=ai(e,t=El(e.stateNode,t,2),2))&&(Te(e,2),Cu(e))}function fu(e,t,n){if(3===e.tag)du(e,e,n);else for(;null!==t;){if(3===t.tag){du(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Mc||!Mc.has(r))){e=Mr(n,e),null!==(r=ai(t,n=Ml(2),2))&&(Tl(n,r,t,e),Te(r,2),Cu(r));break}}t=t.return}}function hu(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new rc;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(fc=!0,o.add(n),e=pu.bind(null,e,t,n),t.then(e,e))}function pu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ic===e&&(lc&n)===n&&(4===pc||3===pc&&(62914560&lc)===lc&&300>re()-xc?!(2&oc)&&Wc(e,0):yc|=n,bc===lc&&(bc=0)),Cu(e)}function mu(e,t){0===t&&(t=Ee()),null!==(e=Rr(e,t))&&(Te(e,t),Cu(e))}function gu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),mu(e,n)}function yu(e,t){var n=0;switch(e.tag){case 13:var o=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:o=e.stateNode;break;case 22:o=e.stateNode._retryCache;break;default:throw Error(r(314))}null!==o&&o.delete(t),mu(e,n)}var vu=null,bu=null,ku=!1,wu=!1,Su=!1,xu=0;function Cu(e){e!==bu&&null===e.next&&(null===bu?vu=bu=e:bu=bu.next=e),wu=!0,ku||(ku=!0,hd((function(){6&oc?Z(ie,Mu):Tu()})))}function Eu(e,t){if(!Su&&wu){Su=!0;do{for(var n=!1,r=vu;null!==r;){if(0!==e){var o=r.pendingLanes;if(0===o)var i=0;else{var s=r.suspendedLanes,l=r.pingedLanes;i=(1<<31-me(42|e)+1)-1,i=201326741&(i&=o&~(s&~l))?201326741&i|1:i?2|i:0}0!==i&&(n=!0,Au(r,i))}else i=lc,!(3&(i=we(r,r===ic?i:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||Se(r,i)||(n=!0,Au(r,i));r=r.next}}while(n);Su=!1}}function Mu(){Tu()}function Tu(){wu=ku=!1;var e=0;0!==xu&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==cd&&(cd=e,!0);return cd=null,!1}()&&(e=xu),xu=0);for(var t=re(),n=null,r=vu;null!==r;){var o=r.next,i=Ou(r,t);0===i?(r.next=null,null===n?vu=o:n.next=o,null===o&&(bu=n)):(n=r,(0!==e||3&i)&&(wu=!0)),r=o}Eu(e)}function Ou(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=-62914561&e.pendingLanes;0<i;){var s=31-me(i),l=1<<s,a=o[s];-1===a?0!==(l&n)&&0===(l&r)||(o[s]=xe(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}if(n=lc,n=we(e,e===(t=ic)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===ac||9===ac)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&ee(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||Se(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&ee(r),De(n)){case 2:case 8:n=se;break;case 32:default:n=le;break;case 268435456:n=ce}return r=Nu.bind(null,e),n=Z(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&ee(r),e.callbackPriority=2,e.callbackNode=null,2}function Nu(e,t){if(0!==Tc&&5!==Tc)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(cu()&&e.callbackNode!==n)return null;var r=lc;return 0===(r=we(e,e===ic?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Fc(e,r,t),Ou(e,re()),null!=e.callbackNode&&e.callbackNode===n?Nu.bind(null,e):null)}function Au(e,t){if(cu())return null;Fc(e,t,!0)}function Du(){return 0===xu&&(xu=Ce()),xu}function Pu(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:Dt(""+e)}function Ru(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Lu=0;Lu<xr.length;Lu++){var zu=xr[Lu];Cr(zu.toLowerCase(),"on"+(zu[0].toUpperCase()+zu.slice(1)))}Cr(mr,"onAnimationEnd"),Cr(gr,"onAnimationIteration"),Cr(yr,"onAnimationStart"),Cr("dblclick","onDoubleClick"),Cr("focusin","onFocus"),Cr("focusout","onBlur"),Cr(vr,"onTransitionRun"),Cr(br,"onTransitionStart"),Cr(kr,"onTransitionCancel"),Cr(wr,"onTransitionEnd"),Ye("onMouseEnter",["mouseout","mouseover"]),Ye("onMouseLeave",["mouseout","mouseover"]),Ye("onPointerEnter",["pointerout","pointerover"]),Ye("onPointerLeave",["pointerout","pointerover"]),Ge("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ge("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ge("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ge("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Iu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),_u=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Iu));function $u(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,c=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;i=l,o.currentTarget=c;try{i(o)}catch(u){bl(u)}o.currentTarget=null,i=a}else for(s=0;s<r.length;s++){if(a=(l=r[s]).instance,c=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;i=l,o.currentTarget=c;try{i(o)}catch(u){bl(u)}o.currentTarget=null,i=a}}}}function Fu(e,t){var n=t[_e];void 0===n&&(n=t[_e]=new Set);var r=e+"__bubble";n.has(r)||(Vu(t,e,2,!1),n.add(r))}function Bu(e,t,n){var r=0;t&&(r|=4),Vu(n,e,r,t)}var ju="_reactListening"+Math.random().toString(36).slice(2);function Hu(e){if(!e[ju]){e[ju]=!0,Je.forEach((function(t){"selectionchange"!==t&&(_u.has(t)||Bu(t,!1,e),Bu(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[ju]||(t[ju]=!0,Bu("selectionchange",!1,t))}}function Vu(e,t,n,r){switch(ff(t)){case 2:var o=sf;break;case 8:o=lf;break;default:o=af}n=o.bind(null,t,n,e),o=void 0,!jt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Uu(e,t,n,r,o){var s=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var a=r.stateNode.containerInfo;if(a===o)break;if(4===l)for(l=r.return;null!==l;){var c=l.tag;if((3===c||4===c)&&l.stateNode.containerInfo===o)return;l=l.return}for(;null!==a;){if(null===(l=Ve(a)))return;if(5===(c=l.tag)||6===c||26===c||27===c){r=s=l;continue e}a=a.parentNode}}r=r.return}$t((function(){var r=s,o=Rt(n),l=[];e:{var a=Sr.get(e);if(void 0!==a){var c=tn,u=e;switch(e){case"keypress":if(0===Kt(n))break e;case"keydown":case"keyup":c=yn;break;case"focusin":u="focus",c=an;break;case"focusout":u="blur",c=an;break;case"beforeblur":case"afterblur":c=an;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=sn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=ln;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=bn;break;case mr:case gr:case yr:c=cn;break;case wr:c=kn;break;case"scroll":case"scrollend":c=rn;break;case"wheel":c=wn;break;case"copy":case"cut":case"paste":c=un;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=vn;break;case"toggle":case"beforetoggle":c=Sn}var d=!!(4&t),f=!d&&("scroll"===e||"scrollend"===e),h=d?null!==a?a+"Capture":null:a;d=[];for(var p,m=r;null!==m;){var g=m;if(p=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===p||null===h||null!=(g=Ft(m,h))&&d.push(Wu(m,g,p)),f)break;m=m.return}0<d.length&&(a=new c(a,u,null,n,o),l.push({event:a,listeners:d}))}}if(!(7&t)){if(c="mouseout"===e||"pointerout"===e,(!(a="mouseover"===e||"pointerover"===e)||n===Pt||!(u=n.relatedTarget||n.fromElement)||!Ve(u)&&!u[Ie])&&(c||a)&&(a=o.window===o?o:(a=o.ownerDocument)?a.defaultView||a.parentWindow:window,c?(c=r,null!==(u=(u=n.relatedTarget||n.toElement)?Ve(u):null)&&(f=i(u),d=u.tag,u!==f||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=r),c!==u)){if(d=sn,g="onMouseLeave",h="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=vn,g="onPointerLeave",h="onPointerEnter",m="pointer"),f=null==c?a:We(c),p=null==u?a:We(u),(a=new d(g,m+"leave",c,n,o)).target=f,a.relatedTarget=p,g=null,Ve(o)===r&&((d=new d(h,m+"enter",u,n,o)).target=p,d.relatedTarget=f,g=d),f=g,c&&u)e:{for(h=u,m=0,p=d=c;p;p=Ku(p))m++;for(p=0,g=h;g;g=Ku(g))p++;for(;0<m-p;)d=Ku(d),m--;for(;0<p-m;)h=Ku(h),p--;for(;m--;){if(d===h||null!==h&&d===h.alternate)break e;d=Ku(d),h=Ku(h)}d=null}else d=null;null!==c&&Ju(l,a,c,d,!1),null!==u&&null!==f&&Ju(l,f,u,d,!0)}if("select"===(c=(a=r?We(r):window).nodeName&&a.nodeName.toLowerCase())||"input"===c&&"file"===a.type)var y=Bn;else if(Ln(a))if(jn)y=Gn;else{y=Jn;var v=Kn}else!(c=a.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==a.type&&"radio"!==a.type?r&&Ot(r.elementType)&&(y=Bn):y=Qn;switch(y&&(y=y(e,r))?zn(l,y,n,o):(v&&v(e,a,r),"focusout"===e&&r&&"number"===a.type&&null!=r.memoizedProps.value&&kt(a,"number",a.value)),v=r?We(r):window,e){case"focusin":(Ln(v)||"true"===v.contentEditable)&&(ir=v,sr=r,lr=null);break;case"focusout":lr=sr=ir=null;break;case"mousedown":ar=!0;break;case"contextmenu":case"mouseup":case"dragend":ar=!1,cr(l,n,o);break;case"selectionchange":if(or)break;case"keydown":case"keyup":cr(l,n,o)}var b;if(Cn)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else Pn?An(e,n)&&(k="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(k="onCompositionStart");k&&(Tn&&"ko"!==n.locale&&(Pn||"onCompositionStart"!==k?"onCompositionEnd"===k&&Pn&&(b=qt()):(Ut="value"in(Vt=o)?Vt.value:Vt.textContent,Pn=!0)),0<(v=qu(r,k)).length&&(k=new dn(k,e,null,n,o),l.push({event:k,listeners:v}),b?k.data=b:null!==(b=Dn(n))&&(k.data=b))),(b=Mn?function(e,t){switch(e){case"compositionend":return Dn(t);case"keypress":return 32!==t.which?null:(Nn=!0,On);case"textInput":return(e=t.data)===On&&Nn?null:e;default:return null}}(e,n):function(e,t){if(Pn)return"compositionend"===e||!Cn&&An(e,t)?(e=qt(),Wt=Ut=Vt=null,Pn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Tn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(k=qu(r,"onBeforeInput")).length&&(v=new dn("onBeforeInput","beforeinput",null,n,o),l.push({event:v,listeners:k}),v.data=b)),function(e,t,n,r,o){if("submit"===t&&n&&n.stateNode===o){var i=Pu((o[ze]||null).action),s=r.submitter;s&&null!==(t=(t=s[ze]||null)?Pu(t.formAction):s.getAttribute("formAction"))&&(i=t,s=null);var l=new tn("action","action",null,r,o);e.push({event:l,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==xu){var e=s?Ru(o,s):new FormData(o);Rs(n,{pending:!0,data:e,method:o.method,action:i},null,e)}}else"function"==typeof i&&(l.preventDefault(),e=s?Ru(o,s):new FormData(o),Rs(n,{pending:!0,data:e,method:o.method,action:i},i,e))},currentTarget:o}]})}}(l,e,r,n,o)}$u(l,t)}))}function Wu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qu(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,i=o.stateNode;if(5!==(o=o.tag)&&26!==o&&27!==o||null===i||(null!=(o=Ft(e,n))&&r.unshift(Wu(e,o,i)),null!=(o=Ft(e,t))&&r.push(Wu(e,o,i))),3===e.tag)return r;e=e.return}return[]}function Ku(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Ju(e,t,n,r,o){for(var i=t._reactName,s=[];null!==n&&n!==r;){var l=n,a=l.alternate,c=l.stateNode;if(l=l.tag,null!==a&&a===r)break;5!==l&&26!==l&&27!==l||null===c||(a=c,o?null!=(c=Ft(n,i))&&s.unshift(Wu(n,c,a)):o||null!=(c=Ft(n,i))&&s.push(Wu(n,c,a))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Qu=/\r\n?/g,Gu=/\u0000|\uFFFD/g;function Yu(e){return("string"==typeof e?e:""+e).replace(Qu,"\n").replace(Gu,"")}function Xu(e,t){return t=Yu(t),Yu(e)===t}function Zu(){}function ed(e,t,n,o,i,s){switch(n){case"children":"string"==typeof o?"body"===t||"textarea"===t&&""===o||Ct(e,o):("number"==typeof o||"bigint"==typeof o)&&"body"!==t&&Ct(e,""+o);break;case"className":ot(e,"class",o);break;case"tabIndex":ot(e,"tabindex",o);break;case"dir":case"role":case"viewBox":case"width":case"height":ot(e,n,o);break;case"style":Tt(e,o,s);break;case"data":if("object"!==t){ot(e,"data",o);break}case"src":case"href":if(""===o&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==o||"function"==typeof o||"symbol"==typeof o||"boolean"==typeof o){e.removeAttribute(n);break}o=Dt(""+o),e.setAttribute(n,o);break;case"action":case"formAction":if("function"==typeof o){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof s&&("formAction"===n?("input"!==t&&ed(e,t,"name",i.name,i,null),ed(e,t,"formEncType",i.formEncType,i,null),ed(e,t,"formMethod",i.formMethod,i,null),ed(e,t,"formTarget",i.formTarget,i,null)):(ed(e,t,"encType",i.encType,i,null),ed(e,t,"method",i.method,i,null),ed(e,t,"target",i.target,i,null))),null==o||"symbol"==typeof o||"boolean"==typeof o){e.removeAttribute(n);break}o=Dt(""+o),e.setAttribute(n,o);break;case"onClick":null!=o&&(e.onclick=Zu);break;case"onScroll":null!=o&&Fu("scroll",e);break;case"onScrollEnd":null!=o&&Fu("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=o){if("object"!=typeof o||!("__html"in o))throw Error(r(61));if(null!=(n=o.__html)){if(null!=i.children)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=o&&"function"!=typeof o&&"symbol"!=typeof o;break;case"muted":e.muted=o&&"function"!=typeof o&&"symbol"!=typeof o;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==o||"function"==typeof o||"boolean"==typeof o||"symbol"==typeof o){e.removeAttribute("xlink:href");break}n=Dt(""+o),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=o&&"function"!=typeof o&&"symbol"!=typeof o?e.setAttribute(n,""+o):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":o&&"function"!=typeof o&&"symbol"!=typeof o?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===o?e.setAttribute(n,""):!1!==o&&null!=o&&"function"!=typeof o&&"symbol"!=typeof o?e.setAttribute(n,o):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&!isNaN(o)&&1<=o?e.setAttribute(n,o):e.removeAttribute(n);break;case"rowSpan":case"start":null==o||"function"==typeof o||"symbol"==typeof o||isNaN(o)?e.removeAttribute(n):e.setAttribute(n,o);break;case"popover":Fu("beforetoggle",e),Fu("toggle",e),rt(e,"popover",o);break;case"xlinkActuate":it(e,"http://www.w3.org/1999/xlink","xlink:actuate",o);break;case"xlinkArcrole":it(e,"http://www.w3.org/1999/xlink","xlink:arcrole",o);break;case"xlinkRole":it(e,"http://www.w3.org/1999/xlink","xlink:role",o);break;case"xlinkShow":it(e,"http://www.w3.org/1999/xlink","xlink:show",o);break;case"xlinkTitle":it(e,"http://www.w3.org/1999/xlink","xlink:title",o);break;case"xlinkType":it(e,"http://www.w3.org/1999/xlink","xlink:type",o);break;case"xmlBase":it(e,"http://www.w3.org/XML/1998/namespace","xml:base",o);break;case"xmlLang":it(e,"http://www.w3.org/XML/1998/namespace","xml:lang",o);break;case"xmlSpace":it(e,"http://www.w3.org/XML/1998/namespace","xml:space",o);break;case"is":rt(e,"is",o);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&rt(e,n=Nt.get(n)||n,o)}}function td(e,t,n,o,i,s){switch(n){case"style":Tt(e,o,s);break;case"dangerouslySetInnerHTML":if(null!=o){if("object"!=typeof o||!("__html"in o))throw Error(r(61));if(null!=(n=o.__html)){if(null!=i.children)throw Error(r(60));e.innerHTML=n}}break;case"children":"string"==typeof o?Ct(e,o):("number"==typeof o||"bigint"==typeof o)&&Ct(e,""+o);break;case"onScroll":null!=o&&Fu("scroll",e);break;case"onScrollEnd":null!=o&&Fu("scrollend",e);break;case"onClick":null!=o&&(e.onclick=Zu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Qe.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),"function"==typeof(s=null!=(s=e[ze]||null)?s[n]:null)&&e.removeEventListener(t,s,i),"function"!=typeof o)?n in e?e[n]=o:!0===o?e.setAttribute(n,""):rt(e,n,o):("function"!=typeof s&&null!==s&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,o,i)))}}function nd(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Fu("error",e),Fu("load",e);var o,i=!1,s=!1;for(o in n)if(n.hasOwnProperty(o)){var l=n[o];if(null!=l)switch(o){case"src":i=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ed(e,t,o,l,n,null)}}return s&&ed(e,t,"srcSet",n.srcSet,n,null),void(i&&ed(e,t,"src",n.src,n,null));case"input":Fu("invalid",e);var a=o=l=s=null,c=null,u=null;for(i in n)if(n.hasOwnProperty(i)){var d=n[i];if(null!=d)switch(i){case"name":s=d;break;case"type":l=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":o=d;break;case"defaultValue":a=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(r(137,t));break;default:ed(e,t,i,d,n,null)}}return bt(e,o,a,c,u,l,s,!1),void ht(e);case"select":for(s in Fu("invalid",e),i=l=o=null,n)if(n.hasOwnProperty(s)&&null!=(a=n[s]))switch(s){case"value":o=a;break;case"defaultValue":l=a;break;case"multiple":i=a;default:ed(e,t,s,a,n,null)}return t=o,n=l,e.multiple=!!i,void(null!=t?wt(e,!!i,t,!1):null!=n&&wt(e,!!i,n,!0));case"textarea":for(l in Fu("invalid",e),o=s=i=null,n)if(n.hasOwnProperty(l)&&null!=(a=n[l]))switch(l){case"value":i=a;break;case"defaultValue":s=a;break;case"children":o=a;break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(r(91));break;default:ed(e,t,l,a,n,null)}return xt(e,i,s,o),void ht(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(i=n[c]))if("selected"===c)e.selected=i&&"function"!=typeof i&&"symbol"!=typeof i;else ed(e,t,c,i,n,null);return;case"dialog":Fu("beforetoggle",e),Fu("toggle",e),Fu("cancel",e),Fu("close",e);break;case"iframe":case"object":Fu("load",e);break;case"video":case"audio":for(i=0;i<Iu.length;i++)Fu(Iu[i],e);break;case"image":Fu("error",e),Fu("load",e);break;case"details":Fu("toggle",e);break;case"embed":case"source":case"link":Fu("error",e),Fu("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(i=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ed(e,t,u,i,n,null)}return;default:if(Ot(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(i=n[d])&&td(e,t,d,i,n,void 0));return}}for(a in n)n.hasOwnProperty(a)&&(null!=(i=n[a])&&ed(e,t,a,i,n,null))}var rd=null,od=null;function id(e){return 9===e.nodeType?e:e.ownerDocument}function sd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ld(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function ad(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var cd=null;var ud="function"==typeof setTimeout?setTimeout:void 0,dd="function"==typeof clearTimeout?clearTimeout:void 0,fd="function"==typeof Promise?Promise:void 0,hd="function"==typeof queueMicrotask?queueMicrotask:void 0!==fd?function(e){return fd.resolve(null).then(e).catch(pd)}:ud;function pd(e){setTimeout((function(){throw e}))}function md(e){return"head"===e}function gd(e,t){var n=t,r=0,o=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0<r&&8>r){n=r;var s=e.ownerDocument;if(1&n&&xd(s.documentElement),2&n&&xd(s.body),4&n)for(xd(n=s.head),s=n.firstChild;s;){var l=s.nextSibling,a=s.nodeName;s[je]||"SCRIPT"===a||"STYLE"===a||"LINK"===a&&"stylesheet"===s.rel.toLowerCase()||n.removeChild(s),s=l}}if(0===o)return e.removeChild(i),void Af(t);o--}else"$"===n||"$?"===n||"$!"===n?o++:r=n.charCodeAt(0)-48;else r=0;n=i}while(n);Af(t)}function yd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":yd(n),He(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function vd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function bd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var kd=null;function wd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function Sd(e,t,n){switch(t=id(n),e){case"html":if(!(e=t.documentElement))throw Error(r(452));return e;case"head":if(!(e=t.head))throw Error(r(453));return e;case"body":if(!(e=t.body))throw Error(r(454));return e;default:throw Error(r(451))}}function xd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);He(e)}var Cd=new Map,Ed=new Set;function Md(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Td=_.d;_.d={f:function(){var e=Td.f(),t=Vc();return e||t},r:function(e){var t=Ue(e);null!==t&&5===t.tag&&"form"===t.type?zs(t):Td.r(e)},D:function(e){Td.D(e),Nd("dns-prefetch",e,null)},C:function(e,t){Td.C(e,t),Nd("preconnect",e,t)},L:function(e,t,n){Td.L(e,t,n);var r=Od;if(r&&e&&t){var o='link[rel="preload"][as="'+yt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(o+='[imagesrcset="'+yt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(o+='[imagesizes="'+yt(n.imageSizes)+'"]')):o+='[href="'+yt(e)+'"]';var i=o;switch(t){case"style":i=Dd(e);break;case"script":i=Ld(e)}Cd.has(i)||(e=c({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Cd.set(i,e),null!==r.querySelector(o)||"style"===t&&r.querySelector(Pd(i))||"script"===t&&r.querySelector(zd(i))||(nd(t=r.createElement("link"),"link",e),Ke(t),r.head.appendChild(t)))}},m:function(e,t){Td.m(e,t);var n=Od;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",o='link[rel="modulepreload"][as="'+yt(r)+'"][href="'+yt(e)+'"]',i=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Ld(e)}if(!Cd.has(i)&&(e=c({rel:"modulepreload",href:e},t),Cd.set(i,e),null===n.querySelector(o))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(zd(i)))return}nd(r=n.createElement("link"),"link",e),Ke(r),n.head.appendChild(r)}}},X:function(e,t){Td.X(e,t);var n=Od;if(n&&e){var r=qe(n).hoistableScripts,o=Ld(e),i=r.get(o);i||((i=n.querySelector(zd(o)))||(e=c({src:e,async:!0},t),(t=Cd.get(o))&&Fd(e,t),Ke(i=n.createElement("script")),nd(i,"link",e),n.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},r.set(o,i))}},S:function(e,t,n){Td.S(e,t,n);var r=Od;if(r&&e){var o=qe(r).hoistableStyles,i=Dd(e);t=t||"default";var s=o.get(i);if(!s){var l={loading:0,preload:null};if(s=r.querySelector(Pd(i)))l.loading=5;else{e=c({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Cd.get(i))&&$d(e,n);var a=s=r.createElement("link");Ke(a),nd(a,"link",e),a._p=new Promise((function(e,t){a.onload=e,a.onerror=t})),a.addEventListener("load",(function(){l.loading|=1})),a.addEventListener("error",(function(){l.loading|=2})),l.loading|=4,_d(s,t,r)}s={type:"stylesheet",instance:s,count:1,state:l},o.set(i,s)}}},M:function(e,t){Td.M(e,t);var n=Od;if(n&&e){var r=qe(n).hoistableScripts,o=Ld(e),i=r.get(o);i||((i=n.querySelector(zd(o)))||(e=c({src:e,async:!0,type:"module"},t),(t=Cd.get(o))&&Fd(e,t),Ke(i=n.createElement("script")),nd(i,"link",e),n.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},r.set(o,i))}}};var Od="undefined"==typeof document?null:document;function Nd(e,t,n){var r=Od;if(r&&"string"==typeof t&&t){var o=yt(t);o='link[rel="'+e+'"][href="'+o+'"]',"string"==typeof n&&(o+='[crossorigin="'+n+'"]'),Ed.has(o)||(Ed.add(o),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(o)&&(nd(t=r.createElement("link"),"link",e),Ke(t),r.head.appendChild(t)))}}function Ad(e,t,n,o){var i,s,l,a,c=(c=q.current)?Md(c):null;if(!c)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Dd(n.href),(o=(n=qe(c).hoistableStyles).get(t))||(o={type:"style",instance:null,count:0,state:null},n.set(t,o)),o):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Dd(n.href);var u=qe(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(Pd(e)))&&!u._p&&(d.instance=u,d.state.loading=5),Cd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Cd.set(e,n),u||(i=c,s=e,l=n,a=d.state,i.querySelector('link[rel="preload"][as="style"]['+s+"]")?a.loading=1:(s=i.createElement("link"),a.preload=s,s.addEventListener("load",(function(){return a.loading|=1})),s.addEventListener("error",(function(){return a.loading|=2})),nd(s,"link",l),Ke(s),i.head.appendChild(s))))),t&&null===o)throw Error(r(528,""));return d}if(t&&null!==o)throw Error(r(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Ld(n),(o=(n=qe(c).hoistableScripts).get(t))||(o={type:"script",instance:null,count:0,state:null},n.set(t,o)),o):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function Dd(e){return'href="'+yt(e)+'"'}function Pd(e){return'link[rel="stylesheet"]['+e+"]"}function Rd(e){return c({},e,{"data-precedence":e.precedence,precedence:null})}function Ld(e){return'[src="'+yt(e)+'"]'}function zd(e){return"script[async]"+e}function Id(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var o=e.querySelector('style[data-href~="'+yt(n.href)+'"]');if(o)return t.instance=o,Ke(o),o;var i=c({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ke(o=(e.ownerDocument||e).createElement("style")),nd(o,"style",i),_d(o,n.precedence,e),t.instance=o;case"stylesheet":i=Dd(n.href);var s=e.querySelector(Pd(i));if(s)return t.state.loading|=4,t.instance=s,Ke(s),s;o=Rd(n),(i=Cd.get(i))&&$d(o,i),Ke(s=(e.ownerDocument||e).createElement("link"));var l=s;return l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),nd(s,"link",o),t.state.loading|=4,_d(s,n.precedence,e),t.instance=s;case"script":return s=Ld(n.src),(i=e.querySelector(zd(s)))?(t.instance=i,Ke(i),i):(o=n,(i=Cd.get(s))&&Fd(o=c({},n),i),Ke(i=(e=e.ownerDocument||e).createElement("script")),nd(i,"link",o),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(r(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(o=t.instance,t.state.loading|=4,_d(o,n.precedence,e));return t.instance}function _d(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,i=o,s=0;s<r.length;s++){var l=r[s];if(l.dataset.precedence===t)i=l;else if(i!==o)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function $d(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Fd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Bd=null;function jd(e,t,n){if(null===Bd){var r=new Map,o=Bd=new Map;o.set(n,r)}else(r=(o=Bd).get(n))||(r=new Map,o.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),o=0;o<n.length;o++){var i=n[o];if(!(i[je]||i[Le]||"link"===e&&"stylesheet"===i.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==i.namespaceURI){var s=i.getAttribute(t)||"";s=e+s;var l=r.get(s);l?l.push(i):r.set(s,[i])}}return r}function Hd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Vd(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Ud=null;function Wd(){}function qd(){if(this.count--,0===this.count)if(this.stylesheets)Jd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Kd=null;function Jd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Kd=new Map,t.forEach(Qd,e),Kd=null,qd.call(e))}function Qd(e,t){if(!(4&t.state.loading)){var n=Kd.get(e);if(n)var r=n.get(null);else{n=new Map,Kd.set(e,n);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<o.length;i++){var s=o[i];"LINK"!==s.nodeName&&"not all"===s.getAttribute("media")||(n.set(s.dataset.precedence,s),r=s)}r&&n.set(null,r)}s=(o=t.instance).getAttribute("data-precedence"),(i=n.get(s)||r)===r&&n.set(null,o),n.set(s,o),this.count++,r=qd.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),i?i.parentNode.insertBefore(o,i.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(o,e.firstChild),t.state.loading|=4}}var Gd={$$typeof:k,Provider:null,Consumer:null,_currentValue:$,_currentValue2:$,_threadCount:0};function Yd(e,t,n,r,o,i,s,l){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Me(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Me(0),this.hiddenUpdates=Me(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=i,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=l,this.incompleteTransitions=new Map}function Xd(e,t,n,r,o,i,s,l,a,c,u,d){return e=new Yd(e,t,n,s,l,a,c,d),t=1,!0===i&&(t|=24),i=$r(3,null,null,t),e.current=i,i.stateNode=e,(t=Io()).refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:r,isDehydrated:n,cache:t},ii(i),e}function Zd(e){return e?e=Ir:Ir}function ef(e,t,n,r,o,i){o=Zd(o),null===r.context?r.context=o:r.pendingContext=o,(r=li(t)).payload={element:n},null!==(i=void 0===i?null:i)&&(r.callback=i),null!==(n=ai(e,r,t))&&($c(n,0,t),ci(n,e,t))}function tf(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function nf(e,t){tf(e,t),(e=e.alternate)&&tf(e,t)}function rf(e){if(13===e.tag){var t=Rr(e,67108864);null!==t&&$c(t,0,67108864),nf(e,67108864)}}var of=!0;function sf(e,t,n,r){var o=I.T;I.T=null;var i=_.p;try{_.p=2,af(e,t,n,r)}finally{_.p=i,I.T=o}}function lf(e,t,n,r){var o=I.T;I.T=null;var i=_.p;try{_.p=8,af(e,t,n,r)}finally{_.p=i,I.T=o}}function af(e,t,n,r){if(of){var o=cf(r);if(null===o)Uu(e,t,r,uf,n),wf(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return pf=Sf(pf,e,t,n,r,o),!0;case"dragenter":return mf=Sf(mf,e,t,n,r,o),!0;case"mouseover":return gf=Sf(gf,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return yf.set(i,Sf(yf.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,vf.set(i,Sf(vf.get(i)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(wf(e,r),4&t&&-1<kf.indexOf(e)){for(;null!==o;){var i=Ue(o);if(null!==i)switch(i.tag){case 3:if((i=i.stateNode).current.memoizedState.isDehydrated){var s=ke(i.pendingLanes);if(0!==s){var l=i;for(l.pendingLanes|=2,l.entangledLanes|=2;s;){var a=1<<31-me(s);l.entanglements[1]|=a,s&=~a}Cu(i),!(6&oc)&&(Cc=re()+500,Eu(0))}}break;case 13:null!==(l=Rr(i,2))&&$c(l,0,2),Vc(),nf(i,2)}if(null===(i=cf(r))&&Uu(e,t,r,uf,n),i===o)break;o=i}null!==o&&r.stopPropagation()}else Uu(e,t,r,null,n)}}function cf(e){return df(e=Rt(e))}var uf=null;function df(e){if(uf=null,null!==(e=Ve(e))){var t=i(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=s(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return uf=e,null}function ff(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(oe()){case ie:return 2;case se:return 8;case le:case ae:return 32;case ce:return 268435456;default:return 32}default:return 32}}var hf=!1,pf=null,mf=null,gf=null,yf=new Map,vf=new Map,bf=[],kf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wf(e,t){switch(e){case"focusin":case"focusout":pf=null;break;case"dragenter":case"dragleave":mf=null;break;case"mouseover":case"mouseout":gf=null;break;case"pointerover":case"pointerout":yf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":vf.delete(t.pointerId)}}function Sf(e,t,n,r,o,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},null!==t&&(null!==(t=Ue(t))&&rf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function xf(e){var t=Ve(e.target);if(null!==t){var n=i(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=s(n)))return e.blockedOn=t,void function(e,t){var n=_.p;try{return _.p=e,t()}finally{_.p=n}}(e.priority,(function(){if(13===n.tag){var e=Ic();e=Ae(e);var t=Rr(n,e);null!==t&&$c(t,0,e),nf(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Cf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=cf(e.nativeEvent);if(null!==n)return null!==(t=Ue(n))&&rf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Pt=r,n.target.dispatchEvent(r),Pt=null,t.shift()}return!0}function Ef(e,t,n){Cf(e)&&n.delete(t)}function Mf(){hf=!1,null!==pf&&Cf(pf)&&(pf=null),null!==mf&&Cf(mf)&&(mf=null),null!==gf&&Cf(gf)&&(gf=null),yf.forEach(Ef),vf.forEach(Ef)}function Tf(t,n){t.blockedOn===n&&(t.blockedOn=null,hf||(hf=!0,e.unstable_scheduleCallback(e.unstable_NormalPriority,Mf)))}var Of=null;function Nf(t){Of!==t&&(Of=t,e.unstable_scheduleCallback(e.unstable_NormalPriority,(function(){Of===t&&(Of=null);for(var e=0;e<t.length;e+=3){var n=t[e],r=t[e+1],o=t[e+2];if("function"!=typeof r){if(null===df(r||n))continue;break}var i=Ue(n);null!==i&&(t.splice(e,3),e-=3,Rs(i,{pending:!0,data:o,method:n.method,action:r},r,o))}})))}function Af(e){function t(t){return Tf(t,e)}null!==pf&&Tf(pf,e),null!==mf&&Tf(mf,e),null!==gf&&Tf(gf,e),yf.forEach(t),vf.forEach(t);for(var n=0;n<bf.length;n++){var r=bf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<bf.length&&null===(n=bf[0]).blockedOn;)xf(n),null===n.blockedOn&&bf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var o=n[r],i=n[r+1],s=o[ze]||null;if("function"==typeof i)s||Nf(n);else if(s){var l=null;if(i&&i.hasAttribute("formAction")){if(o=i,s=i[ze]||null)l=s.formAction;else if(null!==df(o))continue}else l=s.action;"function"==typeof l?n[r+1]=l:(n.splice(r,3),r-=3),Nf(n)}}}function Df(e){this._internalRoot=e}function Pf(e){this._internalRoot=e}Pf.prototype.render=Df.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(r(409));ef(t.current,Ic(),e,t,null,null)},Pf.prototype.unmount=Df.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;ef(e.current,2,null,e,null,null),Vc(),t[Ie]=null}},Pf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pe();e={blockedOn:null,target:e,priority:t};for(var n=0;n<bf.length&&0!==t&&t<bf[n].priority;n++);bf.splice(n,0,e),0===n&&xf(e)}};var Rf=t.version;if("19.1.0"!==Rf)throw Error(r(527,Rf,"19.1.0"));_.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(r(188));throw e=Object.keys(e).join(","),Error(r(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=i(e)))throw Error(r(188));return t!==e?null:e}for(var n=e,o=t;;){var s=n.return;if(null===s)break;var a=s.alternate;if(null===a){if(null!==(o=s.return)){n=o;continue}break}if(s.child===a.child){for(a=s.child;a;){if(a===n)return l(s),e;if(a===o)return l(s),t;a=a.sibling}throw Error(r(188))}if(n.return!==o.return)n=s,o=a;else{for(var c=!1,u=s.child;u;){if(u===n){c=!0,n=s,o=a;break}if(u===o){c=!0,o=s,n=a;break}u=u.sibling}if(!c){for(u=a.child;u;){if(u===n){c=!0,n=a,o=s;break}if(u===o){c=!0,o=a,n=s;break}u=u.sibling}if(!c)throw Error(r(189))}}if(n.alternate!==o)throw Error(r(190))}if(3!==n.tag)throw Error(r(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?a(e):null)?null:e.stateNode};var Lf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:I,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var zf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!zf.isDisabled&&zf.supportsFiber)try{fe=zf.inject(Lf),he=zf}catch(_f){}}return b.createRoot=function(e,t){if(!o(e))throw Error(r(299));var n=!1,i="",s=kl,l=wl,a=Sl;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(i=t.identifierPrefix),void 0!==t.onUncaughtError&&(s=t.onUncaughtError),void 0!==t.onCaughtError&&(l=t.onCaughtError),void 0!==t.onRecoverableError&&(a=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Xd(e,1,!1,null,0,n,i,s,l,a,0,null),e[Ie]=t.current,Hu(e),new Df(t)},b.hydrateRoot=function(e,t,n){if(!o(e))throw Error(r(299));var i=!1,s="",l=kl,a=wl,c=Sl,u=null;return null!=n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(s=n.identifierPrefix),void 0!==n.onUncaughtError&&(l=n.onUncaughtError),void 0!==n.onCaughtError&&(a=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Xd(e,1,!0,t,0,i,s,l,a,c,0,u)).context=Zd(null),n=t.current,(s=li(i=Ae(i=Ic()))).callback=null,ai(n,s,i),n=i,t.current.lanes=n,Te(t,n),Cu(t),e[Ie]=t.current,Hu(e),new Pf(t)},b.version="19.1.0",b}var P=(M||(M=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),v.exports=D()),v.exports);function R(e){this.content=e}function L(e,t,n){for(let r=0;;r++){if(r==e.childCount||r==t.childCount)return e.childCount==t.childCount?null:n;let o=e.child(r),i=t.child(r);if(o!=i){if(!o.sameMarkup(i))return n;if(o.isText&&o.text!=i.text){for(let e=0;o.text[e]==i.text[e];e++)n++;return n}if(o.content.size||i.content.size){let e=L(o.content,i.content,n+1);if(null!=e)return e}n+=o.nodeSize}else n+=o.nodeSize}}function z(e,t,n,r){for(let o=e.childCount,i=t.childCount;;){if(0==o||0==i)return o==i?null:{a:n,b:r};let s=e.child(--o),l=t.child(--i),a=s.nodeSize;if(s!=l){if(!s.sameMarkup(l))return{a:n,b:r};if(s.isText&&s.text!=l.text){let e=0,t=Math.min(s.text.length,l.text.length);for(;e<t&&s.text[s.text.length-e-1]==l.text[l.text.length-e-1];)e++,n--,r--;return{a:n,b:r}}if(s.content.size||l.content.size){let e=z(s.content,l.content,n-1,r-1);if(e)return e}n-=a,r-=a}else n-=a,r-=a}}R.prototype={constructor:R,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return-1},get:function(e){var t=this.find(e);return-1==t?void 0:this.content[t+1]},update:function(e,t,n){var r=n&&n!=e?this.remove(n):this,o=r.find(e),i=r.content.slice();return-1==o?i.push(n||e,t):(i[o+1]=t,n&&(i[o]=n)),new R(i)},remove:function(e){var t=this.find(e);if(-1==t)return this;var n=this.content.slice();return n.splice(t,2),new R(n)},addToStart:function(e,t){return new R([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var n=this.remove(e).content.slice();return n.push(e,t),new R(n)},addBefore:function(e,t,n){var r=this.remove(t),o=r.content.slice(),i=r.find(e);return o.splice(-1==i?o.length:i,0,t,n),new R(o)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return(e=R.from(e)).size?new R(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=R.from(e)).size?new R(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=R.from(e);for(var n=0;n<e.content.length;n+=2)t=t.remove(e.content[n]);return t},toObject:function(){var e={};return this.forEach((function(t,n){e[t]=n})),e},get size(){return this.content.length>>1}},R.from=function(e){if(e instanceof R)return e;var t=[];if(e)for(var n in e)t.push(n,e[n]);return new R(t)};class I{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let n=0;n<e.length;n++)this.size+=e[n].nodeSize}nodesBetween(e,t,n,r=0,o){for(let i=0,s=0;s<t;i++){let l=this.content[i],a=s+l.nodeSize;if(a>e&&!1!==n(l,r+s,o||null,i)&&l.content.size){let o=s+1;l.nodesBetween(Math.max(0,e-o),Math.min(l.content.size,t-o),n,r+o)}s=a}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,r){let o="",i=!0;return this.nodesBetween(e,t,((s,l)=>{let a=s.isText?s.text.slice(Math.max(e,l)-l,t-l):s.isLeaf?r?"function"==typeof r?r(s):r:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&a||s.isTextblock)&&n&&(i?i=!1:o+=n),o+=a}),0),o}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,r=this.content.slice(),o=0;for(t.isText&&t.sameMarkup(n)&&(r[r.length-1]=t.withText(t.text+n.text),o=1);o<e.content.length;o++)r.push(e.content[o]);return new I(r,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],r=0;if(t>e)for(let o=0,i=0;i<t;o++){let s=this.content[o],l=i+s.nodeSize;l>e&&((i<e||l>t)&&(s=s.isText?s.cut(Math.max(0,e-i),Math.min(s.text.length,t-i)):s.cut(Math.max(0,e-i-1),Math.min(s.content.size,t-i-1))),n.push(s),r+=s.nodeSize),i=l}return new I(n,r)}cutByIndex(e,t){return e==t?I.empty:0==e&&t==this.content.length?this:new I(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let r=this.content.slice(),o=this.size+t.nodeSize-n.nodeSize;return r[e]=t,new I(r,o)}addToStart(e){return new I([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new I(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let r=this.content[t];e(r,n,t),n+=r.nodeSize}}findDiffStart(e,t=0){return L(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return z(this,e,t,n)}findIndex(e,t=-1){if(0==e)return $(0,e);if(e==this.size)return $(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,r=0;;n++){let o=r+this.child(n).nodeSize;if(o>=e)return o==e||t>0?$(n+1,o):$(n,r);r=o}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map((e=>e.toJSON())):null}static fromJSON(e,t){if(!t)return I.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new I(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return I.empty;let t,n=0;for(let r=0;r<e.length;r++){let o=e[r];n+=o.nodeSize,r&&o.isText&&e[r-1].sameMarkup(o)?(t||(t=e.slice(0,r)),t[t.length-1]=o.withText(t[t.length-1].text+o.text)):t&&t.push(o)}return new I(t||e,n)}static from(e){if(!e)return I.empty;if(e instanceof I)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new I([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}I.empty=new I([],0);const _={index:0,offset:0};function $(e,t){return _.index=e,_.offset=t,_}function F(e,t){if(e===t)return!0;if(!e||"object"!=typeof e||!t||"object"!=typeof t)return!1;let n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!F(e[n],t[n]))return!1}else{for(let n in e)if(!(n in t)||!F(e[n],t[n]))return!1;for(let n in t)if(!(n in e))return!1}return!0}let B=class e{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let r=0;r<e.length;r++){let o=e[r];if(this.eq(o))return e;if(this.type.excludes(o.type))t||(t=e.slice(0,r));else{if(o.type.excludes(this.type))return e;!n&&o.type.rank>this.type.rank&&(t||(t=e.slice(0,r)),t.push(this),n=!0),t&&t.push(o)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&F(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw new RangeError(`There is no mark type ${t.type} in this schema`);let r=n.create(t.attrs);return n.checkAttrs(r.attrs),r}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&0==t.length)return e.none;if(t instanceof e)return[t];let n=t.slice();return n.sort(((e,t)=>e.type.rank-t.type.rank)),n}};B.none=[];class j extends Error{}class H{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=U(this.content,e+this.openStart,t);return n&&new H(n,this.openStart,this.openEnd)}removeBetween(e,t){return new H(V(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return H.empty;let n=t.openStart||0,r=t.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw new RangeError("Invalid input for Slice.fromJSON");return new H(I.fromJSON(e,t.content),n,r)}static maxOpen(e,t=!0){let n=0,r=0;for(let o=e.firstChild;o&&!o.isLeaf&&(t||!o.type.spec.isolating);o=o.firstChild)n++;for(let o=e.lastChild;o&&!o.isLeaf&&(t||!o.type.spec.isolating);o=o.lastChild)r++;return new H(e,n,r)}}function V(e,t,n){let{index:r,offset:o}=e.findIndex(t),i=e.maybeChild(r),{index:s,offset:l}=e.findIndex(n);if(o==t||i.isText){if(l!=n&&!e.child(s).isText)throw new RangeError("Removing non-flat range");return e.cut(0,t).append(e.cut(n))}if(r!=s)throw new RangeError("Removing non-flat range");return e.replaceChild(r,i.copy(V(i.content,t-o-1,n-o-1)))}function U(e,t,n,r){let{index:o,offset:i}=e.findIndex(t),s=e.maybeChild(o);if(i==t||s.isText)return e.cut(0,t).append(n).append(e.cut(t));let l=U(s.content,t-i-1,n);return l&&e.replaceChild(o,s.copy(l))}function W(e,t,n){if(n.openStart>e.depth)throw new j("Inserted content deeper than insertion position");if(e.depth-n.openStart!=t.depth-n.openEnd)throw new j("Inconsistent open depths");return q(e,t,n,0)}function q(e,t,n,r){let o=e.index(r),i=e.node(r);if(o==t.index(r)&&r<e.depth-n.openStart){let s=q(e,t,n,r+1);return i.copy(i.content.replaceChild(o,s))}if(n.content.size){if(n.openStart||n.openEnd||e.depth!=r||t.depth!=r){let{start:o,end:s}=function(e,t){let n=t.depth-e.openStart,r=t.node(n).copy(e.content);for(let o=n-1;o>=0;o--)r=t.node(o).copy(I.from(r));return{start:r.resolveNoCache(e.openStart+n),end:r.resolveNoCache(r.content.size-e.openEnd-n)}}(n,e);return Y(i,X(e,o,s,t,r))}{let r=e.parent,o=r.content;return Y(r,o.cut(0,e.parentOffset).append(n.content).append(o.cut(t.parentOffset)))}}return Y(i,Z(e,t,r))}function K(e,t){if(!t.type.compatibleContent(e.type))throw new j("Cannot join "+t.type.name+" onto "+e.type.name)}function J(e,t,n){let r=e.node(n);return K(r,t.node(n)),r}function Q(e,t){let n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function G(e,t,n,r){let o=(t||e).node(n),i=0,s=t?t.index(n):o.childCount;e&&(i=e.index(n),e.depth>n?i++:e.textOffset&&(Q(e.nodeAfter,r),i++));for(let l=i;l<s;l++)Q(o.child(l),r);t&&t.depth==n&&t.textOffset&&Q(t.nodeBefore,r)}function Y(e,t){return e.type.checkContent(t),e.copy(t)}function X(e,t,n,r,o){let i=e.depth>o&&J(e,t,o+1),s=r.depth>o&&J(n,r,o+1),l=[];return G(null,e,o,l),i&&s&&t.index(o)==n.index(o)?(K(i,s),Q(Y(i,X(e,t,n,r,o+1)),l)):(i&&Q(Y(i,Z(e,t,o+1)),l),G(t,n,o,l),s&&Q(Y(s,Z(n,r,o+1)),l)),G(r,null,o,l),new I(l)}function Z(e,t,n){let r=[];if(G(null,e,n,r),e.depth>n){Q(Y(J(e,t,n+1),Z(e,t,n+1)),r)}return G(t,null,n,r),new I(r)}H.empty=new H(I.empty,0,0);class ee{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],r=0==t?0:this.path[3*t-1]+1;for(let o=0;o<e;o++)r+=n.child(o).nodeSize;return r}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return B.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){let e=n;n=r,r=e}let o=n.marks;for(var i=0;i<o.length;i++)!1!==o[i].type.spec.inclusive||r&&o[i].isInSet(r.marks)||(o=o[i--].removeFromSet(o));return o}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,r=e.parent.maybeChild(e.index());for(var o=0;o<n.length;o++)!1!==n[o].type.spec.inclusive||r&&n[o].isInSet(r.marks)||(n=n[o--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new oe(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let n=[],r=0,o=t;for(let i=e;;){let{index:e,offset:t}=i.content.findIndex(o),s=o-t;if(n.push(i,e,r+t),!s)break;if(i=i.child(e),i.isText)break;o=s-1,r+=t+1}return new ee(t,n,o)}static resolveCached(e,t){let n=re.get(e);if(n)for(let o=0;o<n.elts.length;o++){let e=n.elts[o];if(e.pos==t)return e}else re.set(e,n=new te);let r=n.elts[n.i]=ee.resolve(e,t);return n.i=(n.i+1)%ne,r}}class te{constructor(){this.elts=[],this.i=0}}const ne=12,re=new WeakMap;class oe{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const ie=Object.create(null);let se=class e{constructor(e,t,n,r=B.none){this.type=e,this.attrs=t,this.marks=r,this.content=n||I.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,r=0){this.content.nodesBetween(e,t,n,r,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,r){return this.content.textBetween(e,t,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&F(this.attrs,t||e.defaultAttrs||ie)&&B.sameSet(this.marks,n||B.none)}copy(t=null){return t==this.content?this:new e(this.type,this.attrs,t,this.marks)}mark(t){return t==this.marks?this:new e(this.type,this.attrs,this.content,t)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return H.empty;let r=this.resolve(e),o=this.resolve(t),i=n?0:r.sharedDepth(t),s=r.start(i),l=r.node(i).content.cut(r.pos-s,o.pos-s);return new H(l,r.depth-i,o.depth-i)}replace(e,t,n){return W(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:r}=t.content.findIndex(e);if(t=t.maybeChild(n),!t)return null;if(r==e||t.isText)return t;e-=r+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let r=this.content.child(t-1);return{node:r,index:t-1,offset:n-r.nodeSize}}resolve(e){return ee.resolveCached(this,e)}resolveNoCache(e){return ee.resolve(this,e)}rangeHasMark(e,t,n){let r=!1;return t>e&&this.nodesBetween(e,t,(e=>(n.isInSet(e.marks)&&(r=!0),!r))),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),ae(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=I.empty,r=0,o=n.childCount){let i=this.contentMatchAt(e).matchFragment(n,r,o),s=i&&i.matchFragment(this.content,t);if(!s||!s.validEnd)return!1;for(let l=r;l<o;l++)if(!this.type.allowsMarks(n.child(l).marks))return!1;return!0}canReplaceWith(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;let o=this.contentMatchAt(e).matchType(n),i=o&&o.matchFragment(this.content,t);return!!i&&i.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=B.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!B.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map((e=>e.type.name))}`);this.content.forEach((e=>e.check()))}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map((e=>e.toJSON()))),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let n;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw new RangeError("Invalid text node in JSON");return e.text(t.text,n)}let r=I.fromJSON(e,t.content),o=e.nodeType(t.type).create(t.attrs,r,n);return o.type.checkAttrs(o.attrs),o}};se.prototype.text=void 0;class le extends se{constructor(e,t,n,r){if(super(e,t,null,r),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):ae(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new le(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new le(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function ae(e,t){for(let n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}class ce{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let n=new ue(e,t);if(null==n.next)return ce.empty;let r=de(n);n.next&&n.err("Unexpected trailing text");let o=function(e){let t=Object.create(null);return n(ye(e,0));function n(r){let o=[];r.forEach((t=>{e[t].forEach((({term:t,to:n})=>{if(!t)return;let r;for(let e=0;e<o.length;e++)o[e][0]==t&&(r=o[e][1]);ye(e,n).forEach((e=>{r||o.push([t,r=[]]),-1==r.indexOf(e)&&r.push(e)}))}))}));let i=t[r.join(",")]=new ce(r.indexOf(e.length-1)>-1);for(let e=0;e<o.length;e++){let r=o[e][1].sort(ge);i.next.push({type:o[e][0],next:t[r.join(",")]||n(r)})}return i}}(function(e){let t=[[]];return o(i(e,0),n()),t;function n(){return t.push([])-1}function r(e,n,r){let o={term:r,to:n};return t[e].push(o),o}function o(e,t){e.forEach((e=>e.to=t))}function i(e,t){if("choice"==e.type)return e.exprs.reduce(((e,n)=>e.concat(i(n,t))),[]);if("seq"!=e.type){if("star"==e.type){let s=n();return r(t,s),o(i(e.expr,s),s),[r(s)]}if("plus"==e.type){let s=n();return o(i(e.expr,t),s),o(i(e.expr,s),s),[r(s)]}if("opt"==e.type)return[r(t)].concat(i(e.expr,t));if("range"==e.type){let s=t;for(let t=0;t<e.min;t++){let t=n();o(i(e.expr,s),t),s=t}if(-1==e.max)o(i(e.expr,s),s);else for(let t=e.min;t<e.max;t++){let t=n();r(s,t),o(i(e.expr,s),t),s=t}return[r(s)]}if("name"==e.type)return[r(t,void 0,e.value)];throw new Error("Unknown expr type")}for(let r=0;;r++){let s=i(e.exprs[r],t);if(r==e.exprs.length-1)return s;o(s,t=n())}}}(r));return function(e,t){for(let n=0,r=[e];n<r.length;n++){let e=r[n],o=!e.validEnd,i=[];for(let t=0;t<e.next.length;t++){let{type:n,next:s}=e.next[t];i.push(n.name),!o||n.isText||n.hasRequiredAttrs()||(o=!1),-1==r.indexOf(s)&&r.push(s)}o&&t.err("Only non-generatable nodes ("+i.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(o,n),o}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let r=this;for(let o=t;r&&o<n;o++)r=r.matchType(e.child(o).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!t.isText&&!t.hasRequiredAttrs())return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let r=[this];return function o(i,s){let l=i.matchFragment(e,n);if(l&&(!t||l.validEnd))return I.from(s.map((e=>e.createAndFill())));for(let e=0;e<i.next.length;e++){let{type:t,next:n}=i.next[e];if(!t.isText&&!t.hasRequiredAttrs()&&-1==r.indexOf(n)){r.push(n);let e=o(n,s.concat(t));if(e)return e}}return null}(this,[])}findWrapping(e){for(let n=0;n<this.wrapCache.length;n+=2)if(this.wrapCache[n]==e)return this.wrapCache[n+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),o=r.match;if(o.matchType(e)){let e=[];for(let t=r;t.type;t=t.via)e.push(t.type);return e.reverse()}for(let e=0;e<o.next.length;e++){let{type:i,next:s}=o.next[e];i.isLeaf||i.hasRequiredAttrs()||i.name in t||r.type&&!s.validEnd||(n.push({match:i.contentMatch,type:i,via:r}),t[i.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return function t(n){e.push(n);for(let r=0;r<n.next.length;r++)-1==e.indexOf(n.next[r].next)&&t(n.next[r].next)}(this),e.map(((t,n)=>{let r=n+(t.validEnd?"*":" ")+" ";for(let o=0;o<t.next.length;o++)r+=(o?", ":"")+t.next[o].type.name+"->"+e.indexOf(t.next[o].next);return r})).join("\n")}}ce.empty=new ce(!0);class ue{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function de(e){let t=[];do{t.push(fe(e))}while(e.eat("|"));return 1==t.length?t[0]:{type:"choice",exprs:t}}function fe(e){let t=[];do{t.push(he(e))}while(e.next&&")"!=e.next&&"|"!=e.next);return 1==t.length?t[0]:{type:"seq",exprs:t}}function he(e){let t=function(e){if(e.eat("(")){let t=de(e);return e.eat(")")||e.err("Missing closing paren"),t}if(!/\W/.test(e.next)){let t=function(e,t){let n=e.nodeTypes,r=n[t];if(r)return[r];let o=[];for(let i in n){let e=n[i];e.isInGroup(t)&&o.push(e)}0==o.length&&e.err("No node type or group '"+t+"' found");return o}(e,e.next).map((t=>(null==e.inline?e.inline=t.isInline:e.inline!=t.isInline&&e.err("Mixing inline and block content"),{type:"name",value:t})));return e.pos++,1==t.length?t[0]:{type:"choice",exprs:t}}e.err("Unexpected token '"+e.next+"'")}(e);for(;;)if(e.eat("+"))t={type:"plus",expr:t};else if(e.eat("*"))t={type:"star",expr:t};else if(e.eat("?"))t={type:"opt",expr:t};else{if(!e.eat("{"))break;t=me(e,t)}return t}function pe(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");let t=Number(e.next);return e.pos++,t}function me(e,t){let n=pe(e),r=n;return e.eat(",")&&(r="}"!=e.next?pe(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:t}}function ge(e,t){return t-e}function ye(e,t){let n=[];return function t(r){let o=e[r];if(1==o.length&&!o[0].term)return t(o[0].to);n.push(r);for(let e=0;e<o.length;e++){let{term:r,to:i}=o[e];r||-1!=n.indexOf(i)||t(i)}}(t),n.sort(ge)}function ve(e){let t=Object.create(null);for(let n in e){let r=e[n];if(!r.hasDefault)return null;t[n]=r.default}return t}function be(e,t){let n=Object.create(null);for(let r in e){let o=t&&t[r];if(void 0===o){let t=e[r];if(!t.hasDefault)throw new RangeError("No value supplied for attribute "+r);o=t.default}n[r]=o}return n}function ke(e,t,n,r){for(let o in t)if(!(o in e))throw new RangeError(`Unsupported attribute ${o} for ${n} of type ${o}`);for(let o in e){let n=e[o];n.validate&&n.validate(t[o])}}function we(e,t){let n=Object.create(null);if(t)for(let r in t)n[r]=new xe(e,r,t[r]);return n}let Se=class e{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=we(e,n.attrs),this.defaultAttrs=ve(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==ce.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:be(this.attrs,e)}create(e=null,t,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new se(this,this.computeAttrs(e),I.from(t),B.setFrom(n))}createChecked(e=null,t,n){return t=I.from(t),this.checkContent(t),new se(this,this.computeAttrs(e),t,B.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=I.from(t)).size){let e=this.contentMatch.fillBefore(t);if(!e)return null;t=e.append(t)}let r=this.contentMatch.matchFragment(t),o=r&&r.fillBefore(I.empty,!0);return o?new se(this,e,t.append(o),B.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let n=0;n<e.childCount;n++)if(!this.allowsMarks(e.child(n).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){ke(this.attrs,e,"node",this.name)}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(null==this.markSet)return e;let t;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:B.none:e}static compile(t,n){let r=Object.create(null);t.forEach(((t,o)=>r[t]=new e(t,n,o)));let o=n.spec.topNode||"doc";if(!r[o])throw new RangeError("Schema is missing its top node type ('"+o+"')");if(!r.text)throw new RangeError("Every schema needs a 'text' type");for(let e in r.text.attrs)throw new RangeError("The text node type should not have attributes");return r}};class xe{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(e,t,n){let r=n.split("|");return n=>{let o=null===n?"null":typeof n;if(r.indexOf(o)<0)throw new RangeError(`Expected value of type ${r} for attribute ${t} on type ${e}, got ${o}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class Ce{constructor(e,t,n,r){this.name=e,this.rank=t,this.schema=n,this.spec=r,this.attrs=we(e,r.attrs),this.excluded=null;let o=ve(this.attrs);this.instance=o?new B(this,o):null}create(e=null){return!e&&this.instance?this.instance:new B(this,be(this.attrs,e))}static compile(e,t){let n=Object.create(null),r=0;return e.forEach(((e,o)=>n[e]=new Ce(e,r++,t,o))),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){ke(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class Ee{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let r in e)t[r]=e[r];t.nodes=R.from(e.nodes),t.marks=R.from(e.marks||{}),this.nodes=Se.compile(this.spec.nodes,this),this.marks=Ce.compile(this.spec.marks,this);let n=Object.create(null);for(let r in this.nodes){if(r in this.marks)throw new RangeError(r+" can not be both a node and a mark");let e=this.nodes[r],t=e.spec.content||"",o=e.spec.marks;if(e.contentMatch=n[t]||(n[t]=ce.parse(t,this.nodes)),e.inlineContent=e.contentMatch.inlineContent,e.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!e.isInline||!e.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=e}e.markSet="_"==o?null:o?Me(this,o.split(" ")):""!=o&&e.inlineContent?null:[]}for(let r in this.marks){let e=this.marks[r],t=e.spec.excludes;e.excluded=null==t?[e]:""==t?[]:Me(this,t.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,r){if("string"==typeof e)e=this.nodeType(e);else{if(!(e instanceof Se))throw new RangeError("Invalid node type: "+e);if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}return e.createChecked(t,n,r)}text(e,t){let n=this.nodes.text;return new le(n,n.defaultAttrs,e,B.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return se.fromJSON(this,e)}markFromJSON(e){return B.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function Me(e,t){let n=[];for(let r=0;r<t.length;r++){let o=t[r],i=e.marks[o],s=i;if(i)n.push(i);else for(let t in e.marks){let r=e.marks[t];("_"==o||r.spec.group&&r.spec.group.split(" ").indexOf(o)>-1)&&n.push(s=r)}if(!s)throw new SyntaxError("Unknown mark type: '"+t[r]+"'")}return n}class Te{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach((e=>{if(function(e){return null!=e.tag}(e))this.tags.push(e);else if(function(e){return null!=e.style}(e)){let t=/[^=]*/.exec(e.style)[0];n.indexOf(t)<0&&n.push(t),this.styles.push(e)}})),this.normalizeLists=!this.tags.some((t=>{if(!/^(ul|ol)\b/.test(t.tag)||!t.node)return!1;let n=e.nodes[t.node];return n.contentMatch.matchType(n)}))}parse(e,t={}){let n=new Re(this,t,!1);return n.addAll(e,B.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new Re(this,t,!0);return n.addAll(e,B.none,t.from,t.to),H.maxOpen(n.finish())}matchTag(e,t,n){for(let r=n?this.tags.indexOf(n)+1:0;r<this.tags.length;r++){let n=this.tags[r];if(Le(e,n.tag)&&(void 0===n.namespace||e.namespaceURI==n.namespace)&&(!n.context||t.matchesContext(n.context))){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}matchStyle(e,t,n,r){for(let o=r?this.styles.indexOf(r)+1:0;o<this.styles.length;o++){let r=this.styles[o],i=r.style;if(!(0!=i.indexOf(e)||r.context&&!n.matchesContext(r.context)||i.length>e.length&&(61!=i.charCodeAt(e.length)||i.slice(e.length+1)!=t))){if(r.getAttrs){let e=r.getAttrs(t);if(!1===e)continue;r.attrs=e||void 0}return r}}}static schemaRules(e){let t=[];function n(e){let n=null==e.priority?50:e.priority,r=0;for(;r<t.length;r++){let e=t[r];if((null==e.priority?50:e.priority)<n)break}t.splice(r,0,e)}for(let r in e.marks){let t=e.marks[r].spec.parseDOM;t&&t.forEach((e=>{n(e=ze(e)),e.mark||e.ignore||e.clearMark||(e.mark=r)}))}for(let r in e.nodes){let t=e.nodes[r].spec.parseDOM;t&&t.forEach((e=>{n(e=ze(e)),e.node||e.ignore||e.mark||(e.node=r)}))}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new Te(e,Te.schemaRules(e)))}}const Oe={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Ne={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},Ae={ol:!0,ul:!0};function De(e,t,n){return null!=t?(t?1:0)|("full"===t?2:0):e&&"pre"==e.whitespace?3:-5&n}class Pe{constructor(e,t,n,r,o,i){this.type=e,this.attrs=t,this.marks=n,this.solid=r,this.options=i,this.content=[],this.activeMarks=B.none,this.match=o||(4&i?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(I.from(e));if(!t){let t,n=this.type.contentMatch;return(t=n.findWrapping(e.type))?(this.match=n,t):null}this.match=this.type.contentMatch.matchFragment(t)}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let e,t=this.content[this.content.length-1];if(t&&t.isText&&(e=/[ \t\r\n\u000c]+$/.exec(t.text))){let n=t;t.text.length==e[0].length?this.content.pop():this.content[this.content.length-1]=n.withText(n.text.slice(0,n.text.length-e[0].length))}}let t=I.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(I.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Oe.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class Re{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r,o=t.topNode,i=De(null,t.preserveWhitespace,0)|(n?4:0);r=o?new Pe(o.type,o.attrs,B.none,!0,t.topMatch||o.type.contentMatch,i):new Pe(n?null:e.schema.topNodeType,null,B.none,!0,null,i),this.nodes=[r],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,r=this.top,o=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===o||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(o)n="full"!==o?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let t=r.content[r.content.length-1],o=e.previousSibling;(!t||o&&"BR"==o.nodeName||t.isText&&/[ \t\r\n\u000c]$/.test(t.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t,!/\S/.test(n)),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let r=this.localPreserveWS,o=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let i,s=e.nodeName.toLowerCase();Ae.hasOwnProperty(s)&&this.parser.normalizeLists&&function(e){for(let t=e.firstChild,n=null;t;t=t.nextSibling){let e=1==t.nodeType?t.nodeName.toLowerCase():null;e&&Ae.hasOwnProperty(e)&&n?(n.appendChild(t),t=n):"li"==e?n=t:e&&(n=null)}}(e);let l=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(i=this.parser.matchTag(e,this,n));e:if(l?l.ignore:Ne.hasOwnProperty(s))this.findInside(e),this.ignoreFallback(e,t);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(e=l.skip);let n,r=this.needsBlock;if(Oe.hasOwnProperty(s))o.content.length&&o.content[0].isInline&&this.open&&(this.open--,o=this.top),n=!0,o.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let i=l&&l.skip?t:this.readStyles(e,t);i&&this.addAll(e,i),n&&this.sync(o),this.needsBlock=r}else{let n=this.readStyles(e,t);n&&this.addElementByRule(e,l,n,!1===l.consuming?i:void 0)}this.localPreserveWS=r}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let r=0;r<this.parser.matchedStyles.length;r++){let e=this.parser.matchedStyles[r],o=n.getPropertyValue(e);if(o)for(let n;;){let r=this.parser.matchStyle(e,o,this,n);if(!r)break;if(r.ignore)return null;if(t=r.clearMark?t.filter((e=>!r.clearMark(e))):t.concat(this.parser.schema.marks[r.mark].create(r.attrs)),!1!==r.consuming)break;n=r}}return t}addElementByRule(e,t,n,r){let o,i;if(t.node)if(i=this.parser.schema.nodes[t.node],i.isLeaf)this.insertNode(i.create(t.attrs),n,"BR"==e.nodeName)||this.leafFallback(e,n);else{let e=this.enter(i,t.attrs||null,n,t.preserveWhitespace);e&&(o=!0,n=e)}else{let e=this.parser.schema.marks[t.mark];n=n.concat(e.create(t.attrs))}let s=this.top;if(i&&i.isLeaf)this.findInside(e);else if(r)this.addElement(e,n,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach((e=>this.insertNode(e,n,!1)));else{let r=e;"string"==typeof t.contentElement?r=e.querySelector(t.contentElement):"function"==typeof t.contentElement?r=t.contentElement(e):t.contentElement&&(r=t.contentElement),this.findAround(e,r,!0),this.addAll(r,n),this.findAround(e,r,!1)}o&&this.sync(s)&&this.open--}addAll(e,t,n,r){let o=n||0;for(let i=n?e.childNodes[n]:e.firstChild,s=null==r?null:e.childNodes[r];i!=s;i=i.nextSibling,++o)this.findAtPoint(e,o),this.addDOM(i,t);this.findAtPoint(e,o)}findPlace(e,t,n){let r,o;for(let i=this.open,s=0;i>=0;i--){let t=this.nodes[i],l=t.findWrapping(e);if(l&&(!r||r.length>l.length+s)&&(r=l,o=t,!l.length))break;if(t.solid){if(n)break;s+=2}}if(!r)return null;this.sync(o);for(let i=0;i<r.length;i++)t=this.enterInner(r[i],null,t,!1);return t}insertNode(e,t,n){if(e.isInline&&this.needsBlock&&!this.top.type){let e=this.textblockFromContext();e&&(t=this.enterInner(e,null,t))}let r=this.findPlace(e,t,n);if(r){this.closeExtra();let t=this.top;t.match&&(t.match=t.match.matchType(e.type));let n=B.none;for(let o of r.concat(e.marks))(t.type?t.type.allowsMarkType(o.type):Ie(o.type,e.type))&&(n=o.addToSet(n));return t.content.push(e.mark(n)),!0}return!1}enter(e,t,n,r){let o=this.findPlace(e.create(t),n,!1);return o&&(o=this.enterInner(e,t,n,!0,r)),o}enterInner(e,t,n,r=!1,o){this.closeExtra();let i=this.top;i.match=i.match&&i.match.matchType(e);let s=De(e,o,i.options);4&i.options&&0==i.content.length&&(s|=4);let l=B.none;return n=n.filter((t=>!(i.type?i.type.allowsMarkType(t.type):Ie(t.type,e))||(l=t.addToSet(l),!1))),this.nodes.push(new Pe(e,t,l,r,null,s)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!(!this.isOpen&&!this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=1)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let t=n.length-1;t>=0;t--)e+=n[t].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let r=0;r<this.find.length;r++)if(null==this.find[r].pos&&1==e.nodeType&&e.contains(this.find[r].node)){t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,r=!(this.isOpen||n&&n.parent.type!=this.nodes[0].type),o=-(n?n.depth+1:0)+(r?0:1),i=(e,s)=>{for(;e>=0;e--){let l=t[e];if(""==l){if(e==t.length-1||0==e)continue;for(;s>=o;s--)if(i(e-1,s))return!0;return!1}{let e=s>0||0==s&&r?this.nodes[s].type:n&&s>=o?n.node(s-o).type:null;if(!e||e.name!=l&&!e.isInGroup(l))return!1;s--}}return!0};return i(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let e=this.parser.schema.nodes[t];if(e.isTextblock&&e.defaultAttrs)return e}}}function Le(e,t){return(e.matches||e.msMatchesSelector||e.webkitMatchesSelector||e.mozMatchesSelector).call(e,t)}function ze(e){let t={};for(let n in e)t[n]=e[n];return t}function Ie(e,t){let n=t.schema.nodes;for(let r in n){let o=n[r];if(!o.allowsMarkType(e))continue;let i=[],s=e=>{i.push(e);for(let n=0;n<e.edgeCount;n++){let{type:r,next:o}=e.edge(n);if(r==t)return!0;if(i.indexOf(o)<0&&s(o))return!0}};if(s(o.contentMatch))return!0}}class _e{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=Fe(t).createDocumentFragment());let r=n,o=[];return e.forEach((e=>{if(o.length||e.marks.length){let n=0,i=0;for(;n<o.length&&i<e.marks.length;){let t=e.marks[i];if(this.marks[t.type.name]){if(!t.eq(o[n][0])||!1===t.type.spec.spanning)break;n++,i++}else i++}for(;n<o.length;)r=o.pop()[1];for(;i<e.marks.length;){let n=e.marks[i++],s=this.serializeMark(n,e.isInline,t);s&&(o.push([n,r]),r.appendChild(s.dom),r=s.contentDOM||s.dom)}}r.appendChild(this.serializeNodeInner(e,t))})),n}serializeNodeInner(e,t){let{dom:n,contentDOM:r}=He(Fe(t),this.nodes[e.type.name](e),null,e.attrs);if(r){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,r)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let r=e.marks.length-1;r>=0;r--){let o=this.serializeMark(e.marks[r],e.isInline,t);o&&((o.contentDOM||o.dom).appendChild(n),n=o.dom)}return n}serializeMark(e,t,n={}){let r=this.marks[e.type.name];return r&&He(Fe(n),r(e,t),null,e.attrs)}static renderSpec(e,t,n=null,r){return He(e,t,n,r)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new _e(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=$e(e.nodes);return t.text||(t.text=e=>e.text),t}static marksFromSchema(e){return $e(e.marks)}}function $e(e){let t={};for(let n in e){let r=e[n].spec.toDOM;r&&(t[n]=r)}return t}function Fe(e){return e.document||window.document}const Be=new WeakMap;function je(e){let t=Be.get(e);return void 0===t&&Be.set(e,t=function(e){let t=null;function n(e){if(e&&"object"==typeof e)if(Array.isArray(e))if("string"==typeof e[0])t||(t=[]),t.push(e);else for(let t=0;t<e.length;t++)n(e[t]);else for(let t in e)n(e[t])}return n(e),t}(e)),t}function He(e,t,n,r){if("string"==typeof t)return{dom:e.createTextNode(t)};if(null!=t.nodeType)return{dom:t};if(t.dom&&null!=t.dom.nodeType)return t;let o,i=t[0];if("string"!=typeof i)throw new RangeError("Invalid array passed to renderSpec");if(r&&(o=je(r))&&o.indexOf(t)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s,l=i.indexOf(" ");l>0&&(n=i.slice(0,l),i=i.slice(l+1));let a=n?e.createElementNS(n,i):e.createElement(i),c=t[1],u=1;if(c&&"object"==typeof c&&null==c.nodeType&&!Array.isArray(c)){u=2;for(let e in c)if(null!=c[e]){let t=e.indexOf(" ");t>0?a.setAttributeNS(e.slice(0,t),e.slice(t+1),c[e]):a.setAttribute(e,c[e])}}for(let d=u;d<t.length;d++){let o=t[d];if(0===o){if(d<t.length-1||d>u)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}{let{dom:t,contentDOM:i}=He(e,o,n,r);if(a.appendChild(t),i){if(s)throw new RangeError("Multiple content holes");s=i}}}return{dom:a,contentDOM:s}}const Ve=Math.pow(2,16);function Ue(e){return 65535&e}class We{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class qe{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&qe.empty)return qe.empty}recover(e){let t=0,n=Ue(e);if(!this.inverted)for(let r=0;r<n;r++)t+=this.ranges[3*r+2]-this.ranges[3*r+1];return this.ranges[3*n]+t+function(e){return(e-(65535&e))/Ve}(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let r=0,o=this.inverted?2:1,i=this.inverted?1:2;for(let s=0;s<this.ranges.length;s+=3){let l=this.ranges[s]-(this.inverted?r:0);if(l>e)break;let a=this.ranges[s+o],c=this.ranges[s+i],u=l+a;if(e<=u){let o=l+r+((a?e==l?-1:e==u?1:t:t)<0?0:c);if(n)return o;let i=e==(t<0?l:u)?null:s/3+(e-l)*Ve,d=e==l?2:e==u?1:4;return(t<0?e!=l:e!=u)&&(d|=8),new We(o,d,i)}r+=c-a}return n?e+r:new We(e+r,0,null)}touches(e,t){let n=0,r=Ue(t),o=this.inverted?2:1,i=this.inverted?1:2;for(let s=0;s<this.ranges.length;s+=3){let t=this.ranges[s]-(this.inverted?n:0);if(t>e)break;let l=this.ranges[s+o];if(e<=t+l&&s==3*r)return!0;n+=this.ranges[s+i]-l}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,o=0;r<this.ranges.length;r+=3){let i=this.ranges[r],s=i-(this.inverted?o:0),l=i+(this.inverted?0:o),a=this.ranges[r+t],c=this.ranges[r+n];e(s,s+a,l,l+c),o+=c-a}}invert(){return new qe(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?qe.empty:new qe(e<0?[0,-e,0]:[0,0,e])}}qe.empty=new qe([]);class Ke{constructor(e,t,n=0,r=(e?e.length:0)){this.mirror=t,this.from=n,this.to=r,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new Ke(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let r=e.getMirror(t);this.appendMap(e._maps[t],null!=r&&r<t?n+r:void 0)}}getMirror(e){if(this.mirror)for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let r=e.getMirror(t);this.appendMap(e._maps[t].invert(),null!=r&&r>t?n-r-1:void 0)}}invert(){let e=new Ke;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let r=0;for(let o=this.from;o<this.to;o++){let n=this._maps[o].mapResult(e,t);if(null!=n.recover){let t=this.getMirror(o);if(null!=t&&t>o&&t<this.to){o=t,e=this._maps[t].recover(n.recover);continue}}r|=n.delInfo,e=n.pos}return n?e:new We(e,r,null)}}const Je=Object.create(null);class Qe{getMap(){return qe.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=Je[t.stepType];if(!n)throw new RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in Je)throw new RangeError("Duplicate use of step JSON ID "+e);return Je[e]=t,t.prototype.jsonID=e,t}}class Ge{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new Ge(e,null)}static fail(e){return new Ge(null,e)}static fromReplace(e,t,n,r){try{return Ge.ok(e.replace(t,n,r))}catch(o){if(o instanceof j)return Ge.fail(o.message);throw o}}}function Ye(e,t,n){let r=[];for(let o=0;o<e.childCount;o++){let i=e.child(o);i.content.size&&(i=i.copy(Ye(i.content,t,i))),i.isInline&&(i=t(i,n,o)),r.push(i)}return I.fromArray(r)}class Xe extends Qe{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),r=n.node(n.sharedDepth(this.to)),o=new H(Ye(t.content,((e,t)=>e.isAtom&&t.type.allowsMarkType(this.mark.type)?e.mark(this.mark.addToSet(e.marks)):e),r),t.openStart,t.openEnd);return Ge.fromReplace(e,this.from,this.to,o)}invert(){return new Ze(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new Xe(t.pos,n.pos,this.mark)}merge(e){return e instanceof Xe&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Xe(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new Xe(t.from,t.to,e.markFromJSON(t.mark))}}Qe.jsonID("addMark",Xe);class Ze extends Qe{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new H(Ye(t.content,(e=>e.mark(this.mark.removeFromSet(e.marks))),e),t.openStart,t.openEnd);return Ge.fromReplace(e,this.from,this.to,n)}invert(){return new Xe(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new Ze(t.pos,n.pos,this.mark)}merge(e){return e instanceof Ze&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Ze(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new Ze(t.from,t.to,e.markFromJSON(t.mark))}}Qe.jsonID("removeMark",Ze);class et extends Qe{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return Ge.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return Ge.fromReplace(e,this.pos,this.pos+1,new H(I.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let e=this.mark.addToSet(t.marks);if(e.length==t.marks.length){for(let n=0;n<t.marks.length;n++)if(!t.marks[n].isInSet(e))return new et(this.pos,t.marks[n]);return new et(this.pos,this.mark)}}return new tt(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new et(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new et(t.pos,e.markFromJSON(t.mark))}}Qe.jsonID("addNodeMark",et);class tt extends Qe{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return Ge.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return Ge.fromReplace(e,this.pos,this.pos+1,new H(I.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new et(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new tt(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new tt(t.pos,e.markFromJSON(t.mark))}}Qe.jsonID("removeNodeMark",tt);class nt extends Qe{constructor(e,t,n,r=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=r}apply(e){return this.structure&&ot(e,this.from,this.to)?Ge.fail("Structure replace would overwrite content"):Ge.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new qe([this.from,this.to-this.from,this.slice.size])}invert(e){return new nt(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new nt(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof nt)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart){if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;{let t=this.slice.size+e.slice.size==0?H.empty:new H(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new nt(e.from,this.to,t,this.structure)}}{let t=this.slice.size+e.slice.size==0?H.empty:new H(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new nt(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new nt(t.from,t.to,H.fromJSON(e,t.slice),!!t.structure)}}Qe.jsonID("replace",nt);class rt extends Qe{constructor(e,t,n,r,o,i,s=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=r,this.slice=o,this.insert=i,this.structure=s}apply(e){if(this.structure&&(ot(e,this.from,this.gapFrom)||ot(e,this.gapTo,this.to)))return Ge.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return Ge.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?Ge.fromReplace(e,this.from,this.to,n):Ge.fail("Content does not fit in gap")}getMap(){return new qe([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new rt(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),r=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),o=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||r<t.pos||o>n.pos?null:new rt(t.pos,n.pos,r,o,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new rt(t.from,t.to,t.gapFrom,t.gapTo,H.fromJSON(e,t.slice),t.insert,!!t.structure)}}function ot(e,t,n){let r=e.resolve(t),o=n-t,i=r.depth;for(;o>0&&i>0&&r.indexAfter(i)==r.node(i).childCount;)i--,o--;if(o>0){let e=r.node(i).maybeChild(r.indexAfter(i));for(;o>0;){if(!e||e.isLeaf)return!0;e=e.firstChild,o--}}return!1}function it(e,t,n,r=n.contentMatch,o=!0){let i=e.doc.nodeAt(t),s=[],l=t+1;for(let a=0;a<i.childCount;a++){let t=i.child(a),c=l+t.nodeSize,u=r.matchType(t.type);if(u){r=u;for(let r=0;r<t.marks.length;r++)n.allowsMarkType(t.marks[r].type)||e.step(new Ze(l,c,t.marks[r]));if(o&&t.isText&&"pre"!=n.whitespace){let e,r,o=/\r?\n|\r/g;for(;e=o.exec(t.text);)r||(r=new H(I.from(n.schema.text(" ",n.allowedMarks(t.marks))),0,0)),s.push(new nt(l+e.index,l+e.index+e[0].length,r))}}else s.push(new nt(l,c,H.empty));l=c}if(!r.validEnd){let t=r.fillBefore(I.empty,!0);e.replace(l,l,new H(t,0,0))}for(let a=s.length-1;a>=0;a--)e.step(s[a])}function st(e,t,n){return(0==t||e.canReplace(t,e.childCount))&&(n==e.childCount||e.canReplace(0,n))}function lt(e){let t=e.parent.content.cutByIndex(e.startIndex,e.endIndex);for(let n=e.depth;;--n){let r=e.$from.node(n),o=e.$from.index(n),i=e.$to.indexAfter(n);if(n<e.depth&&r.canReplace(o,i,t))return n;if(0==n||r.type.spec.isolating||!st(r,o,i))break}return null}function at(e,t,n=null,r=e){let o=function(e,t){let{parent:n,startIndex:r,endIndex:o}=e,i=n.contentMatchAt(r).findWrapping(t);if(!i)return null;let s=i.length?i[0]:t;return n.canReplaceWith(r,o,s)?i:null}(e,t),i=o&&function(e,t){let{parent:n,startIndex:r,endIndex:o}=e,i=n.child(r),s=t.contentMatch.findWrapping(i.type);if(!s)return null;let l=(s.length?s[s.length-1]:t).contentMatch;for(let a=r;l&&a<o;a++)l=l.matchType(n.child(a).type);return l&&l.validEnd?s:null}(r,t);return i?o.map(ct).concat({type:t,attrs:n}).concat(i.map(ct)):null}function ct(e){return{type:e,attrs:null}}function ut(e,t,n,r){t.forEach(((o,i)=>{if(o.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(o.text);){let o=e.mapping.slice(r).map(n+1+i+s.index);e.replaceWith(o,o+1,t.type.schema.linebreakReplacement.create())}}}))}function dt(e,t,n,r){t.forEach(((o,i)=>{if(o.type==o.type.schema.linebreakReplacement){let o=e.mapping.slice(r).map(n+1+i);e.replaceWith(o,o+1,t.type.schema.text("\n"))}}))}function ft(e,t,n=1,r){let o=e.resolve(t),i=o.depth-n,s=r&&r[r.length-1]||o.parent;if(i<0||o.parent.type.spec.isolating||!o.parent.canReplace(o.index(),o.parent.childCount)||!s.type.validContent(o.parent.content.cutByIndex(o.index(),o.parent.childCount)))return!1;for(let c=o.depth-1,u=n-2;c>i;c--,u--){let e=o.node(c),t=o.index(c);if(e.type.spec.isolating)return!1;let n=e.content.cutByIndex(t,e.childCount),i=r&&r[u+1];i&&(n=n.replaceChild(0,i.type.create(i.attrs)));let s=r&&r[u]||e;if(!e.canReplace(t+1,e.childCount)||!s.type.validContent(n))return!1}let l=o.indexAfter(i),a=r&&r[0];return o.node(i).canReplaceWith(l,l,a?a.type:o.node(i+1).type)}function ht(e,t){let n=e.resolve(t),r=n.index();return pt(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function pt(e,t){return!(!e||!t||e.isLeaf||!function(e,t){t.content.size||e.type.compatibleContent(t.type);let n=e.contentMatchAt(e.childCount),{linebreakReplacement:r}=e.type.schema;for(let o=0;o<t.childCount;o++){let i=t.child(o),s=i.type==r?e.type.schema.nodes.text:i.type;if(n=n.matchType(s),!n)return!1;if(!e.type.allowsMarks(i.marks))return!1}return n.validEnd}(e,t))}function mt(e,t,n=-1){let r=e.resolve(t);for(let o=r.depth;;o--){let e,i,s=r.index(o);if(o==r.depth?(e=r.nodeBefore,i=r.nodeAfter):n>0?(e=r.node(o+1),s++,i=r.node(o).maybeChild(s)):(e=r.node(o).maybeChild(s-1),i=r.node(o+1)),e&&!e.isTextblock&&pt(e,i)&&r.node(o).canReplace(s,s+1))return t;if(0==o)break;t=n<0?r.before(o):r.after(o)}}function gt(e,t,n){let r=e.resolve(t);if(!n.content.size)return t;let o=n.content;for(let i=0;i<n.openStart;i++)o=o.firstChild.content;for(let i=1;i<=(0==n.openStart&&n.size?2:1);i++)for(let e=r.depth;e>=0;e--){let t=e==r.depth?0:r.pos<=(r.start(e+1)+r.end(e+1))/2?-1:1,n=r.index(e)+(t>0?1:0),s=r.node(e),l=!1;if(1==i)l=s.canReplace(n,n,o);else{let e=s.contentMatchAt(n).findWrapping(o.firstChild.type);l=e&&s.canReplaceWith(n,n,e[0])}if(l)return 0==t?r.pos:t<0?r.before(e+1):r.after(e+1)}return null}function yt(e,t,n=t,r=H.empty){if(t==n&&!r.size)return null;let o=e.resolve(t),i=e.resolve(n);return vt(o,i,r)?new nt(t,n,r):new bt(o,i,r).fit()}function vt(e,t,n){return!n.openStart&&!n.openEnd&&e.start()==t.start()&&e.parent.canReplace(e.index(),t.index(),n.content)}Qe.jsonID("replaceAround",rt);class bt{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=I.empty;for(let r=0;r<=e.depth;r++){let t=e.node(r);this.frontier.push({type:t.type,match:t.contentMatchAt(e.indexAfter(r))})}for(let r=e.depth;r>0;r--)this.placed=I.from(e.node(r).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let e=this.findFittable();e?this.placeNodes(e):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,r=this.close(e<0?this.$to:n.doc.resolve(e));if(!r)return null;let o=this.placed,i=n.depth,s=r.depth;for(;i&&s&&1==o.childCount;)o=o.firstChild.content,i--,s--;let l=new H(o,i,s);return e>-1?new rt(n.pos,e,this.$to.pos,this.$to.end(),l,t):l.size||n.pos!=this.$to.pos?new nt(n.pos,r.pos,l):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<e;n++){let o=t.firstChild;if(t.childCount>1&&(r=0),o.type.spec.isolating&&r<=n){e=n;break}t=o.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let e,r=null;n?(r=St(this.unplaced.content,n-1).firstChild,e=r.content):e=this.unplaced.content;let o=e.firstChild;for(let i=this.depth;i>=0;i--){let e,{type:s,match:l}=this.frontier[i],a=null;if(1==t&&(o?l.matchType(o.type)||(a=l.fillBefore(I.from(o),!1)):r&&s.compatibleContent(r.type)))return{sliceDepth:n,frontierDepth:i,parent:r,inject:a};if(2==t&&o&&(e=l.findWrapping(o.type)))return{sliceDepth:n,frontierDepth:i,parent:r,wrap:e};if(r&&l.matchType(r.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=St(e,t);return!(!r.childCount||r.firstChild.isLeaf)&&(this.unplaced=new H(e,t+1,Math.max(n,r.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=St(e,t);if(r.childCount<=1&&t>0){let o=e.size-t<=t+r.size;this.unplaced=new H(kt(e,t-1,1),t-1,o?t-1:n)}else this.unplaced=new H(kt(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:r,wrap:o}){for(;this.depth>t;)this.closeFrontierNode();if(o)for(let p=0;p<o.length;p++)this.openFrontierNode(o[p]);let i=this.unplaced,s=n?n.content:i.content,l=i.openStart-e,a=0,c=[],{match:u,type:d}=this.frontier[t];if(r){for(let e=0;e<r.childCount;e++)c.push(r.child(e));u=u.matchFragment(r)}let f=s.size+e-(i.content.size-i.openEnd);for(;a<s.childCount;){let e=s.child(a),t=u.matchType(e.type);if(!t)break;a++,(a>1||0==l||e.content.size)&&(u=t,c.push(xt(e.mark(d.allowedMarks(e.marks)),1==a?l:0,a==s.childCount?f:-1)))}let h=a==s.childCount;h||(f=-1),this.placed=wt(this.placed,t,I.from(c)),this.frontier[t].match=u,h&&f<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let p=0,m=s;p<f;p++){let e=m.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),m=e.content}this.unplaced=h?0==e?H.empty:new H(kt(i.content,e-1,1),e-1,f<0?i.openEnd:e-1):new H(kt(i.content,e,a),i.openStart,i.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e,t=this.frontier[this.depth];if(!t.type.isTextblock||!Ct(this.$to,this.$to.depth,t.type,t.match,!1)||this.$to.depth==this.depth&&(e=this.findCloseLevel(this.$to))&&e.depth==this.depth)return-1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:r}=this.frontier[t],o=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),i=Ct(e,t,r,n,o);if(i){for(let n=t-1;n>=0;n--){let{match:t,type:r}=this.frontier[n],o=Ct(e,n,r,t,!0);if(!o||o.childCount)continue e}return{depth:t,fit:i,move:o?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=wt(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let t=e.node(n),r=t.type.contentMatch.fillBefore(t.content,!0,e.index(n));this.openFrontierNode(t.type,t.attrs,r)}return e}openFrontierNode(e,t=null,n){let r=this.frontier[this.depth];r.match=r.match.matchType(e),this.placed=wt(this.placed,this.depth,I.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(I.empty,!0);e.childCount&&(this.placed=wt(this.placed,this.frontier.length,e))}}function kt(e,t,n){return 0==t?e.cutByIndex(n,e.childCount):e.replaceChild(0,e.firstChild.copy(kt(e.firstChild.content,t-1,n)))}function wt(e,t,n){return 0==t?e.append(n):e.replaceChild(e.childCount-1,e.lastChild.copy(wt(e.lastChild.content,t-1,n)))}function St(e,t){for(let n=0;n<t;n++)e=e.firstChild.content;return e}function xt(e,t,n){if(t<=0)return e;let r=e.content;return t>1&&(r=r.replaceChild(0,xt(r.firstChild,t-1,1==r.childCount?n-1:0))),t>0&&(r=e.type.contentMatch.fillBefore(r).append(r),n<=0&&(r=r.append(e.type.contentMatch.matchFragment(r).fillBefore(I.empty,!0)))),e.copy(r)}function Ct(e,t,n,r,o){let i=e.node(t),s=o?e.indexAfter(t):e.index(t);if(s==i.childCount&&!n.compatibleContent(i.type))return null;let l=r.fillBefore(i.content,!0,s);return l&&!function(e,t,n){for(let r=n;r<t.childCount;r++)if(!e.allowsMarks(t.child(r).marks))return!0;return!1}(n,i.content,s)?l:null}function Et(e,t,n,r,o){if(t<n){let o=e.firstChild;e=e.replaceChild(0,o.copy(Et(o.content,t+1,n,r,o)))}if(t>r){let t=o.contentMatchAt(0),n=t.fillBefore(e).append(e);e=n.append(t.matchFragment(n).fillBefore(I.empty,!0))}return e}function Mt(e,t){let n=[];for(let r=Math.min(e.depth,t.depth);r>=0;r--){let o=e.start(r);if(o<e.pos-(e.depth-r)||t.end(r)>t.pos+(t.depth-r)||e.node(r).type.spec.isolating||t.node(r).type.spec.isolating)break;(o==t.start(r)||r==e.depth&&r==t.depth&&e.parent.inlineContent&&t.parent.inlineContent&&r&&t.start(r-1)==o-1)&&n.push(r)}return n}class Tt extends Qe{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return Ge.fail("No node at attribute step's position");let n=Object.create(null);for(let o in t.attrs)n[o]=t.attrs[o];n[this.attr]=this.value;let r=t.type.create(n,null,t.marks);return Ge.fromReplace(e,this.pos,this.pos+1,new H(I.from(r),0,t.isLeaf?0:1))}getMap(){return qe.empty}invert(e){return new Tt(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Tt(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw new RangeError("Invalid input for AttrStep.fromJSON");return new Tt(t.pos,t.attr,t.value)}}Qe.jsonID("attr",Tt);class Ot extends Qe{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let r in e.attrs)t[r]=e.attrs[r];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return Ge.ok(n)}getMap(){return qe.empty}invert(e){return new Ot(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new Ot(t.attr,t.value)}}Qe.jsonID("docAttr",Ot);let Nt=class extends Error{};Nt=function e(t){let n=Error.call(this,t);return n.__proto__=e.prototype,n},(Nt.prototype=Object.create(Error.prototype)).constructor=Nt,Nt.prototype.name="TransformError";class At{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new Ke}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new Nt(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=H.empty){let r=yt(this.doc,e,t,n);return r&&this.step(r),this}replaceWith(e,t,n){return this.replace(e,t,new H(I.from(n),0,0))}delete(e,t){return this.replace(e,t,H.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return function(e,t,n,r){if(!r.size)return e.deleteRange(t,n);let o=e.doc.resolve(t),i=e.doc.resolve(n);if(vt(o,i,r))return e.step(new nt(t,n,r));let s=Mt(o,e.doc.resolve(n));0==s[s.length-1]&&s.pop();let l=-(o.depth+1);s.unshift(l);for(let h=o.depth,p=o.pos-1;h>0;h--,p--){let e=o.node(h).type.spec;if(e.defining||e.definingAsContext||e.isolating)break;s.indexOf(h)>-1?l=h:o.before(h)==p&&s.splice(1,0,-h)}let a=s.indexOf(l),c=[],u=r.openStart;for(let h=r.content,p=0;;p++){let e=h.firstChild;if(c.push(e),p==r.openStart)break;h=e.content}for(let h=u-1;h>=0;h--){let e=c[h],t=(d=e.type).spec.defining||d.spec.definingForContent;if(t&&!e.sameMarkup(o.node(Math.abs(l)-1)))u=h;else if(t||!e.type.isTextblock)break}var d;for(let h=r.openStart;h>=0;h--){let t=(h+u+1)%(r.openStart+1),l=c[t];if(l)for(let c=0;c<s.length;c++){let u=s[(c+a)%s.length],d=!0;u<0&&(d=!1,u=-u);let f=o.node(u-1),h=o.index(u-1);if(f.canReplaceWith(h,h,l.type,l.marks))return e.replace(o.before(u),d?i.after(u):n,new H(Et(r.content,0,r.openStart,t),t,r.openEnd))}}let f=e.steps.length;for(let h=s.length-1;h>=0&&(e.replace(t,n,r),!(e.steps.length>f));h--){let e=s[h];e<0||(t=o.before(e),n=i.after(e))}}(this,e,t,n),this}replaceRangeWith(e,t,n){return function(e,t,n,r){if(!r.isInline&&t==n&&e.doc.resolve(t).parent.content.size){let o=function(e,t,n){let r=e.resolve(t);if(r.parent.canReplaceWith(r.index(),r.index(),n))return t;if(0==r.parentOffset)for(let o=r.depth-1;o>=0;o--){let e=r.index(o);if(r.node(o).canReplaceWith(e,e,n))return r.before(o+1);if(e>0)return null}if(r.parentOffset==r.parent.content.size)for(let o=r.depth-1;o>=0;o--){let e=r.indexAfter(o);if(r.node(o).canReplaceWith(e,e,n))return r.after(o+1);if(e<r.node(o).childCount)return null}return null}(e.doc,t,r.type);null!=o&&(t=n=o)}e.replaceRange(t,n,new H(I.from(r),0,0))}(this,e,t,n),this}deleteRange(e,t){return function(e,t,n){let r=e.doc.resolve(t),o=e.doc.resolve(n),i=Mt(r,o);for(let s=0;s<i.length;s++){let t=i[s],n=s==i.length-1;if(n&&0==t||r.node(t).type.contentMatch.validEnd)return e.delete(r.start(t),o.end(t));if(t>0&&(n||r.node(t-1).canReplace(r.index(t-1),o.indexAfter(t-1))))return e.delete(r.before(t),o.after(t))}for(let s=1;s<=r.depth&&s<=o.depth;s++)if(t-r.start(s)==r.depth-s&&n>r.end(s)&&o.end(s)-n!=o.depth-s&&r.start(s-1)==o.start(s-1)&&r.node(s-1).canReplace(r.index(s-1),o.index(s-1)))return e.delete(r.before(s),n);e.delete(t,n)}(this,e,t),this}lift(e,t){return function(e,t,n){let{$from:r,$to:o,depth:i}=t,s=r.before(i+1),l=o.after(i+1),a=s,c=l,u=I.empty,d=0;for(let p=i,m=!1;p>n;p--)m||r.index(p)>0?(m=!0,u=I.from(r.node(p).copy(u)),d++):a--;let f=I.empty,h=0;for(let p=i,m=!1;p>n;p--)m||o.after(p+1)<o.end(p)?(m=!0,f=I.from(o.node(p).copy(f)),h++):c++;e.step(new rt(a,c,s,l,new H(u.append(f),d,h),u.size-d,!0))}(this,e,t),this}join(e,t=1){return function(e,t,n){let r=null,{linebreakReplacement:o}=e.doc.type.schema,i=e.doc.resolve(t-n),s=i.node().type;if(o&&s.inlineContent){let e="pre"==s.whitespace,t=!!s.contentMatch.matchType(o);e&&!t?r=!1:!e&&t&&(r=!0)}let l=e.steps.length;if(!1===r){let r=e.doc.resolve(t+n);dt(e,r.node(),r.before(),l)}s.inlineContent&&it(e,t+n-1,s,i.node().contentMatchAt(i.index()),null==r);let a=e.mapping.slice(l),c=a.map(t-n);if(e.step(new nt(c,a.map(t+n,-1),H.empty,!0)),!0===r){let t=e.doc.resolve(c);ut(e,t.node(),t.before(),e.steps.length)}}(this,e,t),this}wrap(e,t){return function(e,t,n){let r=I.empty;for(let s=n.length-1;s>=0;s--){if(r.size){let e=n[s].type.contentMatch.matchFragment(r);if(!e||!e.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=I.from(n[s].type.create(n[s].attrs,r))}let o=t.start,i=t.end;e.step(new rt(o,i,o,i,new H(r,0,0),n.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,r=null){return function(e,t,n,r,o){if(!r.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let i=e.steps.length;e.doc.nodesBetween(t,n,((t,n)=>{let s="function"==typeof o?o(t):o;if(t.isTextblock&&!t.hasMarkup(r,s)&&function(e,t,n){let r=e.resolve(t),o=r.index();return r.parent.canReplaceWith(o,o+1,n)}(e.doc,e.mapping.slice(i).map(n),r)){let o=null;if(r.schema.linebreakReplacement){let e="pre"==r.whitespace,t=!!r.contentMatch.matchType(r.schema.linebreakReplacement);e&&!t?o=!1:!e&&t&&(o=!0)}!1===o&&dt(e,t,n,i),it(e,e.mapping.slice(i).map(n,1),r,void 0,null===o);let l=e.mapping.slice(i),a=l.map(n,1),c=l.map(n+t.nodeSize,1);return e.step(new rt(a,c,a+1,c-1,new H(I.from(r.create(s,null,t.marks)),0,0),1,!0)),!0===o&&ut(e,t,n,i),!1}}))}(this,e,t,n,r),this}setNodeMarkup(e,t,n=null,r){return function(e,t,n,r,o){let i=e.doc.nodeAt(t);if(!i)throw new RangeError("No node at given position");n||(n=i.type);let s=n.create(r,null,o||i.marks);if(i.isLeaf)return e.replaceWith(t,t+i.nodeSize,s);if(!n.validContent(i.content))throw new RangeError("Invalid content for node type "+n.name);e.step(new rt(t,t+i.nodeSize,t+1,t+i.nodeSize-1,new H(I.from(s),0,0),1,!0))}(this,e,t,n,r),this}setNodeAttribute(e,t,n){return this.step(new Tt(e,t,n)),this}setDocAttribute(e,t){return this.step(new Ot(e,t)),this}addNodeMark(e,t){return this.step(new et(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw new RangeError("No node at position "+e);if(t instanceof B)t.isInSet(n.marks)&&this.step(new tt(e,t));else{let r,o=n.marks,i=[];for(;r=t.isInSet(o);)i.push(new tt(e,r)),o=r.removeFromSet(o);for(let e=i.length-1;e>=0;e--)this.step(i[e])}return this}split(e,t=1,n){return function(e,t,n=1,r){let o=e.doc.resolve(t),i=I.empty,s=I.empty;for(let l=o.depth,a=o.depth-n,c=n-1;l>a;l--,c--){i=I.from(o.node(l).copy(i));let e=r&&r[c];s=I.from(e?e.type.create(e.attrs,s):o.node(l).copy(s))}e.step(new nt(t,t,new H(i.append(s),n,n),!0))}(this,e,t,n),this}addMark(e,t,n){return function(e,t,n,r){let o,i,s=[],l=[];e.doc.nodesBetween(t,n,((e,a,c)=>{if(!e.isInline)return;let u=e.marks;if(!r.isInSet(u)&&c.type.allowsMarkType(r.type)){let c=Math.max(a,t),d=Math.min(a+e.nodeSize,n),f=r.addToSet(u);for(let e=0;e<u.length;e++)u[e].isInSet(f)||(o&&o.to==c&&o.mark.eq(u[e])?o.to=d:s.push(o=new Ze(c,d,u[e])));i&&i.to==c?i.to=d:l.push(i=new Xe(c,d,r))}})),s.forEach((t=>e.step(t))),l.forEach((t=>e.step(t)))}(this,e,t,n),this}removeMark(e,t,n){return function(e,t,n,r){let o=[],i=0;e.doc.nodesBetween(t,n,((e,s)=>{if(!e.isInline)return;i++;let l=null;if(r instanceof Ce){let t,n=e.marks;for(;t=r.isInSet(n);)(l||(l=[])).push(t),n=t.removeFromSet(n)}else r?r.isInSet(e.marks)&&(l=[r]):l=e.marks;if(l&&l.length){let r=Math.min(s+e.nodeSize,n);for(let e=0;e<l.length;e++){let n,a=l[e];for(let e=0;e<o.length;e++){let t=o[e];t.step==i-1&&a.eq(o[e].style)&&(n=t)}n?(n.to=r,n.step=i):o.push({style:a,from:Math.max(s,t),to:r,step:i})}}})),o.forEach((t=>e.step(new Ze(t.from,t.to,t.style))))}(this,e,t,n),this}clearIncompatible(e,t,n){return it(this,e,t,n),this}}const Dt=Object.create(null);class Pt{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new Rt(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=H.empty){let n=t.content.lastChild,r=null;for(let s=0;s<t.openEnd;s++)r=n,n=n.lastChild;let o=e.steps.length,i=this.ranges;for(let s=0;s<i.length;s++){let{$from:l,$to:a}=i[s],c=e.mapping.slice(o);e.replaceRange(c.map(l.pos),c.map(a.pos),s?H.empty:t),0==s&&Vt(e,o,(n?n.isInline:r&&r.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,r=this.ranges;for(let o=0;o<r.length;o++){let{$from:i,$to:s}=r[o],l=e.mapping.slice(n),a=l.map(i.pos),c=l.map(s.pos);o?e.deleteRange(a,c):(e.replaceRangeWith(a,c,t),Vt(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let r=e.parent.inlineContent?new It(e):Ht(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(let o=e.depth-1;o>=0;o--){let r=t<0?Ht(e.node(0),e.node(o),e.before(o+1),e.index(o),t,n):Ht(e.node(0),e.node(o),e.after(o+1),e.index(o)+1,t,n);if(r)return r}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new Bt(e.node(0))}static atStart(e){return Ht(e,e,0,0,1)||new Bt(e)}static atEnd(e){return Ht(e,e,e.content.size,e.childCount,-1)||new Bt(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=Dt[t.type];if(!n)throw new RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in Dt)throw new RangeError("Duplicate use of selection JSON ID "+e);return Dt[e]=t,t.prototype.jsonID=e,t}getBookmark(){return It.between(this.$anchor,this.$head).getBookmark()}}Pt.prototype.visible=!0;class Rt{constructor(e,t){this.$from=e,this.$to=t}}let Lt=!1;function zt(e){Lt||e.parent.inlineContent||(Lt=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}class It extends Pt{constructor(e,t=e){zt(e),zt(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return Pt.near(n);let r=e.resolve(t.map(this.anchor));return new It(r.parent.inlineContent?r:n,n)}replace(e,t=H.empty){if(super.replace(e,t),t==H.empty){let t=this.$from.marksAcross(this.$to);t&&e.ensureMarks(t)}}eq(e){return e instanceof It&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new _t(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid input for TextSelection.fromJSON");return new It(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}static between(e,t,n){let r=e.pos-t.pos;if(n&&!r||(n=r>=0?1:-1),!t.parent.inlineContent){let e=Pt.findFrom(t,n,!0)||Pt.findFrom(t,-n,!0);if(!e)return Pt.near(t,n);t=e.$head}return e.parent.inlineContent||(0==r||(e=(Pt.findFrom(e,-n,!0)||Pt.findFrom(e,n,!0)).$anchor).pos<t.pos!=r<0)&&(e=t),new It(e,t)}}Pt.jsonID("text",It);class _t{constructor(e,t){this.anchor=e,this.head=t}map(e){return new _t(e.map(this.anchor),e.map(this.head))}resolve(e){return It.between(e.resolve(this.anchor),e.resolve(this.head))}}class $t extends Pt{constructor(e){let t=e.nodeAfter,n=e.node(0).resolve(e.pos+t.nodeSize);super(e,n),this.node=t}map(e,t){let{deleted:n,pos:r}=t.mapResult(this.anchor),o=e.resolve(r);return n?Pt.near(o):new $t(o)}content(){return new H(I.from(this.node),0,0)}eq(e){return e instanceof $t&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new Ft(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw new RangeError("Invalid input for NodeSelection.fromJSON");return new $t(e.resolve(t.anchor))}static create(e,t){return new $t(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}$t.prototype.visible=!1,Pt.jsonID("node",$t);class Ft{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new _t(n,n):new Ft(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&$t.isSelectable(n)?new $t(t):Pt.near(t)}}class Bt extends Pt{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=H.empty){if(t==H.empty){e.delete(0,e.doc.content.size);let t=Pt.atStart(e.doc);t.eq(e.selection)||e.setSelection(t)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new Bt(e)}map(e){return new Bt(e)}eq(e){return e instanceof Bt}getBookmark(){return jt}}Pt.jsonID("all",Bt);const jt={map(){return this},resolve:e=>new Bt(e)};function Ht(e,t,n,r,o,i=!1){if(t.inlineContent)return It.create(e,n);for(let s=r-(o>0?0:1);o>0?s<t.childCount:s>=0;s+=o){let r=t.child(s);if(r.isAtom){if(!i&&$t.isSelectable(r))return $t.create(e,n-(o<0?r.nodeSize:0))}else{let t=Ht(e,r,n+o,o<0?r.childCount:0,o,i);if(t)return t}n+=r.nodeSize*o}return null}function Vt(e,t,n){let r=e.steps.length-1;if(r<t)return;let o,i=e.steps[r];(i instanceof nt||i instanceof rt)&&(e.mapping.maps[r].forEach(((e,t,n,r)=>{null==o&&(o=r)})),e.setSelection(Pt.near(e.doc.resolve(o),n)))}class Ut extends At{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=-3&this.updated|1,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return B.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||B.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let r=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);let o=this.storedMarks;if(!o){let e=this.doc.resolve(t);o=n==t?e.marks():e.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,o)),this.selection.empty||this.setSelection(Pt.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function Wt(e,t){return t&&e?e.bind(t):e}class qt{constructor(e,t,n){this.name=e,this.init=Wt(t.init,n),this.apply=Wt(t.apply,n)}}const Kt=[new qt("doc",{init:e=>e.doc||e.schema.topNodeType.createAndFill(),apply:e=>e.doc}),new qt("selection",{init:(e,t)=>e.selection||Pt.atStart(t.doc),apply:e=>e.selection}),new qt("storedMarks",{init:e=>e.storedMarks||null,apply:(e,t,n,r)=>r.selection.$cursor?e.storedMarks:null}),new qt("scrollToSelection",{init:()=>0,apply:(e,t)=>e.scrolledIntoView?t+1:t})];class Jt{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=Kt.slice(),t&&t.forEach((e=>{if(this.pluginsByKey[e.key])throw new RangeError("Adding different instances of a keyed plugin ("+e.key+")");this.plugins.push(e),this.pluginsByKey[e.key]=e,e.spec.state&&this.fields.push(new qt(e.key,e.spec.state,e))}))}}class Qt{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let t=this.config.plugins[n];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),r=null;for(;;){let o=!1;for(let i=0;i<this.config.plugins.length;i++){let s=this.config.plugins[i];if(s.spec.appendTransaction){let l=r?r[i].n:0,a=r?r[i].state:this,c=l<t.length&&s.spec.appendTransaction.call(s,l?t.slice(l):t,a,n);if(c&&n.filterTransaction(c,i)){if(c.setMeta("appendedTransaction",e),!r){r=[];for(let e=0;e<this.config.plugins.length;e++)r.push(e<i?{state:n,n:t.length}:{state:this,n:0})}t.push(c),n=n.applyInner(c),o=!0}r&&(r[i]={state:n,n:t.length})}}if(!o)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new Qt(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let o=n[r];t[o.name]=o.apply(e,this[o.name],this,t)}return t}get tr(){return new Ut(this)}static create(e){let t=new Jt(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new Qt(t);for(let r=0;r<t.fields.length;r++)n[t.fields[r].name]=t.fields[r].init(e,n);return n}reconfigure(e){let t=new Jt(this.schema,e.plugins),n=t.fields,r=new Qt(t);for(let o=0;o<n.length;o++){let t=n[o].name;r[t]=this.hasOwnProperty(t)?this[t]:n[o].init(e,r)}return r}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map((e=>e.toJSON()))),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw new RangeError("The JSON fields `doc` and `selection` are reserved");let r=e[n],o=r.spec.state;o&&o.toJSON&&(t[n]=o.toJSON.call(r,this[r.key]))}return t}static fromJSON(e,t,n){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let r=new Jt(e.schema,e.plugins),o=new Qt(r);return r.fields.forEach((r=>{if("doc"==r.name)o.doc=se.fromJSON(e.schema,t.doc);else if("selection"==r.name)o.selection=Pt.fromJSON(o.doc,t.selection);else if("storedMarks"==r.name)t.storedMarks&&(o.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let i in n){let s=n[i],l=s.spec.state;if(s.key==r.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(t,i))return void(o[r.name]=l.fromJSON.call(s,e,t[i],o))}o[r.name]=r.init(e,o)}})),o}}function Gt(e,t,n){for(let r in e){let o=e[r];o instanceof Function?o=o.bind(t):"handleDOMEvents"==r&&(o=Gt(o,t,{})),n[r]=o}return n}class Yt{constructor(e){this.spec=e,this.props={},e.props&&Gt(e.props,this,this.props),this.key=e.key?e.key.key:Zt("plugin")}getState(e){return e[this.key]}}const Xt=Object.create(null);function Zt(e){return e in Xt?e+"$"+ ++Xt[e]:(Xt[e]=0,e+"$")}class en{constructor(e="key"){this.key=Zt(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const tn=function(e){for(var t=0;;t++)if(!(e=e.previousSibling))return t},nn=function(e){let t=e.assignedSlot||e.parentNode;return t&&11==t.nodeType?t.host:t};let rn=null;const on=function(e,t,n){let r=rn||(rn=document.createRange());return r.setEnd(e,null==n?e.nodeValue.length:n),r.setStart(e,t||0),r},sn=function(e,t,n,r){return n&&(an(e,t,n,r,-1)||an(e,t,n,r,1))},ln=/^(img|br|input|textarea|hr)$/i;function an(e,t,n,r,o){for(;;){if(e==n&&t==r)return!0;if(t==(o<0?0:cn(e))){let n=e.parentNode;if(!n||1!=n.nodeType||un(e)||ln.test(e.nodeName)||"false"==e.contentEditable)return!1;t=tn(e)+(o<0?0:1),e=n}else{if(1!=e.nodeType)return!1;if("false"==(e=e.childNodes[t+(o<0?-1:0)]).contentEditable)return!1;t=o<0?cn(e):0}}}function cn(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function un(e){let t;for(let n=e;n&&!(t=n.pmViewDesc);n=n.parentNode);return t&&t.node&&t.node.isBlock&&(t.dom==e||t.contentDOM==e)}const dn=function(e){return e.focusNode&&sn(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset)};function fn(e,t){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=e,n.key=n.code=t,n}const hn="undefined"!=typeof navigator?navigator:null,pn="undefined"!=typeof document?document:null,mn=hn&&hn.userAgent||"",gn=/Edge\/(\d+)/.exec(mn),yn=/MSIE \d/.exec(mn),vn=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(mn),bn=!!(yn||vn||gn),kn=yn?document.documentMode:vn?+vn[1]:gn?+gn[1]:0,wn=!bn&&/gecko\/(\d+)/i.test(mn);wn&&(/Firefox\/(\d+)/.exec(mn)||[0,0])[1];const Sn=!bn&&/Chrome\/(\d+)/.exec(mn),xn=!!Sn,Cn=Sn?+Sn[1]:0,En=!bn&&!!hn&&/Apple Computer/.test(hn.vendor),Mn=En&&(/Mobile\/\w+/.test(mn)||!!hn&&hn.maxTouchPoints>2),Tn=Mn||!!hn&&/Mac/.test(hn.platform),On=!!hn&&/Win/.test(hn.platform),Nn=/Android \d/.test(mn),An=!!pn&&"webkitFontSmoothing"in pn.documentElement.style,Dn=An?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Pn(e){let t=e.defaultView&&e.defaultView.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:e.documentElement.clientWidth,top:0,bottom:e.documentElement.clientHeight}}function Rn(e,t){return"number"==typeof e?e:e[t]}function Ln(e){let t=e.getBoundingClientRect(),n=t.width/e.offsetWidth||1,r=t.height/e.offsetHeight||1;return{left:t.left,right:t.left+e.clientWidth*n,top:t.top,bottom:t.top+e.clientHeight*r}}function zn(e,t,n){let r=e.someProp("scrollThreshold")||0,o=e.someProp("scrollMargin")||5,i=e.dom.ownerDocument;for(let s=n||e.dom;s;){if(1!=s.nodeType){s=nn(s);continue}let e=s,n=e==i.body,l=n?Pn(i):Ln(e),a=0,c=0;if(t.top<l.top+Rn(r,"top")?c=-(l.top-t.top+Rn(o,"top")):t.bottom>l.bottom-Rn(r,"bottom")&&(c=t.bottom-t.top>l.bottom-l.top?t.top+Rn(o,"top")-l.top:t.bottom-l.bottom+Rn(o,"bottom")),t.left<l.left+Rn(r,"left")?a=-(l.left-t.left+Rn(o,"left")):t.right>l.right-Rn(r,"right")&&(a=t.right-l.right+Rn(o,"right")),a||c)if(n)i.defaultView.scrollBy(a,c);else{let n=e.scrollLeft,r=e.scrollTop;c&&(e.scrollTop+=c),a&&(e.scrollLeft+=a);let o=e.scrollLeft-n,i=e.scrollTop-r;t={left:t.left-o,top:t.top-i,right:t.right-o,bottom:t.bottom-i}}let u=n?"fixed":getComputedStyle(s).position;if(/^(fixed|sticky)$/.test(u))break;s="absolute"==u?s.offsetParent:nn(s)}}function In(e){let t=[],n=e.ownerDocument;for(let r=e;r&&(t.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),e!=n);r=nn(r));return t}function _n(e,t){for(let n=0;n<e.length;n++){let{dom:r,top:o,left:i}=e[n];r.scrollTop!=o+t&&(r.scrollTop=o+t),r.scrollLeft!=i&&(r.scrollLeft=i)}}let $n=null;function Fn(e,t){let n,r,o,i,s=2e8,l=0,a=t.top,c=t.top;for(let u=e.firstChild,d=0;u;u=u.nextSibling,d++){let e;if(1==u.nodeType)e=u.getClientRects();else{if(3!=u.nodeType)continue;e=on(u).getClientRects()}for(let f=0;f<e.length;f++){let h=e[f];if(h.top<=a&&h.bottom>=c){a=Math.max(h.bottom,a),c=Math.min(h.top,c);let e=h.left>t.left?h.left-t.left:h.right<t.left?t.left-h.right:0;if(e<s){n=u,s=e,r=e&&3==n.nodeType?{left:h.right<t.left?h.right:h.left,top:t.top}:t,1==u.nodeType&&e&&(l=d+(t.left>=(h.left+h.right)/2?1:0));continue}}else h.top>t.top&&!o&&h.left<=t.left&&h.right>=t.left&&(o=u,i={left:Math.max(h.left,Math.min(h.right,t.left)),top:h.top});!n&&(t.left>=h.right&&t.top>=h.top||t.left>=h.left&&t.top>=h.bottom)&&(l=d+1)}}return!n&&o&&(n=o,r=i,s=0),n&&3==n.nodeType?function(e,t){let n=e.nodeValue.length,r=document.createRange();for(let o=0;o<n;o++){r.setEnd(e,o+1),r.setStart(e,o);let n=Un(r,1);if(n.top!=n.bottom&&Bn(t,n))return{node:e,offset:o+(t.left>=(n.left+n.right)/2?1:0)}}return{node:e,offset:0}}(n,r):!n||s&&1==n.nodeType?{node:e,offset:l}:Fn(n,r)}function Bn(e,t){return e.left>=t.left-1&&e.left<=t.right+1&&e.top>=t.top-1&&e.top<=t.bottom+1}function jn(e,t,n){let r=e.childNodes.length;if(r&&n.top<n.bottom)for(let o=Math.max(0,Math.min(r-1,Math.floor(r*(t.top-n.top)/(n.bottom-n.top))-2)),i=o;;){let n=e.childNodes[i];if(1==n.nodeType){let e=n.getClientRects();for(let r=0;r<e.length;r++){let o=e[r];if(Bn(t,o))return jn(n,t,o)}}if((i=(i+1)%r)==o)break}return e}function Hn(e,t){let n,r=e.dom.ownerDocument,o=0,i=function(e,t,n){if(e.caretPositionFromPoint)try{let r=e.caretPositionFromPoint(t,n);if(r)return{node:r.offsetNode,offset:Math.min(cn(r.offsetNode),r.offset)}}catch(r){}if(e.caretRangeFromPoint){let r=e.caretRangeFromPoint(t,n);if(r)return{node:r.startContainer,offset:Math.min(cn(r.startContainer),r.startOffset)}}}(r,t.left,t.top);i&&({node:n,offset:o}=i);let s,l=(e.root.elementFromPoint?e.root:r).elementFromPoint(t.left,t.top);if(!l||!e.dom.contains(1!=l.nodeType?l.parentNode:l)){let n=e.dom.getBoundingClientRect();if(!Bn(t,n))return null;if(l=jn(e.dom,t,n),!l)return null}if(En)for(let c=l;n&&c;c=nn(c))c.draggable&&(n=void 0);if(l=function(e,t){let n=e.parentNode;return n&&/^li$/i.test(n.nodeName)&&t.left<e.getBoundingClientRect().left?n:e}(l,t),n){if(wn&&1==n.nodeType&&(o=Math.min(o,n.childNodes.length),o<n.childNodes.length)){let e,r=n.childNodes[o];"IMG"==r.nodeName&&(e=r.getBoundingClientRect()).right<=t.left&&e.bottom>t.top&&o++}let r;An&&o&&1==n.nodeType&&1==(r=n.childNodes[o-1]).nodeType&&"false"==r.contentEditable&&r.getBoundingClientRect().top>=t.top&&o--,n==e.dom&&o==n.childNodes.length-1&&1==n.lastChild.nodeType&&t.top>n.lastChild.getBoundingClientRect().bottom?s=e.state.doc.content.size:0!=o&&1==n.nodeType&&"BR"==n.childNodes[o-1].nodeName||(s=function(e,t,n,r){let o=-1;for(let i=t,s=!1;i!=e.dom;){let t,n=e.docView.nearestDesc(i,!0);if(!n)return null;if(1==n.dom.nodeType&&(n.node.isBlock&&n.parent||!n.contentDOM)&&((t=n.dom.getBoundingClientRect()).width||t.height)&&(n.node.isBlock&&n.parent&&(!s&&t.left>r.left||t.top>r.top?o=n.posBefore:(!s&&t.right<r.left||t.bottom<r.top)&&(o=n.posAfter),s=!0),!n.contentDOM&&o<0&&!n.node.isText))return(n.node.isBlock?r.top<(t.top+t.bottom)/2:r.left<(t.left+t.right)/2)?n.posBefore:n.posAfter;i=n.dom.parentNode}return o>-1?o:e.docView.posFromDOM(t,n,-1)}(e,n,o,t))}null==s&&(s=function(e,t,n){let{node:r,offset:o}=Fn(t,n),i=-1;if(1==r.nodeType&&!r.firstChild){let e=r.getBoundingClientRect();i=e.left!=e.right&&n.left>(e.left+e.right)/2?1:-1}return e.docView.posFromDOM(r,o,i)}(e,l,t));let a=e.docView.nearestDesc(l,!0);return{pos:s,inside:a?a.posAtStart-a.border:-1}}function Vn(e){return e.top<e.bottom||e.left<e.right}function Un(e,t){let n=e.getClientRects();if(n.length){let e=n[t<0?0:n.length-1];if(Vn(e))return e}return Array.prototype.find.call(n,Vn)||e.getBoundingClientRect()}const Wn=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function qn(e,t,n){let{node:r,offset:o,atom:i}=e.docView.domFromPos(t,n<0?-1:1),s=An||wn;if(3==r.nodeType){if(!s||!Wn.test(r.nodeValue)&&(n<0?o:o!=r.nodeValue.length)){let e=o,t=o,i=n<0?1:-1;return n<0&&!o?(t++,i=-1):n>=0&&o==r.nodeValue.length?(e--,i=1):n<0?e--:t++,Kn(Un(on(r,e,t),i),i<0)}{let e=Un(on(r,o,o),n);if(wn&&o&&/\s/.test(r.nodeValue[o-1])&&o<r.nodeValue.length){let t=Un(on(r,o-1,o-1),-1);if(t.top==e.top){let n=Un(on(r,o,o+1),-1);if(n.top!=e.top)return Kn(n,n.left<t.left)}}return e}}if(!e.state.doc.resolve(t-(i||0)).parent.inlineContent){if(null==i&&o&&(n<0||o==cn(r))){let e=r.childNodes[o-1];if(1==e.nodeType)return Jn(e.getBoundingClientRect(),!1)}if(null==i&&o<cn(r)){let e=r.childNodes[o];if(1==e.nodeType)return Jn(e.getBoundingClientRect(),!0)}return Jn(r.getBoundingClientRect(),n>=0)}if(null==i&&o&&(n<0||o==cn(r))){let e=r.childNodes[o-1],t=3==e.nodeType?on(e,cn(e)-(s?0:1)):1!=e.nodeType||"BR"==e.nodeName&&e.nextSibling?null:e;if(t)return Kn(Un(t,1),!1)}if(null==i&&o<cn(r)){let e=r.childNodes[o];for(;e.pmViewDesc&&e.pmViewDesc.ignoreForCoords;)e=e.nextSibling;let t=e?3==e.nodeType?on(e,0,s?0:1):1==e.nodeType?e:null:null;if(t)return Kn(Un(t,-1),!0)}return Kn(Un(3==r.nodeType?on(r):r,-n),n>=0)}function Kn(e,t){if(0==e.width)return e;let n=t?e.left:e.right;return{top:e.top,bottom:e.bottom,left:n,right:n}}function Jn(e,t){if(0==e.height)return e;let n=t?e.top:e.bottom;return{top:n,bottom:n,left:e.left,right:e.right}}function Qn(e,t,n){let r=e.state,o=e.root.activeElement;r!=t&&e.updateState(t),o!=e.dom&&e.focus();try{return n()}finally{r!=t&&e.updateState(r),o!=e.dom&&o&&o.focus()}}const Gn=/[\u0590-\u08ac]/;let Yn=null,Xn=null,Zn=!1;function er(e,t,n){return Yn==t&&Xn==n?Zn:(Yn=t,Xn=n,Zn="up"==n||"down"==n?function(e,t,n){let r=t.selection,o="up"==n?r.$from:r.$to;return Qn(e,t,(()=>{let{node:t}=e.docView.domFromPos(o.pos,"up"==n?-1:1);for(;;){let n=e.docView.nearestDesc(t,!0);if(!n)break;if(n.node.isBlock){t=n.contentDOM||n.dom;break}t=n.dom.parentNode}let r=qn(e,o.pos,1);for(let e=t.firstChild;e;e=e.nextSibling){let t;if(1==e.nodeType)t=e.getClientRects();else{if(3!=e.nodeType)continue;t=on(e,0,e.nodeValue.length).getClientRects()}for(let e=0;e<t.length;e++){let o=t[e];if(o.bottom>o.top+1&&("up"==n?r.top-o.top>2*(o.bottom-r.top):o.bottom-r.bottom>2*(r.bottom-o.top)))return!1}}return!0}))}(e,t,n):function(e,t,n){let{$head:r}=t.selection;if(!r.parent.isTextblock)return!1;let o=r.parentOffset,i=!o,s=o==r.parent.content.size,l=e.domSelection();return l?Gn.test(r.parent.textContent)&&l.modify?Qn(e,t,(()=>{let{focusNode:t,focusOffset:o,anchorNode:i,anchorOffset:s}=e.domSelectionRange(),a=l.caretBidiLevel;l.modify("move",n,"character");let c=r.depth?e.docView.domAfterPos(r.before()):e.dom,{focusNode:u,focusOffset:d}=e.domSelectionRange(),f=u&&!c.contains(1==u.nodeType?u:u.parentNode)||t==u&&o==d;try{l.collapse(i,s),t&&(t!=i||o!=s)&&l.extend&&l.extend(t,o)}catch(h){}return null!=a&&(l.caretBidiLevel=a),f})):"left"==n||"backward"==n?i:s:r.pos==r.start()||r.pos==r.end()}(e,t,n))}class tr{constructor(e,t,n,r){this.parent=e,this.children=t,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let r=this.children[t];if(r==e)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){if(this.contentDOM&&this.contentDOM.contains(1==e.nodeType?e:e.parentNode)){if(n<0){let n,r;if(e==this.contentDOM)n=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.previousSibling}for(;n&&(!(r=n.pmViewDesc)||r.parent!=this);)n=n.previousSibling;return n?this.posBeforeChild(r)+r.size:this.posAtStart}{let n,r;if(e==this.contentDOM)n=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.nextSibling}for(;n&&(!(r=n.pmViewDesc)||r.parent!=this);)n=n.nextSibling;return n?this.posBeforeChild(r):this.posAtEnd}}let r;if(e==this.dom&&this.contentDOM)r=t>tn(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==t)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!1;break}if(t.previousSibling)break}if(null==r&&t==e.childNodes.length)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!0;break}if(t.nextSibling)break}}return(null==r?n>0:r)?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,r=e;r;r=r.parentNode){let o,i=this.getDesc(r);if(i&&(!t||i.node)){if(!n||!(o=i.nodeDOM)||(1==o.nodeType?o.contains(1==e.nodeType?e:e.parentNode):o==e))return i;n=!1}}}getDesc(e){let t=e.pmViewDesc;for(let n=t;n;n=n.parent)if(n==this)return t}posFromDOM(e,t,n){for(let r=e;r;r=r.parentNode){let o=this.getDesc(r);if(o)return o.localPosFromDOM(e,t,n)}return-1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let r=this.children[t],o=n+r.size;if(n==e&&o!=n){for(;!r.border&&r.children.length;)for(let e=0;e<r.children.length;e++){let t=r.children[e];if(t.size){r=t;break}}return r}if(e<o)return r.descAt(e-n-r.border);n=o}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,r=0;for(let o=0;n<this.children.length;n++){let t=this.children[n],i=o+t.size;if(i>e||t instanceof ar){r=e-o;break}o=i}if(r)return this.children[n].domFromPos(r-this.children[n].border,t);for(let o;n&&!(o=this.children[n-1]).size&&o instanceof nr&&o.side>=0;n--);if(t<=0){let e,r=!0;for(;e=n?this.children[n-1]:null,e&&e.dom.parentNode!=this.contentDOM;n--,r=!1);return e&&t&&r&&!e.border&&!e.domAtom?e.domFromPos(e.size,t):{node:this.contentDOM,offset:e?tn(e.dom)+1:0}}{let e,r=!0;for(;e=n<this.children.length?this.children[n]:null,e&&e.dom.parentNode!=this.contentDOM;n++,r=!1);return e&&r&&!e.border&&!e.domAtom?e.domFromPos(0,t):{node:this.contentDOM,offset:e?tn(e.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(0==this.children.length)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,o=-1;for(let i=n,s=0;;s++){let n=this.children[s],l=i+n.size;if(-1==r&&e<=l){let o=i+n.border;if(e>=o&&t<=l-n.border&&n.node&&n.contentDOM&&this.contentDOM.contains(n.contentDOM))return n.parseRange(e,t,o);e=i;for(let t=s;t>0;t--){let n=this.children[t-1];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(1)){r=tn(n.dom)+1;break}e-=n.size}-1==r&&(r=0)}if(r>-1&&(l>t||s==this.children.length-1)){t=l;for(let e=s+1;e<this.children.length;e++){let n=this.children[e];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){o=tn(n.dom);break}t+=n.size}-1==o&&(o=this.contentDOM.childNodes.length);break}i=l}return{node:this.contentDOM,from:e,to:t,fromOffset:r,toOffset:o}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return 0==t.size||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(1!=t.nodeType||n==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,r=!1){let o=Math.min(e,t),i=Math.max(e,t);for(let h=0,p=0;h<this.children.length;h++){let s=this.children[h],l=p+s.size;if(o>p&&i<l)return s.setSelection(e-p-s.border,t-p-s.border,n,r);p=l}let s=this.domFromPos(e,e?-1:1),l=t==e?s:this.domFromPos(t,t?-1:1),a=n.root.getSelection(),c=n.domSelectionRange(),u=!1;if((wn||En)&&e==t){let{node:e,offset:t}=s;if(3==e.nodeType){if(u=!(!t||"\n"!=e.nodeValue[t-1]),u&&t==e.nodeValue.length)for(let n,r=e;r;r=r.parentNode){if(n=r.nextSibling){"BR"==n.nodeName&&(s=l={node:n.parentNode,offset:tn(n)+1});break}let e=r.pmViewDesc;if(e&&e.node&&e.node.isBlock)break}}else{let n=e.childNodes[t-1];u=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(wn&&c.focusNode&&c.focusNode!=l.node&&1==c.focusNode.nodeType){let e=c.focusNode.childNodes[c.focusOffset];e&&"false"==e.contentEditable&&(r=!0)}if(!(r||u&&En)&&sn(s.node,s.offset,c.anchorNode,c.anchorOffset)&&sn(l.node,l.offset,c.focusNode,c.focusOffset))return;let d=!1;if((a.extend||e==t)&&!u){a.collapse(s.node,s.offset);try{e!=t&&a.extend(l.node,l.offset),d=!0}catch(f){}}if(!d){if(e>t){let e=s;s=l,l=e}let n=document.createRange();n.setEnd(l.node,l.offset),n.setStart(s.node,s.offset),a.removeAllRanges(),a.addRange(n)}}ignoreMutation(e){return!this.contentDOM&&"selection"!=e.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,r=0;r<this.children.length;r++){let o=this.children[r],i=n+o.size;if(n==i?e<=i&&t>=n:e<i&&t>n){let r=n+o.border,s=i-o.border;if(e>=r&&t<=s)return this.dirty=e==n||t==i?2:1,void(e!=r||t!=s||!o.contentLost&&o.dom.parentNode==this.contentDOM?o.markDirty(e-r,t-r):o.dirty=3);o.dirty=o.dom!=o.contentDOM||o.dom.parentNode!=this.contentDOM||o.children.length?3:2}n=i}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=1==e?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}}class nr extends tr{constructor(e,t,n,r){let o,i=t.type.toDOM;if("function"==typeof i&&(i=i(n,(()=>o?o.parent?o.parent.posBeforeChild(o):void 0:r))),!t.type.spec.raw){if(1!=i.nodeType){let e=document.createElement("span");e.appendChild(i),i=e}i.contentEditable="false",i.classList.add("ProseMirror-widget")}super(e,[],i,null),this.widget=t,this.widget=t,o=this}matchesWidget(e){return 0==this.dirty&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return"selection"!=e.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class rr extends tr{constructor(e,t,n,r){super(e,[],t,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return"characterData"===e.type&&e.target.nodeValue==e.oldValue}}class or extends tr{constructor(e,t,n,r,o){super(e,[],n,r),this.mark=t,this.spec=o}static create(e,t,n,r){let o=r.nodeViews[t.type.name],i=o&&o(t,r,n);return i&&i.dom||(i=_e.renderSpec(document,t.type.spec.toDOM(t,n),null,t.attrs)),new or(e,t,i.dom,i.contentDOM||i.dom,i)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return 3!=this.dirty&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),0!=this.dirty){let e=this.parent;for(;!e.node;)e=e.parent;e.dirty<this.dirty&&(e.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let r=or.create(this.parent,this.mark,!0,n),o=this.children,i=this.size;t<i&&(o=wr(o,t,i,n)),e>0&&(o=wr(o,0,e,n));for(let s=0;s<o.length;s++)o[s].parent=r;return r.children=o,r}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class ir extends tr{constructor(e,t,n,r,o,i,s,l,a){super(e,[],o,i),this.node=t,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=s}static create(e,t,n,r,o,i){let s,l=o.nodeViews[t.type.name],a=l&&l(t,o,(()=>s?s.parent?s.parent.posBeforeChild(s):void 0:i),n,r),c=a&&a.dom,u=a&&a.contentDOM;if(t.isText)if(c){if(3!=c.nodeType)throw new RangeError("Text must be rendered as a DOM text node")}else c=document.createTextNode(t.text);else if(!c){let e=_e.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs);({dom:c,contentDOM:u}=e)}u||t.isText||"BR"==c.nodeName||(c.hasAttribute("contenteditable")||(c.contentEditable="false"),t.type.spec.draggable&&(c.draggable=!0));let d=c;return c=gr(c,n,t),a?s=new cr(e,t,n,r,c,u||null,d,a,o,i+1):t.isText?new lr(e,t,n,r,c,d,o):new ir(e,t,n,r,c,u||null,d,o,i+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(e.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>I.empty)}else e.contentElement=this.contentDOM;else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return 0==this.dirty&&e.eq(this.node)&&yr(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let n=this.node.inlineContent,r=t,o=e.composing?this.localCompositionInfo(e,t):null,i=o&&o.pos>-1?o:null,s=o&&o.pos<0,l=new br(this,i&&i.node,e);!function(e,t,n,r){let o=t.locals(e),i=0;if(0==o.length){for(let n=0;n<e.childCount;n++){let s=e.child(n);r(s,o,t.forChild(i,s),n),i+=s.nodeSize}return}let s=0,l=[],a=null;for(let c=0;;){let u,d,f,h;for(;s<o.length&&o[s].to==i;){let e=o[s++];e.widget&&(u?(d||(d=[u])).push(e):u=e)}if(u)if(d){d.sort(kr);for(let e=0;e<d.length;e++)n(d[e],c,!!a)}else n(u,c,!!a);if(a)h=-1,f=a,a=null;else{if(!(c<e.childCount))break;h=c,f=e.child(c++)}for(let e=0;e<l.length;e++)l[e].to<=i&&l.splice(e--,1);for(;s<o.length&&o[s].from<=i&&o[s].to>i;)l.push(o[s++]);let p=i+f.nodeSize;if(f.isText){let e=p;s<o.length&&o[s].from<e&&(e=o[s].from);for(let t=0;t<l.length;t++)l[t].to<e&&(e=l[t].to);e<p&&(a=f.cut(e-i),f=f.cut(0,e-i),p=e,h=-1)}else for(;s<o.length&&o[s].to<p;)s++;r(f,f.isInline&&!f.isLeaf?l.filter((e=>!e.inline)):l.slice(),t.forChild(i,f),h),i=p}}(this.node,this.innerDeco,((t,o,i)=>{t.spec.marks?l.syncToMarks(t.spec.marks,n,e):t.type.side>=0&&!i&&l.syncToMarks(o==this.node.childCount?B.none:this.node.child(o).marks,n,e),l.placeWidget(t,e,r)}),((t,i,a,c)=>{let u;l.syncToMarks(t.marks,n,e),l.findNodeMatch(t,i,a,c)||s&&e.state.selection.from>r&&e.state.selection.to<r+t.nodeSize&&(u=l.findIndexWithChild(o.node))>-1&&l.updateNodeAt(t,i,a,u,e)||l.updateNextNode(t,i,a,e,c,r)||l.addNode(t,i,a,e,r),r+=t.nodeSize})),l.syncToMarks([],n,e),this.node.isTextblock&&l.addTextblockHacks(),l.destroyRest(),(l.changed||2==this.dirty)&&(i&&this.protectLocalComposition(e,i),ur(this.contentDOM,this.children,e),Mn&&function(e){if("UL"==e.nodeName||"OL"==e.nodeName){let t=e.style.cssText;e.style.cssText=t+"; list-style: square !important",window.getComputedStyle(e).listStyle,e.style.cssText=t}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:r}=e.state.selection;if(!(e.state.selection instanceof It)||n<t||r>t+this.node.content.size)return null;let o=e.input.compositionNode;if(!o||!this.dom.contains(o.parentNode))return null;if(this.node.inlineContent){let e=o.nodeValue,i=function(e,t,n,r){for(let o=0,i=0;o<e.childCount&&i<=r;){let s=e.child(o++),l=i;if(i+=s.nodeSize,!s.isText)continue;let a=s.text;for(;o<e.childCount;){let t=e.child(o++);if(i+=t.nodeSize,!t.isText)break;a+=t.text}if(i>=n){if(i>=r&&a.slice(r-t.length-l,r-l)==t)return r-t.length;let e=l<r?a.lastIndexOf(t,r-l-1):-1;if(e>=0&&e+t.length+l>=n)return l+e;if(n==r&&a.length>=r+t.length-l&&a.slice(r-l,r-l+t.length)==t)return r}}return-1}(this.node.content,e,n-t,r-t);return i<0?null:{node:o,pos:i,text:e}}return{node:o,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:n,text:r}){if(this.getDesc(t))return;let o=t;for(;o.parentNode!=this.contentDOM;o=o.parentNode){for(;o.previousSibling;)o.parentNode.removeChild(o.previousSibling);for(;o.nextSibling;)o.parentNode.removeChild(o.nextSibling);o.pmViewDesc&&(o.pmViewDesc=void 0)}let i=new rr(this,o,t,r);e.input.compositionNodes.push(i),this.children=wr(this.children,n,n+r.length,e,i)}update(e,t,n,r){return!(3==this.dirty||!e.sameMarkup(this.node))&&(this.updateInner(e,t,n,r),!0)}updateInner(e,t,n,r){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(yr(e,this.outerDeco))return;let t=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=pr(this.dom,this.nodeDOM,hr(this.outerDeco,this.node,t),hr(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function sr(e,t,n,r,o){gr(r,t,e);let i=new ir(void 0,e,t,n,r,r,r,o,0);return i.contentDOM&&i.updateChildren(o,0),i}class lr extends ir{constructor(e,t,n,r,o,i,s){super(e,t,n,r,o,null,i,s,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,r){return!(3==this.dirty||0!=this.dirty&&!this.inParent()||!e.sameMarkup(this.node))&&(this.updateOuterDeco(t),0==this.dirty&&e.text==this.node.text||e.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=e.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=e,this.dirty=0,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return"characterData"!=e.type&&"selection"!=e.type}slice(e,t,n){let r=this.node.cut(e,t),o=document.createTextNode(r.text);return new lr(this.parent,r,this.outerDeco,this.innerDeco,o,o,n)}markDirty(e,t){super.markDirty(e,t),this.dom==this.nodeDOM||0!=e&&t!=this.nodeDOM.nodeValue.length||(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class ar extends tr{parseRule(){return{ignore:!0}}matchesHack(e){return 0==this.dirty&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class cr extends ir{constructor(e,t,n,r,o,i,s,l,a,c){super(e,t,n,r,o,i,s,a,c),this.spec=l}update(e,t,n,r){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let o=this.spec.update(e,t,n);return o&&this.updateInner(e,t,n,r),o}return!(!this.contentDOM&&!e.isLeaf)&&super.update(e,t,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,r){this.spec.setSelection?this.spec.setSelection(e,t,n.root):super.setSelection(e,t,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function ur(e,t,n){let r=e.firstChild,o=!1;for(let i=0;i<t.length;i++){let s=t[i],l=s.dom;if(l.parentNode==e){for(;l!=r;)r=vr(r),o=!0;r=r.nextSibling}else o=!0,e.insertBefore(l,r);if(s instanceof or){let t=r?r.previousSibling:e.lastChild;ur(s.contentDOM,s.children,n),r=t?t.nextSibling:e.firstChild}}for(;r;)r=vr(r),o=!0;o&&n.trackWrites==e&&(n.trackWrites=null)}const dr=function(e){e&&(this.nodeName=e)};dr.prototype=Object.create(null);const fr=[new dr];function hr(e,t,n){if(0==e.length)return fr;let r=n?fr[0]:new dr,o=[r];for(let i=0;i<e.length;i++){let s=e[i].type.attrs;if(s){s.nodeName&&o.push(r=new dr(s.nodeName));for(let e in s){let i=s[e];null!=i&&(n&&1==o.length&&o.push(r=new dr(t.isInline?"span":"div")),"class"==e?r.class=(r.class?r.class+" ":"")+i:"style"==e?r.style=(r.style?r.style+";":"")+i:"nodeName"!=e&&(r[e]=i))}}}return o}function pr(e,t,n,r){if(n==fr&&r==fr)return t;let o=t;for(let i=0;i<r.length;i++){let t=r[i],s=n[i];if(i){let n;s&&s.nodeName==t.nodeName&&o!=e&&(n=o.parentNode)&&n.nodeName.toLowerCase()==t.nodeName||(n=document.createElement(t.nodeName),n.pmIsDeco=!0,n.appendChild(o),s=fr[0]),o=n}mr(o,s||fr[0],t)}return o}function mr(e,t,n){for(let r in t)"class"==r||"style"==r||"nodeName"==r||r in n||e.removeAttribute(r);for(let r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=t[r]&&e.setAttribute(r,n[r]);if(t.class!=n.class){let r=t.class?t.class.split(" ").filter(Boolean):[],o=n.class?n.class.split(" ").filter(Boolean):[];for(let t=0;t<r.length;t++)-1==o.indexOf(r[t])&&e.classList.remove(r[t]);for(let t=0;t<o.length;t++)-1==r.indexOf(o[t])&&e.classList.add(o[t]);0==e.classList.length&&e.removeAttribute("class")}if(t.style!=n.style){if(t.style){let n,r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;n=r.exec(t.style);)e.style.removeProperty(n[1])}n.style&&(e.style.cssText+=n.style)}}function gr(e,t,n){return pr(e,e,fr,hr(t,n,1!=e.nodeType))}function yr(e,t){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].type.eq(t[n].type))return!1;return!0}function vr(e){let t=e.nextSibling;return e.parentNode.removeChild(e),t}class br{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function(e,t){let n=t,r=n.children.length,o=e.childCount,i=new Map,s=[];e:for(;o>0;){let l;for(;;)if(r){let e=n.children[r-1];if(!(e instanceof or)){l=e,r--;break}n=e,r=e.children.length}else{if(n==t)break e;r=n.parent.children.indexOf(n),n=n.parent}let a=l.node;if(a){if(a!=e.child(o-1))break;--o,i.set(l,o),s.push(l)}}return{index:o,matched:i,matches:s.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let r=0,o=this.stack.length>>1,i=Math.min(o,e.length);for(;r<i&&(r==o-1?this.top:this.stack[r+1<<1]).matchesMark(e[r])&&!1!==e[r].type.spec.spanning;)r++;for(;r<o;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),o--;for(;o<e.length;){this.stack.push(this.top,this.index+1);let r=-1;for(let t=this.index;t<Math.min(this.index+3,this.top.children.length);t++){let n=this.top.children[t];if(n.matchesMark(e[o])&&!this.isLocked(n.dom)){r=t;break}}if(r>-1)r>this.index&&(this.changed=!0,this.destroyBetween(this.index,r)),this.top=this.top.children[this.index];else{let r=or.create(this.top,e[o],t,n);this.top.children.splice(this.index,0,r),this.top=r,this.changed=!0}this.index=0,o++}}findNodeMatch(e,t,n,r){let o,i=-1;if(r>=this.preMatch.index&&(o=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,n))i=this.top.children.indexOf(o,this.index);else for(let s=this.index,l=Math.min(this.top.children.length,s+5);s<l;s++){let r=this.top.children[s];if(r.matchesNode(e,t,n)&&!this.preMatch.matched.has(r)){i=s;break}}return!(i<0)&&(this.destroyBetween(this.index,i),this.index++,!0)}updateNodeAt(e,t,n,r,o){let i=this.top.children[r];return 3==i.dirty&&i.dom==i.contentDOM&&(i.dirty=2),!!i.update(e,t,n,o)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let t=e.pmViewDesc;if(t)for(let e=this.index;e<this.top.children.length;e++)if(this.top.children[e]==t)return e;return-1}e=t}}updateNextNode(e,t,n,r,o,i){for(let s=this.index;s<this.top.children.length;s++){let l=this.top.children[s];if(l instanceof ir){let a=this.preMatch.matched.get(l);if(null!=a&&a!=o)return!1;let c,u=l.dom,d=this.isLocked(u)&&!(e.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==e.text&&3!=l.dirty&&yr(t,l.outerDeco));if(!d&&l.update(e,t,n,r))return this.destroyBetween(this.index,s),l.dom!=u&&(this.changed=!0),this.index++,!0;if(!d&&(c=this.recreateWrapper(l,e,t,n,r,i)))return this.destroyBetween(this.index,s),this.top.children[this.index]=c,c.contentDOM&&(c.dirty=2,c.updateChildren(r,i+1),c.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,r,o,i){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!yr(n,e.outerDeco)||!r.eq(e.innerDeco))return null;let s=ir.create(this.top,t,n,r,o,i);if(s.contentDOM){s.children=e.children,e.children=[];for(let e of s.children)e.parent=s}return e.destroy(),s}addNode(e,t,n,r,o){let i=ir.create(this.top,e,t,n,r,o);i.contentDOM&&i.updateChildren(r,o+1),this.top.children.splice(this.index++,0,i),this.changed=!0}placeWidget(e,t,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(!r||!r.matchesWidget(e)||e!=r.widget&&r.widget.type.toDOM.parentNode){let r=new nr(this.top,e,t,n);this.top.children.splice(this.index++,0,r),this.changed=!0}else this.index++}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof or;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof lr)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((En||xn)&&e&&"false"==e.dom.contentEditable&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);"IMG"==e&&(n.className="ProseMirror-separator",n.alt=""),"BR"==e&&(n.className="ProseMirror-trailingBreak");let r=new ar(this.top,[],n,null);t!=this.top?t.children.push(r):t.children.splice(this.index++,0,r),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||1==e.nodeType&&e.contains(this.lock.parentNode))}}function kr(e,t){return e.type.side-t.type.side}function wr(e,t,n,r,o){let i=[];for(let s=0,l=0;s<e.length;s++){let a=e[s],c=l,u=l+=a.size;c>=n||u<=t?i.push(a):(c<t&&i.push(a.slice(0,t-c,r)),o&&(i.push(o),o=void 0),u>n&&i.push(a.slice(n-c,a.size,r)))}return i}function Sr(e,t=null){let n=e.domSelectionRange(),r=e.state.doc;if(!n.focusNode)return null;let o=e.docView.nearestDesc(n.focusNode),i=o&&0==o.size,s=e.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(s<0)return null;let l,a,c=r.resolve(s);if(dn(n)){for(l=s;o&&!o.node;)o=o.parent;let e=o.node;if(o&&e.isAtom&&$t.isSelectable(e)&&o.parent&&(!e.isInline||!function(e,t,n){for(let r=0==t,o=t==cn(e);r||o;){if(e==n)return!0;let t=tn(e);if(!(e=e.parentNode))return!1;r=r&&0==t,o=o&&t==cn(e)}}(n.focusNode,n.focusOffset,o.dom))){let e=o.posBefore;a=new $t(s==e?c:r.resolve(e))}}else{if(n instanceof e.dom.ownerDocument.defaultView.Selection&&n.rangeCount>1){let t=s,o=s;for(let r=0;r<n.rangeCount;r++){let i=n.getRangeAt(r);t=Math.min(t,e.docView.posFromDOM(i.startContainer,i.startOffset,1)),o=Math.max(o,e.docView.posFromDOM(i.endContainer,i.endOffset,-1))}if(t<0)return null;[l,s]=o==e.state.selection.anchor?[o,t]:[t,o],c=r.resolve(s)}else l=e.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(l<0)return null}let u=r.resolve(l);if(!a){a=Dr(e,u,c,"pointer"==t||e.state.selection.head<c.pos&&!i?1:-1)}return a}function xr(e){return e.editable?e.hasFocus():Rr(e)&&document.activeElement&&document.activeElement.contains(e.dom)}function Cr(e,t=!1){let n=e.state.selection;if(Nr(e,n),xr(e)){if(!t&&e.input.mouseDown&&e.input.mouseDown.allowDefault&&xn){let t=e.domSelectionRange(),n=e.domObserver.currentSelection;if(t.anchorNode&&n.anchorNode&&sn(t.anchorNode,t.anchorOffset,n.anchorNode,n.anchorOffset))return e.input.mouseDown.delayedSelectionSync=!0,void e.domObserver.setCurSelection()}if(e.domObserver.disconnectSelection(),e.cursorWrapper)!function(e){let t=e.domSelection(),n=document.createRange();if(!t)return;let r=e.cursorWrapper.dom,o="IMG"==r.nodeName;o?n.setStart(r.parentNode,tn(r)+1):n.setStart(r,0);n.collapse(!0),t.removeAllRanges(),t.addRange(n),!o&&!e.state.selection.visible&&bn&&kn<=11&&(r.disabled=!0,r.disabled=!1)}(e);else{let r,o,{anchor:i,head:s}=n;!Er||n instanceof It||(n.$from.parent.inlineContent||(r=Mr(e,n.from)),n.empty||n.$from.parent.inlineContent||(o=Mr(e,n.to))),e.docView.setSelection(i,s,e,t),Er&&(r&&Or(r),o&&Or(o)),n.visible?e.dom.classList.remove("ProseMirror-hideselection"):(e.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function(e){let t=e.dom.ownerDocument;t.removeEventListener("selectionchange",e.input.hideSelectionGuard);let n=e.domSelectionRange(),r=n.anchorNode,o=n.anchorOffset;t.addEventListener("selectionchange",e.input.hideSelectionGuard=()=>{n.anchorNode==r&&n.anchorOffset==o||(t.removeEventListener("selectionchange",e.input.hideSelectionGuard),setTimeout((()=>{xr(e)&&!e.state.selection.visible||e.dom.classList.remove("ProseMirror-hideselection")}),20))})}(e))}e.domObserver.setCurSelection(),e.domObserver.connectSelection()}}const Er=En||xn&&Cn<63;function Mr(e,t){let{node:n,offset:r}=e.docView.domFromPos(t,0),o=r<n.childNodes.length?n.childNodes[r]:null,i=r?n.childNodes[r-1]:null;if(En&&o&&"false"==o.contentEditable)return Tr(o);if(!(o&&"false"!=o.contentEditable||i&&"false"!=i.contentEditable)){if(o)return Tr(o);if(i)return Tr(i)}}function Tr(e){return e.contentEditable="true",En&&e.draggable&&(e.draggable=!1,e.wasDraggable=!0),e}function Or(e){e.contentEditable="false",e.wasDraggable&&(e.draggable=!0,e.wasDraggable=null)}function Nr(e,t){if(t instanceof $t){let n=e.docView.descAt(t.from);n!=e.lastSelectedViewDesc&&(Ar(e),n&&n.selectNode(),e.lastSelectedViewDesc=n)}else Ar(e)}function Ar(e){e.lastSelectedViewDesc&&(e.lastSelectedViewDesc.parent&&e.lastSelectedViewDesc.deselectNode(),e.lastSelectedViewDesc=void 0)}function Dr(e,t,n,r){return e.someProp("createSelectionBetween",(r=>r(e,t,n)))||It.between(t,n,r)}function Pr(e){return!(e.editable&&!e.hasFocus())&&Rr(e)}function Rr(e){let t=e.domSelectionRange();if(!t.anchorNode)return!1;try{return e.dom.contains(3==t.anchorNode.nodeType?t.anchorNode.parentNode:t.anchorNode)&&(e.editable||e.dom.contains(3==t.focusNode.nodeType?t.focusNode.parentNode:t.focusNode))}catch(n){return!1}}function Lr(e,t){let{$anchor:n,$head:r}=e.selection,o=t>0?n.max(r):n.min(r),i=o.parent.inlineContent?o.depth?e.doc.resolve(t>0?o.after():o.before()):null:o;return i&&Pt.findFrom(i,t)}function zr(e,t){return e.dispatch(e.state.tr.setSelection(t).scrollIntoView()),!0}function Ir(e,t,n){let r=e.state.selection;if(!(r instanceof It)){if(r instanceof $t&&r.node.isInline)return zr(e,new It(t>0?r.$to:r.$from));{let n=Lr(e.state,t);return!!n&&zr(e,n)}}if(n.indexOf("s")>-1){let{$head:n}=r,o=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter;if(!o||o.isText||!o.isLeaf)return!1;let i=e.state.doc.resolve(n.pos+o.nodeSize*(t<0?-1:1));return zr(e,new It(r.$anchor,i))}if(!r.empty)return!1;if(e.endOfTextblock(t>0?"forward":"backward")){let n=Lr(e.state,t);return!!(n&&n instanceof $t)&&zr(e,n)}if(!(Tn&&n.indexOf("m")>-1)){let n,o=r.$head,i=o.textOffset?null:t<0?o.nodeBefore:o.nodeAfter;if(!i||i.isText)return!1;let s=t<0?o.pos-i.nodeSize:o.pos;return!!(i.isAtom||(n=e.docView.descAt(s))&&!n.contentDOM)&&($t.isSelectable(i)?zr(e,new $t(t<0?e.state.doc.resolve(o.pos-i.nodeSize):o)):!!An&&zr(e,new It(e.state.doc.resolve(t<0?s:s+i.nodeSize))))}}function _r(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function $r(e,t){let n=e.pmViewDesc;return n&&0==n.size&&(t<0||e.nextSibling||"BR"!=e.nodeName)}function Fr(e,t){return t<0?function(e){let t=e.domSelectionRange(),n=t.focusNode,r=t.focusOffset;if(!n)return;let o,i,s=!1;wn&&1==n.nodeType&&r<_r(n)&&$r(n.childNodes[r],-1)&&(s=!0);for(;;)if(r>0){if(1!=n.nodeType)break;{let e=n.childNodes[r-1];if($r(e,-1))o=n,i=--r;else{if(3!=e.nodeType)break;n=e,r=n.nodeValue.length}}}else{if(Br(n))break;{let t=n.previousSibling;for(;t&&$r(t,-1);)o=n.parentNode,i=tn(t),t=t.previousSibling;if(t)n=t,r=_r(n);else{if(n=n.parentNode,n==e.dom)break;r=0}}}s?jr(e,n,r):o&&jr(e,o,i)}(e):function(e){let t=e.domSelectionRange(),n=t.focusNode,r=t.focusOffset;if(!n)return;let o,i,s=_r(n);for(;;)if(r<s){if(1!=n.nodeType)break;if(!$r(n.childNodes[r],1))break;o=n,i=++r}else{if(Br(n))break;{let t=n.nextSibling;for(;t&&$r(t,1);)o=t.parentNode,i=tn(t)+1,t=t.nextSibling;if(t)n=t,r=0,s=_r(n);else{if(n=n.parentNode,n==e.dom)break;r=s=0}}}o&&jr(e,o,i)}(e)}function Br(e){let t=e.pmViewDesc;return t&&t.node&&t.node.isBlock}function jr(e,t,n){if(3!=t.nodeType){let e,r;(r=function(e,t){for(;e&&t==e.childNodes.length&&!un(e);)t=tn(e)+1,e=e.parentNode;for(;e&&t<e.childNodes.length;){let n=e.childNodes[t];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=n,t=0}}(t,n))?(t=r,n=0):(e=function(e,t){for(;e&&!t&&!un(e);)t=tn(e),e=e.parentNode;for(;e&&t;){let n=e.childNodes[t-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=(e=n).childNodes.length}}(t,n))&&(t=e,n=e.nodeValue.length)}let r=e.domSelection();if(!r)return;if(dn(r)){let e=document.createRange();e.setEnd(t,n),e.setStart(t,n),r.removeAllRanges(),r.addRange(e)}else r.extend&&r.extend(t,n);e.domObserver.setCurSelection();let{state:o}=e;setTimeout((()=>{e.state==o&&Cr(e)}),50)}function Hr(e,t){let n=e.state.doc.resolve(t);if(!xn&&!On&&n.parent.inlineContent){let r=e.coordsAtPos(t);if(t>n.start()){let n=e.coordsAtPos(t-1),o=(n.top+n.bottom)/2;if(o>r.top&&o<r.bottom&&Math.abs(n.left-r.left)>1)return n.left<r.left?"ltr":"rtl"}if(t<n.end()){let n=e.coordsAtPos(t+1),o=(n.top+n.bottom)/2;if(o>r.top&&o<r.bottom&&Math.abs(n.left-r.left)>1)return n.left>r.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(e.dom).direction?"rtl":"ltr"}function Vr(e,t,n){let r=e.state.selection;if(r instanceof It&&!r.empty||n.indexOf("s")>-1)return!1;if(Tn&&n.indexOf("m")>-1)return!1;let{$from:o,$to:i}=r;if(!o.parent.inlineContent||e.endOfTextblock(t<0?"up":"down")){let n=Lr(e.state,t);if(n&&n instanceof $t)return zr(e,n)}if(!o.parent.inlineContent){let n=t<0?o:i,s=r instanceof Bt?Pt.near(n,t):Pt.findFrom(n,t);return!!s&&zr(e,s)}return!1}function Ur(e,t){if(!(e.state.selection instanceof It))return!0;let{$head:n,$anchor:r,empty:o}=e.state.selection;if(!n.sameParent(r))return!0;if(!o)return!1;if(e.endOfTextblock(t>0?"forward":"backward"))return!0;let i=!n.textOffset&&(t<0?n.nodeBefore:n.nodeAfter);if(i&&!i.isText){let r=e.state.tr;return t<0?r.delete(n.pos-i.nodeSize,n.pos):r.delete(n.pos,n.pos+i.nodeSize),e.dispatch(r),!0}return!1}function Wr(e,t,n){e.domObserver.stop(),t.contentEditable=n,e.domObserver.start()}function qr(e,t){let n=t.keyCode,r=function(e){let t="";return e.ctrlKey&&(t+="c"),e.metaKey&&(t+="m"),e.altKey&&(t+="a"),e.shiftKey&&(t+="s"),t}(t);if(8==n||Tn&&72==n&&"c"==r)return Ur(e,-1)||Fr(e,-1);if(46==n&&!t.shiftKey||Tn&&68==n&&"c"==r)return Ur(e,1)||Fr(e,1);if(13==n||27==n)return!0;if(37==n||Tn&&66==n&&"c"==r){let t=37==n?"ltr"==Hr(e,e.state.selection.from)?-1:1:-1;return Ir(e,t,r)||Fr(e,t)}if(39==n||Tn&&70==n&&"c"==r){let t=39==n?"ltr"==Hr(e,e.state.selection.from)?1:-1:1;return Ir(e,t,r)||Fr(e,t)}return 38==n||Tn&&80==n&&"c"==r?Vr(e,-1,r)||Fr(e,-1):40==n||Tn&&78==n&&"c"==r?function(e){if(!En||e.state.selection.$head.parentOffset>0)return!1;let{focusNode:t,focusOffset:n}=e.domSelectionRange();if(t&&1==t.nodeType&&0==n&&t.firstChild&&"false"==t.firstChild.contentEditable){let n=t.firstChild;Wr(e,n,"true"),setTimeout((()=>Wr(e,n,"false")),20)}return!1}(e)||Vr(e,1,r)||Fr(e,1):r==(Tn?"m":"c")&&(66==n||73==n||89==n||90==n)}function Kr(e,t){e.someProp("transformCopied",(n=>{t=n(t,e)}));let n=[],{content:r,openStart:o,openEnd:i}=t;for(;o>1&&i>1&&1==r.childCount&&1==r.firstChild.childCount;){o--,i--;let e=r.firstChild;n.push(e.type.name,e.attrs!=e.type.defaultAttrs?e.attrs:null),r=e.content}let s=e.someProp("clipboardSerializer")||_e.fromSchema(e.state.schema),l=ro(),a=l.createElement("div");a.appendChild(s.serializeFragment(r,{document:l}));let c,u=a.firstChild,d=0;for(;u&&1==u.nodeType&&(c=to[u.nodeName.toLowerCase()]);){for(let e=c.length-1;e>=0;e--){let t=l.createElement(c[e]);for(;a.firstChild;)t.appendChild(a.firstChild);a.appendChild(t),d++}u=a.firstChild}return u&&1==u.nodeType&&u.setAttribute("data-pm-slice",`${o} ${i}${d?` -${d}`:""} ${JSON.stringify(n)}`),{dom:a,text:e.someProp("clipboardTextSerializer",(n=>n(t,e)))||t.content.textBetween(0,t.content.size,"\n\n"),slice:t}}function Jr(e,t,n,r,o){let i,s,l=o.parent.type.spec.code;if(!n&&!t)return null;let a=t&&(r||l||!n);if(a){if(e.someProp("transformPastedText",(n=>{t=n(t,l||r,e)})),l)return t?new H(I.from(e.state.schema.text(t.replace(/\r\n?/g,"\n"))),0,0):H.empty;let n=e.someProp("clipboardTextParser",(n=>n(t,o,r,e)));if(n)s=n;else{let n=o.marks(),{schema:r}=e.state,s=_e.fromSchema(r);i=document.createElement("div"),t.split(/(?:\r\n?|\n)+/).forEach((e=>{let t=i.appendChild(document.createElement("p"));e&&t.appendChild(s.serializeNode(r.text(e,n)))}))}}else e.someProp("transformPastedHTML",(t=>{n=t(n,e)})),i=function(e){let t=/^(\s*<meta [^>]*>)*/.exec(e);t&&(e=e.slice(t[0].length));let n,r=ro().createElement("div"),o=/<([a-z][^>\s]+)/i.exec(e);(n=o&&to[o[1].toLowerCase()])&&(e=n.map((e=>"<"+e+">")).join("")+e+n.map((e=>"</"+e+">")).reverse().join(""));if(r.innerHTML=function(e){let t=window.trustedTypes;if(!t)return e;oo||(oo=t.defaultPolicy||t.createPolicy("ProseMirrorClipboard",{createHTML:e=>e}));return oo.createHTML(e)}(e),n)for(let i=0;i<n.length;i++)r=r.querySelector(n[i])||r;return r}(n),An&&function(e){let t=e.querySelectorAll(xn?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<t.length;n++){let r=t[n];1==r.childNodes.length&&" "==r.textContent&&r.parentNode&&r.parentNode.replaceChild(e.ownerDocument.createTextNode(" "),r)}}(i);let c=i&&i.querySelector("[data-pm-slice]"),u=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(u&&u[3])for(let d=+u[3];d>0;d--){let e=i.firstChild;for(;e&&1!=e.nodeType;)e=e.nextSibling;if(!e)break;i=e}if(!s){let t=e.someProp("clipboardParser")||e.someProp("domParser")||Te.fromSchema(e.state.schema);s=t.parseSlice(i,{preserveWhitespace:!(!a&&!u),context:o,ruleFromNode:e=>"BR"!=e.nodeName||e.nextSibling||!e.parentNode||Qr.test(e.parentNode.nodeName)?null:{ignore:!0}})}if(u)s=function(e,t){if(!e.size)return e;let n,r=e.content.firstChild.type.schema;try{n=JSON.parse(t)}catch(l){return e}let{content:o,openStart:i,openEnd:s}=e;for(let a=n.length-2;a>=0;a-=2){let e=r.nodes[n[a]];if(!e||e.hasRequiredAttrs())break;o=I.from(e.create(n[a+1],o)),i++,s++}return new H(o,i,s)}(eo(s,+u[1],+u[2]),u[4]);else if(s=H.maxOpen(function(e,t){if(e.childCount<2)return e;for(let n=t.depth;n>=0;n--){let r,o=t.node(n).contentMatchAt(t.index(n)),i=[];if(e.forEach((e=>{if(!i)return;let t,n=o.findWrapping(e.type);if(!n)return i=null;if(t=i.length&&r.length&&Yr(n,r,e,i[i.length-1],0))i[i.length-1]=t;else{i.length&&(i[i.length-1]=Xr(i[i.length-1],r.length));let t=Gr(e,n);i.push(t),o=o.matchType(t.type),r=n}})),i)return I.from(i)}return e}(s.content,o),!0),s.openStart||s.openEnd){let e=0,t=0;for(let n=s.content.firstChild;e<s.openStart&&!n.type.spec.isolating;e++,n=n.firstChild);for(let n=s.content.lastChild;t<s.openEnd&&!n.type.spec.isolating;t++,n=n.lastChild);s=eo(s,e,t)}return e.someProp("transformPasted",(t=>{s=t(s,e)})),s}const Qr=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Gr(e,t,n=0){for(let r=t.length-1;r>=n;r--)e=t[r].create(null,I.from(e));return e}function Yr(e,t,n,r,o){if(o<e.length&&o<t.length&&e[o]==t[o]){let i=Yr(e,t,n,r.lastChild,o+1);if(i)return r.copy(r.content.replaceChild(r.childCount-1,i));if(r.contentMatchAt(r.childCount).matchType(o==e.length-1?n.type:e[o+1]))return r.copy(r.content.append(I.from(Gr(n,e,o+1))))}}function Xr(e,t){if(0==t)return e;let n=e.content.replaceChild(e.childCount-1,Xr(e.lastChild,t-1)),r=e.contentMatchAt(e.childCount).fillBefore(I.empty,!0);return e.copy(n.append(r))}function Zr(e,t,n,r,o,i){let s=t<0?e.firstChild:e.lastChild,l=s.content;return e.childCount>1&&(i=0),o<r-1&&(l=Zr(l,t,n,r,o+1,i)),o>=n&&(l=t<0?s.contentMatchAt(0).fillBefore(l,i<=o).append(l):l.append(s.contentMatchAt(s.childCount).fillBefore(I.empty,!0))),e.replaceChild(t<0?0:e.childCount-1,s.copy(l))}function eo(e,t,n){return t<e.openStart&&(e=new H(Zr(e.content,-1,t,e.openStart,0,e.openEnd),t,e.openEnd)),n<e.openEnd&&(e=new H(Zr(e.content,1,n,e.openEnd,0,0),e.openStart,n)),e}const to={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let no=null;function ro(){return no||(no=document.implementation.createHTMLDocument("title"))}let oo=null;const io={},so={},lo={touchstart:!0,touchmove:!0};class ao{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function co(e,t){e.input.lastSelectionOrigin=t,e.input.lastSelectionTime=Date.now()}function uo(e){e.someProp("handleDOMEvents",(t=>{for(let n in t)e.input.eventHandlers[n]||e.dom.addEventListener(n,e.input.eventHandlers[n]=t=>fo(e,t))}))}function fo(e,t){return e.someProp("handleDOMEvents",(n=>{let r=n[t.type];return!!r&&(r(e,t)||t.defaultPrevented)}))}function ho(e,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let n=t.target;n!=e.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(t))return!1;return!0}function po(e){return{left:e.clientX,top:e.clientY}}function mo(e,t,n,r,o){if(-1==r)return!1;let i=e.state.doc.resolve(r);for(let s=i.depth+1;s>0;s--)if(e.someProp(t,(t=>s>i.depth?t(e,n,i.nodeAfter,i.before(s),o,!0):t(e,n,i.node(s),i.before(s),o,!1))))return!0;return!1}function go(e,t,n){if(e.focused||e.focus(),e.state.selection.eq(t))return;let r=e.state.tr.setSelection(t);r.setMeta("pointer",!0),e.dispatch(r)}function yo(e,t,n,r,o){return mo(e,"handleClickOn",t,n,r)||e.someProp("handleClick",(n=>n(e,t,r)))||(o?function(e,t){if(-1==t)return!1;let n,r,o=e.state.selection;o instanceof $t&&(n=o.node);let i=e.state.doc.resolve(t);for(let s=i.depth+1;s>0;s--){let e=s>i.depth?i.nodeAfter:i.node(s);if($t.isSelectable(e)){r=n&&o.$from.depth>0&&s>=o.$from.depth&&i.before(o.$from.depth+1)==o.$from.pos?i.before(o.$from.depth):i.before(s);break}}return null!=r&&(go(e,$t.create(e.state.doc,r)),!0)}(e,n):function(e,t){if(-1==t)return!1;let n=e.state.doc.resolve(t),r=n.nodeAfter;return!!(r&&r.isAtom&&$t.isSelectable(r))&&(go(e,new $t(n)),!0)}(e,n))}function vo(e,t,n,r){return mo(e,"handleDoubleClickOn",t,n,r)||e.someProp("handleDoubleClick",(n=>n(e,t,r)))}function bo(e,t,n,r){return mo(e,"handleTripleClickOn",t,n,r)||e.someProp("handleTripleClick",(n=>n(e,t,r)))||function(e,t,n){if(0!=n.button)return!1;let r=e.state.doc;if(-1==t)return!!r.inlineContent&&(go(e,It.create(r,0,r.content.size)),!0);let o=r.resolve(t);for(let i=o.depth+1;i>0;i--){let t=i>o.depth?o.nodeAfter:o.node(i),n=o.before(i);if(t.inlineContent)go(e,It.create(r,n+1,n+1+t.content.size));else{if(!$t.isSelectable(t))continue;go(e,$t.create(r,n))}return!0}}(e,n,r)}function ko(e){return Oo(e)}so.keydown=(e,t)=>{let n=t;if(e.input.shiftKey=16==n.keyCode||n.shiftKey,!xo(e,n)&&(e.input.lastKeyCode=n.keyCode,e.input.lastKeyCodeTime=Date.now(),!Nn||!xn||13!=n.keyCode))if(229!=n.keyCode&&e.domObserver.forceFlush(),!Mn||13!=n.keyCode||n.ctrlKey||n.altKey||n.metaKey)e.someProp("handleKeyDown",(t=>t(e,n)))||qr(e,n)?n.preventDefault():co(e,"key");else{let t=Date.now();e.input.lastIOSEnter=t,e.input.lastIOSEnterFallbackTimeout=setTimeout((()=>{e.input.lastIOSEnter==t&&(e.someProp("handleKeyDown",(t=>t(e,fn(13,"Enter")))),e.input.lastIOSEnter=0)}),200)}},so.keyup=(e,t)=>{16==t.keyCode&&(e.input.shiftKey=!1)},so.keypress=(e,t)=>{let n=t;if(xo(e,n)||!n.charCode||n.ctrlKey&&!n.altKey||Tn&&n.metaKey)return;if(e.someProp("handleKeyPress",(t=>t(e,n))))return void n.preventDefault();let r=e.state.selection;if(!(r instanceof It&&r.$from.sameParent(r.$to))){let t=String.fromCharCode(n.charCode);/[\r\n]/.test(t)||e.someProp("handleTextInput",(n=>n(e,r.$from.pos,r.$to.pos,t)))||e.dispatch(e.state.tr.insertText(t).scrollIntoView()),n.preventDefault()}};const wo=Tn?"metaKey":"ctrlKey";io.mousedown=(e,t)=>{let n=t;e.input.shiftKey=n.shiftKey;let r=ko(e),o=Date.now(),i="singleClick";o-e.input.lastClick.time<500&&function(e,t){let n=t.x-e.clientX,r=t.y-e.clientY;return n*n+r*r<100}(n,e.input.lastClick)&&!n[wo]&&("singleClick"==e.input.lastClick.type?i="doubleClick":"doubleClick"==e.input.lastClick.type&&(i="tripleClick")),e.input.lastClick={time:o,x:n.clientX,y:n.clientY,type:i};let s=e.posAtCoords(po(n));s&&("singleClick"==i?(e.input.mouseDown&&e.input.mouseDown.done(),e.input.mouseDown=new So(e,s,n,!!r)):("doubleClick"==i?vo:bo)(e,s.pos,s.inside,n)?n.preventDefault():co(e,"pointer"))};class So{constructor(e,t,n,r){let o,i;if(this.view=e,this.pos=t,this.event=n,this.flushed=r,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[wo],this.allowDefault=n.shiftKey,t.inside>-1)o=e.state.doc.nodeAt(t.inside),i=t.inside;else{let n=e.state.doc.resolve(t.pos);o=n.parent,i=n.depth?n.before():0}const s=r?null:n.target,l=s?e.docView.nearestDesc(s,!0):null;this.target=l&&1==l.dom.nodeType?l.dom:null;let{selection:a}=e.state;(0==n.button&&o.type.spec.draggable&&!1!==o.type.spec.selectable||a instanceof $t&&a.from<=i&&a.to>i)&&(this.mightDrag={node:o,pos:i,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!wn||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout((()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")}),20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),co(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout((()=>Cr(this.view))),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(po(e))),this.updateAllowDefault(e),this.allowDefault||!t?co(this.view,"pointer"):yo(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():0==e.button&&(this.flushed||En&&this.mightDrag&&!this.mightDrag.node.isAtom||xn&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(go(this.view,Pt.near(this.view.state.doc.resolve(t.pos))),e.preventDefault()):co(this.view,"pointer")}move(e){this.updateAllowDefault(e),co(this.view,"pointer"),0==e.buttons&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function xo(e,t){return!!e.composing||!!(En&&Math.abs(t.timeStamp-e.input.compositionEndedAt)<500)&&(e.input.compositionEndedAt=-2e8,!0)}io.touchstart=e=>{e.input.lastTouch=Date.now(),ko(e),co(e,"pointer")},io.touchmove=e=>{e.input.lastTouch=Date.now(),co(e,"pointer")},io.contextmenu=e=>ko(e);const Co=Nn?5e3:-1;function Eo(e,t){clearTimeout(e.input.composingTimeout),t>-1&&(e.input.composingTimeout=setTimeout((()=>Oo(e)),t))}function Mo(e){for(e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=function(){let e=document.createEvent("Event");return e.initEvent("event",!0,!0),e.timeStamp}());e.input.compositionNodes.length>0;)e.input.compositionNodes.pop().markParentsDirty()}function To(e){let t=e.domSelectionRange();if(!t.focusNode)return null;let n=function(e,t){for(;;){if(3==e.nodeType&&t)return e;if(1==e.nodeType&&t>0){if("false"==e.contentEditable)return null;t=cn(e=e.childNodes[t-1])}else{if(!e.parentNode||un(e))return null;t=tn(e),e=e.parentNode}}}(t.focusNode,t.focusOffset),r=function(e,t){for(;;){if(3==e.nodeType&&t<e.nodeValue.length)return e;if(1==e.nodeType&&t<e.childNodes.length){if("false"==e.contentEditable)return null;e=e.childNodes[t],t=0}else{if(!e.parentNode||un(e))return null;t=tn(e)+1,e=e.parentNode}}}(t.focusNode,t.focusOffset);if(n&&r&&n!=r){let t=r.pmViewDesc,o=e.domObserver.lastChangedTextNode;if(n==o||r==o)return o;if(!t||!t.isText(r.nodeValue))return r;if(e.input.compositionNode==r){let e=n.pmViewDesc;if(e&&e.isText(n.nodeValue))return r}}return n||r}function Oo(e,t=!1){if(!(Nn&&e.domObserver.flushingSoon>=0)){if(e.domObserver.forceFlush(),Mo(e),t||e.docView&&e.docView.dirty){let n=Sr(e);return n&&!n.eq(e.state.selection)?e.dispatch(e.state.tr.setSelection(n)):!e.markCursor&&!t||e.state.selection.empty?e.updateState(e.state):e.dispatch(e.state.tr.deleteSelection()),!0}return!1}}so.compositionstart=so.compositionupdate=e=>{if(!e.composing){e.domObserver.flush();let{state:t}=e,n=t.selection.$to;if(t.selection instanceof It&&(t.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some((e=>!1===e.type.spec.inclusive))))e.markCursor=e.state.storedMarks||n.marks(),Oo(e,!0),e.markCursor=null;else if(Oo(e,!t.selection.empty),wn&&t.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let t=e.domSelectionRange();for(let n=t.focusNode,r=t.focusOffset;n&&1==n.nodeType&&0!=r;){let t=r<0?n.lastChild:n.childNodes[r-1];if(!t)break;if(3==t.nodeType){let n=e.domSelection();n&&n.collapse(t,t.nodeValue.length);break}n=t,r=-1}}e.input.composing=!0}Eo(e,Co)},so.compositionend=(e,t)=>{e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=t.timeStamp,e.input.compositionPendingChanges=e.domObserver.pendingRecords().length?e.input.compositionID:0,e.input.compositionNode=null,e.input.compositionPendingChanges&&Promise.resolve().then((()=>e.domObserver.flush())),e.input.compositionID++,Eo(e,20))};const No=bn&&kn<15||Mn&&Dn<604;function Ao(e,t,n,r,o){let i=Jr(e,t,n,r,e.state.selection.$from);if(e.someProp("handlePaste",(t=>t(e,o,i||H.empty))))return!0;if(!i)return!1;let s=function(e){return 0==e.openStart&&0==e.openEnd&&1==e.content.childCount?e.content.firstChild:null}(i),l=s?e.state.tr.replaceSelectionWith(s,r):e.state.tr.replaceSelection(i);return e.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Do(e){let t=e.getData("text/plain")||e.getData("Text");if(t)return t;let n=e.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}io.copy=so.cut=(e,t)=>{let n=t,r=e.state.selection,o="cut"==n.type;if(r.empty)return;let i=No?null:n.clipboardData,s=r.content(),{dom:l,text:a}=Kr(e,s);i?(n.preventDefault(),i.clearData(),i.setData("text/html",l.innerHTML),i.setData("text/plain",a)):function(e,t){if(!e.dom.parentNode)return;let n=e.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(t),n.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),o=document.createRange();o.selectNodeContents(t),e.dom.blur(),r.removeAllRanges(),r.addRange(o),setTimeout((()=>{n.parentNode&&n.parentNode.removeChild(n),e.focus()}),50)}(e,l),o&&e.dispatch(e.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},so.paste=(e,t)=>{let n=t;if(e.composing&&!Nn)return;let r=No?null:n.clipboardData,o=e.input.shiftKey&&45!=e.input.lastKeyCode;r&&Ao(e,Do(r),r.getData("text/html"),o,n)?n.preventDefault():function(e,t){if(!e.dom.parentNode)return;let n=e.input.shiftKey||e.state.selection.$from.parent.type.spec.code,r=e.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let o=e.input.shiftKey&&45!=e.input.lastKeyCode;setTimeout((()=>{e.focus(),r.parentNode&&r.parentNode.removeChild(r),n?Ao(e,r.value,null,o,t):Ao(e,r.textContent,r.innerHTML,o,t)}),50)}(e,n)};class Po{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}const Ro=Tn?"altKey":"ctrlKey";function Lo(e,t){let n=e.someProp("dragCopies",(e=>!e(t)));return null!=n?n:!t[Ro]}io.dragstart=(e,t)=>{let n=t,r=e.input.mouseDown;if(r&&r.done(),!n.dataTransfer)return;let o,i=e.state.selection,s=i.empty?null:e.posAtCoords(po(n));if(s&&s.pos>=i.from&&s.pos<=(i instanceof $t?i.to-1:i.to));else if(r&&r.mightDrag)o=$t.create(e.state.doc,r.mightDrag.pos);else if(n.target&&1==n.target.nodeType){let t=e.docView.nearestDesc(n.target,!0);t&&t.node.type.spec.draggable&&t!=e.docView&&(o=$t.create(e.state.doc,t.posBefore))}let l=(o||e.state.selection).content(),{dom:a,text:c,slice:u}=Kr(e,l);(!n.dataTransfer.files.length||!xn||Cn>120)&&n.dataTransfer.clearData(),n.dataTransfer.setData(No?"Text":"text/html",a.innerHTML),n.dataTransfer.effectAllowed="copyMove",No||n.dataTransfer.setData("text/plain",c),e.dragging=new Po(u,Lo(e,n),o)},io.dragend=e=>{let t=e.dragging;window.setTimeout((()=>{e.dragging==t&&(e.dragging=null)}),50)},so.dragover=so.dragenter=(e,t)=>t.preventDefault(),so.drop=(e,t)=>{let n=t,r=e.dragging;if(e.dragging=null,!n.dataTransfer)return;let o=e.posAtCoords(po(n));if(!o)return;let i=e.state.doc.resolve(o.pos),s=r&&r.slice;s?e.someProp("transformPasted",(t=>{s=t(s,e)})):s=Jr(e,Do(n.dataTransfer),No?null:n.dataTransfer.getData("text/html"),!1,i);let l=!(!r||!Lo(e,n));if(e.someProp("handleDrop",(t=>t(e,n,s||H.empty,l))))return void n.preventDefault();if(!s)return;n.preventDefault();let a=s?gt(e.state.doc,i.pos,s):i.pos;null==a&&(a=i.pos);let c=e.state.tr;if(l){let{node:e}=r;e?e.replace(c):c.deleteSelection()}let u=c.mapping.map(a),d=0==s.openStart&&0==s.openEnd&&1==s.content.childCount,f=c.doc;if(d?c.replaceRangeWith(u,u,s.content.firstChild):c.replaceRange(u,u,s),c.doc.eq(f))return;let h=c.doc.resolve(u);if(d&&$t.isSelectable(s.content.firstChild)&&h.nodeAfter&&h.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new $t(h));else{let t=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach(((e,n,r,o)=>t=o)),c.setSelection(Dr(e,h,c.doc.resolve(t)))}e.focus(),e.dispatch(c.setMeta("uiEvent","drop"))},io.focus=e=>{e.input.lastFocus=Date.now(),e.focused||(e.domObserver.stop(),e.dom.classList.add("ProseMirror-focused"),e.domObserver.start(),e.focused=!0,setTimeout((()=>{e.docView&&e.hasFocus()&&!e.domObserver.currentSelection.eq(e.domSelectionRange())&&Cr(e)}),20))},io.blur=(e,t)=>{let n=t;e.focused&&(e.domObserver.stop(),e.dom.classList.remove("ProseMirror-focused"),e.domObserver.start(),n.relatedTarget&&e.dom.contains(n.relatedTarget)&&e.domObserver.currentSelection.clear(),e.focused=!1)},io.beforeinput=(e,t)=>{if(xn&&Nn&&"deleteContentBackward"==t.inputType){e.domObserver.flushSoon();let{domChangeCount:t}=e.input;setTimeout((()=>{if(e.input.domChangeCount!=t)return;if(e.dom.blur(),e.focus(),e.someProp("handleKeyDown",(t=>t(e,fn(8,"Backspace")))))return;let{$cursor:n}=e.state.selection;n&&n.pos>0&&e.dispatch(e.state.tr.delete(n.pos-1,n.pos).scrollIntoView())}),50)}};for(let os in so)io[os]=so[os];function zo(e,t){if(e==t)return!0;for(let n in e)if(e[n]!==t[n])return!1;for(let n in t)if(!(n in e))return!1;return!0}class Io{constructor(e,t){this.toDOM=e,this.spec=t||jo,this.side=this.spec.side||0}map(e,t,n,r){let{pos:o,deleted:i}=e.mapResult(t.from+r,this.side<0?-1:1);return i?null:new Fo(o-n,o-n,this)}valid(){return!0}eq(e){return this==e||e instanceof Io&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&zo(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class _o{constructor(e,t){this.attrs=e,this.spec=t||jo}map(e,t,n,r){let o=e.map(t.from+r,this.spec.inclusiveStart?-1:1)-n,i=e.map(t.to+r,this.spec.inclusiveEnd?1:-1)-n;return o>=i?null:new Fo(o,i,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof _o&&zo(this.attrs,e.attrs)&&zo(this.spec,e.spec)}static is(e){return e.type instanceof _o}destroy(){}}class $o{constructor(e,t){this.attrs=e,this.spec=t||jo}map(e,t,n,r){let o=e.mapResult(t.from+r,1);if(o.deleted)return null;let i=e.mapResult(t.to+r,-1);return i.deleted||i.pos<=o.pos?null:new Fo(o.pos-n,i.pos-n,this)}valid(e,t){let n,{index:r,offset:o}=e.content.findIndex(t.from);return o==t.from&&!(n=e.child(r)).isText&&o+n.nodeSize==t.to}eq(e){return this==e||e instanceof $o&&zo(this.attrs,e.attrs)&&zo(this.spec,e.spec)}destroy(){}}class Fo{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new Fo(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new Fo(e,e,new Io(t,n))}static inline(e,t,n,r){return new Fo(e,t,new _o(n,r))}static node(e,t,n,r){return new Fo(e,t,new $o(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof _o}get widget(){return this.type instanceof Io}}const Bo=[],jo={};class Ho{constructor(e,t){this.local=e.length?e:Bo,this.children=t.length?t:Bo}static create(e,t){return t.length?Jo(t,e,0,jo):Vo}find(e,t,n){let r=[];return this.findInner(null==e?0:e,null==t?1e9:t,r,0,n),r}findInner(e,t,n,r,o){for(let i=0;i<this.local.length;i++){let s=this.local[i];s.from<=t&&s.to>=e&&(!o||o(s.spec))&&n.push(s.copy(s.from+r,s.to+r))}for(let i=0;i<this.children.length;i+=3)if(this.children[i]<t&&this.children[i+1]>e){let s=this.children[i]+1;this.children[i+2].findInner(e-s,t-s,n,r+s,o)}}map(e,t,n){return this==Vo||0==e.maps.length?this:this.mapInner(e,t,0,0,n||jo)}mapInner(e,t,n,r,o){let i;for(let s=0;s<this.local.length;s++){let l=this.local[s].map(e,n,r);l&&l.type.valid(t,l)?(i||(i=[])).push(l):o.onRemove&&o.onRemove(this.local[s].spec)}return this.children.length?function(e,t,n,r,o,i,s){let l=e.slice();for(let c=0,u=i;c<n.maps.length;c++){let e=0;n.maps[c].forEach(((t,n,r,o)=>{let i=o-r-(n-t);for(let s=0;s<l.length;s+=3){let r=l[s+1];if(r<0||t>r+u-e)continue;let o=l[s]+u-e;n>=o?l[s+1]=t<=o?-2:-1:t>=u&&i&&(l[s]+=i,l[s+1]+=i)}e+=i})),u=n.maps[c].map(u,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(-2==l[c+1]){a=!0,l[c+1]=-1;continue}let t=n.map(e[c]+i),u=t-o;if(u<0||u>=r.content.size){a=!0;continue}let d=n.map(e[c+1]+i,-1)-o,{index:f,offset:h}=r.content.findIndex(u),p=r.maybeChild(f);if(p&&h==u&&h+p.nodeSize==d){let r=l[c+2].mapInner(n,p,t+1,e[c]+i+1,s);r!=Vo?(l[c]=u,l[c+1]=d,l[c+2]=r):(l[c+1]=-2,a=!0)}else a=!0}if(a){let a=function(e,t,n,r,o,i,s){function l(e,t){for(let i=0;i<e.local.length;i++){let l=e.local[i].map(r,o,t);l?n.push(l):s.onRemove&&s.onRemove(e.local[i].spec)}for(let n=0;n<e.children.length;n+=3)l(e.children[n+2],e.children[n]+t+1)}for(let a=0;a<e.length;a+=3)-1==e[a+1]&&l(e[a+2],t[a]+i+1);return n}(l,e,t,n,o,i,s),c=Jo(a,r,0,s);t=c.local;for(let e=0;e<l.length;e+=3)l[e+1]<0&&(l.splice(e,3),e-=3);for(let e=0,t=0;e<c.children.length;e+=3){let n=c.children[e];for(;t<l.length&&l[t]<n;)t+=3;l.splice(t,0,c.children[e],c.children[e+1],c.children[e+2])}}return new Ho(t.sort(Qo),l)}(this.children,i||[],e,t,n,r,o):i?new Ho(i.sort(Qo),Bo):Vo}add(e,t){return t.length?this==Vo?Ho.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let r,o=0;e.forEach(((e,i)=>{let s,l=i+n;if(s=qo(t,e,l)){for(r||(r=this.children.slice());o<r.length&&r[o]<i;)o+=3;r[o]==i?r[o+2]=r[o+2].addInner(e,s,l+1):r.splice(o,0,i,i+e.nodeSize,Jo(s,e,l+1,jo)),o+=3}}));let i=Wo(o?Ko(t):t,-n);for(let s=0;s<i.length;s++)i[s].type.valid(e,i[s])||i.splice(s--,1);return new Ho(i.length?this.local.concat(i).sort(Qo):this.local,r||this.children)}remove(e){return 0==e.length||this==Vo?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,r=this.local;for(let o=0;o<n.length;o+=3){let r,i=n[o]+t,s=n[o+1]+t;for(let t,n=0;n<e.length;n++)(t=e[n])&&t.from>i&&t.to<s&&(e[n]=null,(r||(r=[])).push(t));if(!r)continue;n==this.children&&(n=this.children.slice());let l=n[o+2].removeInner(r,i+1);l!=Vo?n[o+2]=l:(n.splice(o,3),o-=3)}if(r.length)for(let o,i=0;i<e.length;i++)if(o=e[i])for(let e=0;e<r.length;e++)r[e].eq(o,t)&&(r==this.local&&(r=this.local.slice()),r.splice(e--,1));return n==this.children&&r==this.local?this:r.length||n.length?new Ho(r,n):Vo}forChild(e,t){if(this==Vo)return this;if(t.isLeaf)return Ho.empty;let n,r;for(let s=0;s<this.children.length;s+=3)if(this.children[s]>=e){this.children[s]==e&&(n=this.children[s+2]);break}let o=e+1,i=o+t.content.size;for(let s=0;s<this.local.length;s++){let e=this.local[s];if(e.from<i&&e.to>o&&e.type instanceof _o){let t=Math.max(o,e.from)-o,n=Math.min(i,e.to)-o;t<n&&(r||(r=[])).push(e.copy(t,n))}}if(r){let e=new Ho(r.sort(Qo),Bo);return n?new Uo([e,n]):e}return n||Vo}eq(e){if(this==e)return!0;if(!(e instanceof Ho)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return Go(this.localsInner(e))}localsInner(e){if(this==Vo)return Bo;if(e.inlineContent||!this.local.some(_o.is))return this.local;let t=[];for(let n=0;n<this.local.length;n++)this.local[n].type instanceof _o||t.push(this.local[n]);return t}forEachSet(e){e(this)}}Ho.empty=new Ho([],[]),Ho.removeOverlap=Go;const Vo=Ho.empty;class Uo{constructor(e){this.members=e}map(e,t){const n=this.members.map((n=>n.map(e,t,jo)));return Uo.from(n)}forChild(e,t){if(t.isLeaf)return Ho.empty;let n=[];for(let r=0;r<this.members.length;r++){let o=this.members[r].forChild(e,t);o!=Vo&&(o instanceof Uo?n=n.concat(o.members):n.push(o))}return Uo.from(n)}eq(e){if(!(e instanceof Uo)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let r=0;r<this.members.length;r++){let o=this.members[r].localsInner(e);if(o.length)if(t){n&&(t=t.slice(),n=!1);for(let e=0;e<o.length;e++)t.push(o[e])}else t=o}return t?Go(n?t:t.sort(Qo)):Bo}static from(e){switch(e.length){case 0:return Vo;case 1:return e[0];default:return new Uo(e.every((e=>e instanceof Ho))?e:e.reduce(((e,t)=>e.concat(t instanceof Ho?t:t.members)),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function Wo(e,t){if(!t||!e.length)return e;let n=[];for(let r=0;r<e.length;r++){let o=e[r];n.push(new Fo(o.from+t,o.to+t,o.type))}return n}function qo(e,t,n){if(t.isLeaf)return null;let r=n+t.nodeSize,o=null;for(let i,s=0;s<e.length;s++)(i=e[s])&&i.from>n&&i.to<r&&((o||(o=[])).push(i),e[s]=null);return o}function Ko(e){let t=[];for(let n=0;n<e.length;n++)null!=e[n]&&t.push(e[n]);return t}function Jo(e,t,n,r){let o=[],i=!1;t.forEach(((t,s)=>{let l=qo(e,t,s+n);if(l){i=!0;let e=Jo(l,t,n+s+1,r);e!=Vo&&o.push(s,s+t.nodeSize,e)}}));let s=Wo(i?Ko(e):e,-n).sort(Qo);for(let l=0;l<s.length;l++)s[l].type.valid(t,s[l])||(r.onRemove&&r.onRemove(s[l].spec),s.splice(l--,1));return s.length||o.length?new Ho(s,o):Vo}function Qo(e,t){return e.from-t.from||e.to-t.to}function Go(e){let t=e;for(let n=0;n<t.length-1;n++){let r=t[n];if(r.from!=r.to)for(let o=n+1;o<t.length;o++){let i=t[o];if(i.from!=r.from){i.from<r.to&&(t==e&&(t=e.slice()),t[n]=r.copy(r.from,i.from),Yo(t,o,r.copy(i.from,r.to)));break}i.to!=r.to&&(t==e&&(t=e.slice()),t[o]=i.copy(i.from,r.to),Yo(t,o+1,i.copy(r.to,i.to)))}}return t}function Yo(e,t,n){for(;t<e.length&&Qo(n,e[t])>0;)t++;e.splice(t,0,n)}function Xo(e){let t=[];return e.someProp("decorations",(n=>{let r=n(e.state);r&&r!=Vo&&t.push(r)})),e.cursorWrapper&&t.push(Ho.create(e.state.doc,[e.cursorWrapper.deco])),Uo.from(t)}const Zo={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},ei=bn&&kn<=11;class ti{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class ni{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new ti,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver((e=>{for(let t=0;t<e.length;t++)this.queue.push(e[t]);bn&&kn<=11&&e.some((e=>"childList"==e.type&&e.removedNodes.length||"characterData"==e.type&&e.oldValue.length>e.target.nodeValue.length))?this.flushSoon():this.flush()})),ei&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout((()=>{this.flushingSoon=-1,this.flush()}),20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,Zo)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout((()=>this.flush()),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout((()=>this.suppressingSelectionUpdates=!1),50)}onSelectionChange(){if(Pr(this.view)){if(this.suppressingSelectionUpdates)return Cr(this.view);if(bn&&kn<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&sn(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t,n=new Set;for(let o=e.focusNode;o;o=nn(o))n.add(o);for(let o=e.anchorNode;o;o=nn(o))if(n.has(o)){t=o;break}let r=t&&this.view.docView.nearestDesc(t);return r&&r.ignoreMutation({type:"selection",target:3==t.nodeType?t.parentNode:t})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let n=e.domSelectionRange(),r=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&Pr(e)&&!this.ignoreSelectionChange(n),o=-1,i=-1,s=!1,l=[];if(e.editable)for(let c=0;c<t.length;c++){let e=this.registerMutation(t[c],l);e&&(o=o<0?e.from:Math.min(e.from,o),i=i<0?e.to:Math.max(e.to,i),e.typeOver&&(s=!0))}if(wn&&l.length){let t=l.filter((e=>"BR"==e.nodeName));if(2==t.length){let[e,n]=t;e.parentNode&&e.parentNode.parentNode==n.parentNode?n.remove():e.remove()}else{let{focusNode:n}=this.currentSelection;for(let r of t){let t=r.parentNode;!t||"LI"!=t.nodeName||n&&si(e,n)==t||r.remove()}}}let a=null;o<0&&r&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&dn(n)&&(a=Sr(e))&&a.eq(Pt.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,Cr(e),this.currentSelection.set(n),e.scrollToSelection()):(o>-1||r)&&(o>-1&&(e.docView.markDirty(o,i),function(e){if(ri.has(e))return;if(ri.set(e,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(e.dom).whiteSpace)){if(e.requiresGeckoHackNode=wn,oi)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),oi=!0}}(e)),this.handleDOMChange(o,i,s,l),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||Cr(e),this.currentSelection.set(n))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if("attributes"==e.type&&(n==this.view.docView||"contenteditable"==e.attributeName||"style"==e.attributeName&&!e.oldValue&&!e.target.getAttribute("style")))return null;if(!n||n.ignoreMutation(e))return null;if("childList"==e.type){for(let n=0;n<e.addedNodes.length;n++){let r=e.addedNodes[n];t.push(r),3==r.nodeType&&(this.lastChangedTextNode=r)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let r=e.previousSibling,o=e.nextSibling;if(bn&&kn<=11&&e.addedNodes.length)for(let t=0;t<e.addedNodes.length;t++){let{previousSibling:n,nextSibling:i}=e.addedNodes[t];(!n||Array.prototype.indexOf.call(e.addedNodes,n)<0)&&(r=n),(!i||Array.prototype.indexOf.call(e.addedNodes,i)<0)&&(o=i)}let i=r&&r.parentNode==e.target?tn(r)+1:0,s=n.localPosFromDOM(e.target,i,-1),l=o&&o.parentNode==e.target?tn(o):e.target.childNodes.length;return{from:s,to:n.localPosFromDOM(e.target,l,1)}}return"attributes"==e.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=e.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let ri=new WeakMap,oi=!1;function ii(e,t){let n=t.startContainer,r=t.startOffset,o=t.endContainer,i=t.endOffset,s=e.domAtPos(e.state.selection.anchor);return sn(s.node,s.offset,o,i)&&([n,r,o,i]=[o,i,n,r]),{anchorNode:n,anchorOffset:r,focusNode:o,focusOffset:i}}function si(e,t){for(let n=t.parentNode;n&&n!=e.dom;n=n.parentNode){let t=e.docView.nearestDesc(n,!0);if(t&&t.node.isBlock)return n}return null}function li(e){let t=e.pmViewDesc;if(t)return t.parseRule();if("BR"==e.nodeName&&e.parentNode){if(En&&/^(ul|ol)$/i.test(e.parentNode.nodeName)){let e=document.createElement("div");return e.appendChild(document.createElement("li")),{skip:e}}if(e.parentNode.lastChild==e||En&&/^(tr|table)$/i.test(e.parentNode.nodeName))return{ignore:!0}}else if("IMG"==e.nodeName&&e.getAttribute("mark-placeholder"))return{ignore:!0};return null}const ai=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function ci(e,t,n,r,o){let i=e.input.compositionPendingChanges||(e.composing?e.input.compositionID:0);if(e.input.compositionPendingChanges=0,t<0){let t=e.input.lastSelectionTime>Date.now()-50?e.input.lastSelectionOrigin:null,n=Sr(e,t);if(n&&!e.state.selection.eq(n)){if(xn&&Nn&&13===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime&&e.someProp("handleKeyDown",(t=>t(e,fn(13,"Enter")))))return;let r=e.state.tr.setSelection(n);"pointer"==t?r.setMeta("pointer",!0):"key"==t&&r.scrollIntoView(),i&&r.setMeta("composition",i),e.dispatch(r)}return}let s=e.state.doc.resolve(t),l=s.sharedDepth(n);t=s.before(l+1),n=e.state.doc.resolve(n).after(l+1);let a,c,u=e.state.selection,d=function(e,t,n){let r,{node:o,fromOffset:i,toOffset:s,from:l,to:a}=e.docView.parseRange(t,n),c=e.domSelectionRange(),u=c.anchorNode;if(u&&e.dom.contains(1==u.nodeType?u:u.parentNode)&&(r=[{node:u,offset:c.anchorOffset}],dn(c)||r.push({node:c.focusNode,offset:c.focusOffset})),xn&&8===e.input.lastKeyCode)for(let g=s;g>i;g--){let e=o.childNodes[g-1],t=e.pmViewDesc;if("BR"==e.nodeName&&!t){s=g;break}if(!t||t.size)break}let d=e.state.doc,f=e.someProp("domParser")||Te.fromSchema(e.state.schema),h=d.resolve(l),p=null,m=f.parse(o,{topNode:h.parent,topMatch:h.parent.contentMatchAt(h.index()),topOpen:!0,from:i,to:s,preserveWhitespace:"pre"!=h.parent.type.whitespace||"full",findPositions:r,ruleFromNode:li,context:h});if(r&&null!=r[0].pos){let e=r[0].pos,t=r[1]&&r[1].pos;null==t&&(t=e),p={anchor:e+l,head:t+l}}return{doc:m,sel:p,from:l,to:a}}(e,t,n),f=e.state.doc,h=f.slice(d.from,d.to);8===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime?(a=e.state.selection.to,c="end"):(a=e.state.selection.from,c="start"),e.input.lastKeyCode=null;let p=function(e,t,n,r,o){let i=e.findDiffStart(t,n);if(null==i)return null;let{a:s,b:l}=e.findDiffEnd(t,n+e.size,n+t.size);if("end"==o){r-=s+Math.max(0,i-Math.min(s,l))-i}if(s<i&&e.size<t.size){let e=r<=i&&r>=s?i-r:0;i-=e,i&&i<t.size&&fi(t.textBetween(i-1,i+1))&&(i+=e?1:-1),l=i+(l-s),s=i}else if(l<i){let t=r<=i&&r>=l?i-r:0;i-=t,i&&i<e.size&&fi(e.textBetween(i-1,i+1))&&(i+=t?1:-1),s=i+(s-l),l=i}return{start:i,endA:s,endB:l}}(h.content,d.doc.content,d.from,a,c);if(p&&e.input.domChangeCount++,(Mn&&e.input.lastIOSEnter>Date.now()-225||Nn)&&o.some((e=>1==e.nodeType&&!ai.test(e.nodeName)))&&(!p||p.endA>=p.endB)&&e.someProp("handleKeyDown",(t=>t(e,fn(13,"Enter")))))return void(e.input.lastIOSEnter=0);if(!p){if(!(r&&u instanceof It&&!u.empty&&u.$head.sameParent(u.$anchor))||e.composing||d.sel&&d.sel.anchor!=d.sel.head){if(d.sel){let t=ui(e,e.state.doc,d.sel);if(t&&!t.eq(e.state.selection)){let n=e.state.tr.setSelection(t);i&&n.setMeta("composition",i),e.dispatch(n)}}return}p={start:u.from,endA:u.to,endB:u.to}}e.state.selection.from<e.state.selection.to&&p.start==p.endB&&e.state.selection instanceof It&&(p.start>e.state.selection.from&&p.start<=e.state.selection.from+2&&e.state.selection.from>=d.from?p.start=e.state.selection.from:p.endA<e.state.selection.to&&p.endA>=e.state.selection.to-2&&e.state.selection.to<=d.to&&(p.endB+=e.state.selection.to-p.endA,p.endA=e.state.selection.to)),bn&&kn<=11&&p.endB==p.start+1&&p.endA==p.start&&p.start>d.from&&"  "==d.doc.textBetween(p.start-d.from-1,p.start-d.from+1)&&(p.start--,p.endA--,p.endB--);let m,g=d.doc.resolveNoCache(p.start-d.from),y=d.doc.resolveNoCache(p.endB-d.from),v=f.resolve(p.start),b=g.sameParent(y)&&g.parent.inlineContent&&v.end()>=p.endA;if((Mn&&e.input.lastIOSEnter>Date.now()-225&&(!b||o.some((e=>"DIV"==e.nodeName||"P"==e.nodeName)))||!b&&g.pos<d.doc.content.size&&(!g.sameParent(y)||!g.parent.inlineContent)&&!/\S/.test(d.doc.textBetween(g.pos,y.pos,"",""))&&(m=Pt.findFrom(d.doc.resolve(g.pos+1),1,!0))&&m.head>g.pos)&&e.someProp("handleKeyDown",(t=>t(e,fn(13,"Enter")))))return void(e.input.lastIOSEnter=0);if(e.state.selection.anchor>p.start&&function(e,t,n,r,o){if(n-t<=o.pos-r.pos||di(r,!0,!1)<o.pos)return!1;let i=e.resolve(t);if(!r.parent.isTextblock){let e=i.nodeAfter;return null!=e&&n==t+e.nodeSize}if(i.parentOffset<i.parent.content.size||!i.parent.isTextblock)return!1;let s=e.resolve(di(i,!0,!0));return!(!s.parent.isTextblock||s.pos>n||di(s,!0,!1)<n)&&r.parent.content.cut(r.parentOffset).eq(s.parent.content)}(f,p.start,p.endA,g,y)&&e.someProp("handleKeyDown",(t=>t(e,fn(8,"Backspace")))))return void(Nn&&xn&&e.domObserver.suppressSelectionUpdates());xn&&p.endB==p.start&&(e.input.lastChromeDelete=Date.now()),Nn&&!b&&g.start()!=y.start()&&0==y.parentOffset&&g.depth==y.depth&&d.sel&&d.sel.anchor==d.sel.head&&d.sel.head==p.endA&&(p.endB-=2,y=d.doc.resolveNoCache(p.endB-d.from),setTimeout((()=>{e.someProp("handleKeyDown",(function(t){return t(e,fn(13,"Enter"))}))}),20));let k,w,S,x=p.start,C=p.endA;if(b)if(g.pos==y.pos)bn&&kn<=11&&0==g.parentOffset&&(e.domObserver.suppressSelectionUpdates(),setTimeout((()=>Cr(e)),20)),k=e.state.tr.delete(x,C),w=f.resolve(p.start).marksAcross(f.resolve(p.endA));else if(p.endA==p.endB&&(S=function(e,t){let n,r,o,i=e.firstChild.marks,s=t.firstChild.marks,l=i,a=s;for(let u=0;u<s.length;u++)l=s[u].removeFromSet(l);for(let u=0;u<i.length;u++)a=i[u].removeFromSet(a);if(1==l.length&&0==a.length)r=l[0],n="add",o=e=>e.mark(r.addToSet(e.marks));else{if(0!=l.length||1!=a.length)return null;r=a[0],n="remove",o=e=>e.mark(r.removeFromSet(e.marks))}let c=[];for(let u=0;u<t.childCount;u++)c.push(o(t.child(u)));if(I.from(c).eq(e))return{mark:r,type:n}}(g.parent.content.cut(g.parentOffset,y.parentOffset),v.parent.content.cut(v.parentOffset,p.endA-v.start()))))k=e.state.tr,"add"==S.type?k.addMark(x,C,S.mark):k.removeMark(x,C,S.mark);else if(g.parent.child(g.index()).isText&&g.index()==y.index()-(y.textOffset?0:1)){let t=g.parent.textBetween(g.parentOffset,y.parentOffset);if(e.someProp("handleTextInput",(n=>n(e,x,C,t))))return;k=e.state.tr.insertText(t,x,C)}if(k||(k=e.state.tr.replace(x,C,d.doc.slice(p.start-d.from,p.endB-d.from))),d.sel){let t=ui(e,k.doc,d.sel);t&&!(xn&&e.composing&&t.empty&&(p.start!=p.endB||e.input.lastChromeDelete<Date.now()-100)&&(t.head==x||t.head==k.mapping.map(C)-1)||bn&&t.empty&&t.head==x)&&k.setSelection(t)}w&&k.ensureMarks(w),i&&k.setMeta("composition",i),e.dispatch(k.scrollIntoView())}function ui(e,t,n){return Math.max(n.anchor,n.head)>t.content.size?null:Dr(e,t.resolve(n.anchor),t.resolve(n.head))}function di(e,t,n){let r=e.depth,o=t?e.end():e.pos;for(;r>0&&(t||e.indexAfter(r)==e.node(r).childCount);)r--,o++,t=!1;if(n){let t=e.node(r).maybeChild(e.indexAfter(r));for(;t&&!t.isLeaf;)t=t.firstChild,o++}return o}function fi(e){if(2!=e.length)return!1;let t=e.charCodeAt(0),n=e.charCodeAt(1);return t>=56320&&t<=57343&&n>=55296&&n<=56319}class hi{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new ao,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(vi),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):"function"==typeof e?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=gi(this),mi(this),this.nodeViews=yi(this),this.docView=sr(this.state.doc,pi(this),Xo(this),this.dom,this),this.domObserver=new ni(this,((e,t,n,r)=>ci(this,e,t,n,r))),this.domObserver.start(),function(e){for(let t in io){let n=io[t];e.dom.addEventListener(t,e.input.eventHandlers[t]=t=>{!ho(e,t)||fo(e,t)||!e.editable&&t.type in so||n(e,t)},lo[t]?{passive:!0}:void 0)}En&&e.dom.addEventListener("input",(()=>null)),uo(e)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&uo(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(vi),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let n in this._props)t[n]=this._props[n];t.state=this.state;for(let n in e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n;let r=this.state,o=!1,i=!1;e.storedMarks&&this.composing&&(Mo(this),i=!0),this.state=e;let s=r.plugins!=e.plugins||this._props.plugins!=t.plugins;if(s||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let e=yi(this);(function(e,t){let n=0,r=0;for(let o in e){if(e[o]!=t[o])return!0;n++}for(let o in t)r++;return n!=r})(e,this.nodeViews)&&(this.nodeViews=e,o=!0)}(s||t.handleDOMEvents!=this._props.handleDOMEvents)&&uo(this),this.editable=gi(this),mi(this);let l=Xo(this),a=pi(this),c=r.plugins==e.plugins||r.doc.eq(e.doc)?e.scrollToSelection>r.scrollToSelection?"to selection":"preserve":"reset",u=o||!this.docView.matchesNode(e.doc,a,l);!u&&e.selection.eq(r.selection)||(i=!0);let d="preserve"==c&&i&&null==this.dom.style.overflowAnchor&&function(e){let t,n,r=e.dom.getBoundingClientRect(),o=Math.max(0,r.top);for(let i=(r.left+r.right)/2,s=o+1;s<Math.min(innerHeight,r.bottom);s+=5){let r=e.root.elementFromPoint(i,s);if(!r||r==e.dom||!e.dom.contains(r))continue;let l=r.getBoundingClientRect();if(l.top>=o-20){t=r,n=l.top;break}}return{refDOM:t,refTop:n,stack:In(e.dom)}}(this);if(i){this.domObserver.stop();let t=u&&(bn||xn)&&!this.composing&&!r.selection.empty&&!e.selection.empty&&function(e,t){let n=Math.min(e.$anchor.sharedDepth(e.head),t.$anchor.sharedDepth(t.head));return e.$anchor.start(n)!=t.$anchor.start(n)}(r.selection,e.selection);if(u){let n=xn?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=To(this)),!o&&this.docView.update(e.doc,a,l,this)||(this.docView.updateOuterDeco(a),this.docView.destroy(),this.docView=sr(e.doc,a,l,this.dom,this)),n&&!this.trackWrites&&(t=!0)}t||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function(e){let t=e.docView.domFromPos(e.state.selection.anchor,0),n=e.domSelectionRange();return sn(t.node,t.offset,n.anchorNode,n.anchorOffset)}(this))?Cr(this,t):(Nr(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(r),(null===(n=this.dragging)||void 0===n?void 0:n.node)&&!r.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,r),"reset"==c?this.dom.scrollTop=0:"to selection"==c?this.scrollToSelection():d&&function({refDOM:e,refTop:t,stack:n}){let r=e?e.getBoundingClientRect().top:0;_n(n,0==r?0:r-t)}(d)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(e&&this.dom.contains(1==e.nodeType?e:e.parentNode))if(this.someProp("handleScrollToSelection",(e=>e(this))));else if(this.state.selection instanceof $t){let t=this.docView.domAfterPos(this.state.selection.from);1==t.nodeType&&zn(this,t.getBoundingClientRect(),e)}else zn(this,this.coordsAtPos(this.state.selection.head,1),e);else;}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let e=0;e<this.directPlugins.length;e++){let t=this.directPlugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}for(let e=0;e<this.state.plugins.length;e++){let t=this.state.plugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,r=-1;if(this.state.doc.nodeAt(n.from)==n.node)r=n.from;else{let e=n.from+(this.state.doc.content.size-t.doc.content.size);(e>0&&this.state.doc.nodeAt(e))==n.node&&(r=e)}this.dragging=new Po(e.slice,e.move,r<0?void 0:$t.create(this.state.doc,r))}someProp(e,t){let n,r=this._props&&this._props[e];if(null!=r&&(n=t?t(r):r))return n;for(let i=0;i<this.directPlugins.length;i++){let r=this.directPlugins[i].props[e];if(null!=r&&(n=t?t(r):r))return n}let o=this.state.plugins;if(o)for(let i=0;i<o.length;i++){let r=o[i].props[e];if(null!=r&&(n=t?t(r):r))return n}}hasFocus(){if(bn){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if("false"==e.contentEditable)return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(e){if(e.setActive)return e.setActive();if($n)return e.focus($n);let t=In(e);e.focus(null==$n?{get preventScroll(){return $n={preventScroll:!0},!0}}:void 0),$n||($n=!1,_n(t,0))}(this.dom),Cr(this),this.domObserver.start()}get root(){let e=this._root;if(null==e)for(let t=this.dom.parentNode;t;t=t.parentNode)if(9==t.nodeType||11==t.nodeType&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t;return e||document}updateRoot(){this._root=null}posAtCoords(e){return Hn(this,e)}coordsAtPos(e,t=1){return qn(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let r=this.docView.posFromDOM(e,t,n);if(null==r)throw new RangeError("DOM position not inside the editor");return r}endOfTextblock(e,t){return er(this,t||this.state,e)}pasteHTML(e,t){return Ao(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return Ao(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return Kr(this,e)}destroy(){this.docView&&(!function(e){e.domObserver.stop();for(let t in e.input.eventHandlers)e.dom.removeEventListener(t,e.input.eventHandlers[t]);clearTimeout(e.input.composingTimeout),clearTimeout(e.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],Xo(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,rn=null)}get isDestroyed(){return null==this.docView}dispatchEvent(e){return function(e,t){fo(e,t)||!io[t.type]||!e.editable&&t.type in so||io[t.type](e,t)}(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return e?En&&11===this.root.nodeType&&function(e){let t=e.activeElement;for(;t&&t.shadowRoot;)t=t.shadowRoot.activeElement;return t}(this.dom.ownerDocument)==this.dom&&function(e,t){if(t.getComposedRanges){let n=t.getComposedRanges(e.root)[0];if(n)return ii(e,n)}let n;function r(e){e.preventDefault(),e.stopImmediatePropagation(),n=e.getTargetRanges()[0]}return e.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),e.dom.removeEventListener("beforeinput",r,!0),n?ii(e,n):null}(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function pi(e){let t=Object.create(null);return t.class="ProseMirror",t.contenteditable=String(e.editable),e.someProp("attributes",(n=>{if("function"==typeof n&&(n=n(e.state)),n)for(let e in n)"class"==e?t.class+=" "+n[e]:"style"==e?t.style=(t.style?t.style+";":"")+n[e]:t[e]||"contenteditable"==e||"nodeName"==e||(t[e]=String(n[e]))})),t.translate||(t.translate="no"),[Fo.node(0,e.state.doc.content.size,t)]}function mi(e){if(e.markCursor){let t=document.createElement("img");t.className="ProseMirror-separator",t.setAttribute("mark-placeholder","true"),t.setAttribute("alt",""),e.cursorWrapper={dom:t,deco:Fo.widget(e.state.selection.from,t,{raw:!0,marks:e.markCursor})}}else e.cursorWrapper=null}function gi(e){return!e.someProp("editable",(t=>!1===t(e.state)))}function yi(e){let t=Object.create(null);function n(e){for(let n in e)Object.prototype.hasOwnProperty.call(t,n)||(t[n]=e[n])}return e.someProp("nodeViews",n),e.someProp("markViews",n),t}function vi(e){if(e.spec.state||e.spec.filterTransaction||e.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}for(var bi={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},ki={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},wi="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),Si="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),xi=0;xi<10;xi++)bi[48+xi]=bi[96+xi]=String(xi);for(xi=1;xi<=24;xi++)bi[xi+111]="F"+xi;for(xi=65;xi<=90;xi++)bi[xi]=String.fromCharCode(xi+32),ki[xi]=String.fromCharCode(xi);for(var Ci in bi)ki.hasOwnProperty(Ci)||(ki[Ci]=bi[Ci]);const Ei="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),Mi="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function Ti(e){let t,n,r,o,i=e.split(/-(?!$)/),s=i[i.length-1];"Space"==s&&(s=" ");for(let l=0;l<i.length-1;l++){let e=i[l];if(/^(cmd|meta|m)$/i.test(e))o=!0;else if(/^a(lt)?$/i.test(e))t=!0;else if(/^(c|ctrl|control)$/i.test(e))n=!0;else if(/^s(hift)?$/i.test(e))r=!0;else{if(!/^mod$/i.test(e))throw new Error("Unrecognized modifier name: "+e);Ei?o=!0:n=!0}}return t&&(s="Alt-"+s),n&&(s="Ctrl-"+s),o&&(s="Meta-"+s),r&&(s="Shift-"+s),s}function Oi(e,t,n=!0){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),n&&t.shiftKey&&(e="Shift-"+e),e}function Ni(e){let t=function(e){let t=Object.create(null);for(let n in e)t[Ti(n)]=e[n];return t}(e);return function(e,n){let r,o=function(e){var t=!(wi&&e.metaKey&&e.shiftKey&&!e.ctrlKey&&!e.altKey||Si&&e.shiftKey&&e.key&&1==e.key.length||"Unidentified"==e.key)&&e.key||(e.shiftKey?ki:bi)[e.keyCode]||e.key||"Unidentified";return"Esc"==t&&(t="Escape"),"Del"==t&&(t="Delete"),"Left"==t&&(t="ArrowLeft"),"Up"==t&&(t="ArrowUp"),"Right"==t&&(t="ArrowRight"),"Down"==t&&(t="ArrowDown"),t}(n),i=t[Oi(o,n)];if(i&&i(e.state,e.dispatch,e))return!0;if(1==o.length&&" "!=o){if(n.shiftKey){let r=t[Oi(o,n,!1)];if(r&&r(e.state,e.dispatch,e))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(Mi&&n.ctrlKey&&n.altKey)&&(r=bi[n.keyCode])&&r!=o){let o=t[Oi(r,n)];if(o&&o(e.state,e.dispatch,e))return!0}}return!1}}const Ai=(e,t)=>!e.selection.empty&&(t&&t(e.tr.deleteSelection().scrollIntoView()),!0);function Di(e,t){let{$cursor:n}=e.selection;return!n||(t?!t.endOfTextblock("backward",e):n.parentOffset>0)?null:n}const Pi=(e,t,n)=>{let r=Di(e,n);if(!r)return!1;let o=Ii(r);if(!o){let n=r.blockRange(),o=n&&lt(n);return null!=o&&(t&&t(e.tr.lift(n,o).scrollIntoView()),!0)}let i=o.nodeBefore;if(qi(e,o,t,-1))return!0;if(0==r.parent.content.size&&(Li(i,"end")||$t.isSelectable(i)))for(let s=r.depth;;s--){let n=yt(e.doc,r.before(s),r.after(s),H.empty);if(n&&n.slice.size<n.to-n.from){if(t){let r=e.tr.step(n);r.setSelection(Li(i,"end")?Pt.findFrom(r.doc.resolve(r.mapping.map(o.pos,-1)),-1):$t.create(r.doc,o.pos-i.nodeSize)),t(r.scrollIntoView())}return!0}if(1==s||r.node(s-1).childCount>1)break}return!(!i.isAtom||o.depth!=r.depth-1)&&(t&&t(e.tr.delete(o.pos-i.nodeSize,o.pos).scrollIntoView()),!0)};function Ri(e,t,n){let r=t.nodeBefore,o=t.pos-1;for(;!r.isTextblock;o--){if(r.type.spec.isolating)return!1;let e=r.lastChild;if(!e)return!1;r=e}let i=t.nodeAfter,s=t.pos+1;for(;!i.isTextblock;s++){if(i.type.spec.isolating)return!1;let e=i.firstChild;if(!e)return!1;i=e}let l=yt(e.doc,o,s,H.empty);if(!l||l.from!=o||l instanceof nt&&l.slice.size>=s-o)return!1;if(n){let t=e.tr.step(l);t.setSelection(It.create(t.doc,o)),n(t.scrollIntoView())}return!0}function Li(e,t,n=!1){for(let r=e;r;r="start"==t?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)return!1}return!1}const zi=(e,t,n)=>{let{$head:r,empty:o}=e.selection,i=r;if(!o)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",e):r.parentOffset>0)return!1;i=Ii(r)}let s=i&&i.nodeBefore;return!(!s||!$t.isSelectable(s))&&(t&&t(e.tr.setSelection($t.create(e.doc,i.pos-s.nodeSize)).scrollIntoView()),!0)};function Ii(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){if(e.index(t)>0)return e.doc.resolve(e.before(t+1));if(e.node(t).type.spec.isolating)break}return null}function _i(e,t){let{$cursor:n}=e.selection;return!n||(t?!t.endOfTextblock("forward",e):n.parentOffset<n.parent.content.size)?null:n}const $i=(e,t,n)=>{let r=_i(e,n);if(!r)return!1;let o=Bi(r);if(!o)return!1;let i=o.nodeAfter;if(qi(e,o,t,1))return!0;if(0==r.parent.content.size&&(Li(i,"start")||$t.isSelectable(i))){let n=yt(e.doc,r.before(),r.after(),H.empty);if(n&&n.slice.size<n.to-n.from){if(t){let r=e.tr.step(n);r.setSelection(Li(i,"start")?Pt.findFrom(r.doc.resolve(r.mapping.map(o.pos)),1):$t.create(r.doc,r.mapping.map(o.pos))),t(r.scrollIntoView())}return!0}}return!(!i.isAtom||o.depth!=r.depth-1)&&(t&&t(e.tr.delete(o.pos,o.pos+i.nodeSize).scrollIntoView()),!0)},Fi=(e,t,n)=>{let{$head:r,empty:o}=e.selection,i=r;if(!o)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",e):r.parentOffset<r.parent.content.size)return!1;i=Bi(r)}let s=i&&i.nodeAfter;return!(!s||!$t.isSelectable(s))&&(t&&t(e.tr.setSelection($t.create(e.doc,i.pos)).scrollIntoView()),!0)};function Bi(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){let n=e.node(t);if(e.index(t)+1<n.childCount)return e.doc.resolve(e.after(t+1));if(n.type.spec.isolating)break}return null}const ji=(e,t)=>{let{$head:n,$anchor:r}=e.selection;return!(!n.parent.type.spec.code||!n.sameParent(r))&&(t&&t(e.tr.insertText("\n").scrollIntoView()),!0)};function Hi(e){for(let t=0;t<e.edgeCount;t++){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}const Vi=(e,t)=>{let n=e.selection,{$from:r,$to:o}=n;if(n instanceof Bt||r.parent.inlineContent||o.parent.inlineContent)return!1;let i=Hi(o.parent.contentMatchAt(o.indexAfter()));if(!i||!i.isTextblock)return!1;if(t){let n=(!r.parentOffset&&o.index()<o.parent.childCount?r:o).pos,s=e.tr.insert(n,i.createAndFill());s.setSelection(It.create(s.doc,n+1)),t(s.scrollIntoView())}return!0},Ui=(e,t)=>{let{$cursor:n}=e.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if(ft(e.doc,r))return t&&t(e.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),o=r&&lt(r);return null!=o&&(t&&t(e.tr.lift(r,o).scrollIntoView()),!0)};const Wi=(e,t)=>{let{$from:n,$to:r}=e.selection;if(e.selection instanceof $t&&e.selection.node.isBlock)return!(!n.parentOffset||!ft(e.doc,n.pos)||(t&&t(e.tr.split(n.pos).scrollIntoView()),0));if(!n.depth)return!1;let o,i,s=[],l=!1,a=!1;for(let f=n.depth;;f--){if(n.node(f).isBlock){l=n.end(f)==n.pos+(n.depth-f),a=n.start(f)==n.pos-(n.depth-f),i=Hi(n.node(f-1).contentMatchAt(n.indexAfter(f-1))),s.unshift(l&&i?{type:i}:null),o=f;break}if(1==f)return!1;s.unshift(null)}let c=e.tr;(e.selection instanceof It||e.selection instanceof Bt)&&c.deleteSelection();let u=c.mapping.map(n.pos),d=ft(c.doc,u,s.length,s);if(d||(s[0]=i?{type:i}:null,d=ft(c.doc,u,s.length,s)),!d)return!1;if(c.split(u,s.length,s),!l&&a&&n.node(o).type!=i){let e=c.mapping.map(n.before(o)),t=c.doc.resolve(e);i&&n.node(o-1).canReplaceWith(t.index(),t.index()+1,i)&&c.setNodeMarkup(c.mapping.map(n.before(o)),i)}return t&&t(c.scrollIntoView()),!0};function qi(e,t,n,r){let o,i,s=t.nodeBefore,l=t.nodeAfter,a=s.type.spec.isolating||l.type.spec.isolating;if(!a&&function(e,t,n){let r=t.nodeBefore,o=t.nodeAfter,i=t.index();return!(!(r&&o&&r.type.compatibleContent(o.type))||(!r.content.size&&t.parent.canReplace(i-1,i)?(n&&n(e.tr.delete(t.pos-r.nodeSize,t.pos).scrollIntoView()),0):!t.parent.canReplace(i,i+1)||!o.isTextblock&&!ht(e.doc,t.pos)||(n&&n(e.tr.join(t.pos).scrollIntoView()),0)))}(e,t,n))return!0;let c=!a&&t.parent.canReplace(t.index(),t.index()+1);if(c&&(o=(i=s.contentMatchAt(s.childCount)).findWrapping(l.type))&&i.matchType(o[0]||l.type).validEnd){if(n){let r=t.pos+l.nodeSize,i=I.empty;for(let e=o.length-1;e>=0;e--)i=I.from(o[e].create(null,i));i=I.from(s.copy(i));let a=e.tr.step(new rt(t.pos-1,r,t.pos,r,new H(i,1,0),o.length,!0)),c=a.doc.resolve(r+2*o.length);c.nodeAfter&&c.nodeAfter.type==s.type&&ht(a.doc,c.pos)&&a.join(c.pos),n(a.scrollIntoView())}return!0}let u=l.type.spec.isolating||r>0&&a?null:Pt.findFrom(t,1),d=u&&u.$from.blockRange(u.$to),f=d&&lt(d);if(null!=f&&f>=t.depth)return n&&n(e.tr.lift(d,f).scrollIntoView()),!0;if(c&&Li(l,"start",!0)&&Li(s,"end")){let r=s,o=[];for(;o.push(r),!r.isTextblock;)r=r.lastChild;let i=l,a=1;for(;!i.isTextblock;i=i.firstChild)a++;if(r.canReplace(r.childCount,r.childCount,i.content)){if(n){let r=I.empty;for(let e=o.length-1;e>=0;e--)r=I.from(o[e].copy(r));n(e.tr.step(new rt(t.pos-o.length,t.pos+l.nodeSize,t.pos+a,t.pos+l.nodeSize-a,new H(r,o.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function Ki(e){return function(t,n){let r=t.selection,o=e<0?r.$from:r.$to,i=o.depth;for(;o.node(i).isInline;){if(!i)return!1;i--}return!!o.node(i).isTextblock&&(n&&n(t.tr.setSelection(It.create(t.doc,e<0?o.start(i):o.end(i)))),!0)}}const Ji=Ki(-1),Qi=Ki(1);function Gi(e,t=null){return function(n,r){let o=!1;for(let i=0;i<n.selection.ranges.length&&!o;i++){let{$from:{pos:r},$to:{pos:s}}=n.selection.ranges[i];n.doc.nodesBetween(r,s,((r,i)=>{if(o)return!1;if(r.isTextblock&&!r.hasMarkup(e,t))if(r.type==e)o=!0;else{let t=n.doc.resolve(i),r=t.index();o=t.parent.canReplaceWith(r,r+1,e)}}))}if(!o)return!1;if(r){let o=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:i},$to:{pos:s}}=n.selection.ranges[r];o.setBlockType(i,s,e,t)}r(o.scrollIntoView())}return!0}}function Yi(...e){return function(t,n,r){for(let o=0;o<e.length;o++)if(e[o](t,n,r))return!0;return!1}}function Xi(e,t=null){return function(n,r){let{$from:o,$to:i}=n.selection,s=o.blockRange(i);if(!s)return!1;let l=r?n.tr:null;return!!function(e,t,n,r=null){let o=!1,i=t,s=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(n)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=s.resolve(t.start-2);i=new oe(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new oe(t.$from,s.resolve(t.$to.end(t.depth)),t.depth)),o=!0}let l=at(i,n,r,t);if(!l)return!1;e&&function(e,t,n,r,o){let i=I.empty;for(let u=n.length-1;u>=0;u--)i=I.from(n[u].type.create(n[u].attrs,i));e.step(new rt(t.start-(r?2:0),t.end,t.start,t.end,new H(i,0,0),n.length,!0));let s=0;for(let u=0;u<n.length;u++)n[u].type==o&&(s=u+1);let l=n.length-s,a=t.start+n.length-(r?2:0),c=t.parent;for(let u=t.startIndex,d=t.endIndex,f=!0;u<d;u++,f=!1)!f&&ft(e.doc,a,l)&&(e.split(a,l),a+=2*l),a+=c.child(u).nodeSize}(e,t,l,o,n);return!0}(l,s,e,t)&&(r&&r(l.scrollIntoView()),!0)}}function Zi(e){return function(t,n){let{$from:r,$to:o}=t.selection,i=r.blockRange(o,(t=>t.childCount>0&&t.firstChild.type==e));return!!i&&(!n||(r.node(i.depth-1).type==e?function(e,t,n,r){let o=e.tr,i=r.end,s=r.$to.end(r.depth);i<s&&(o.step(new rt(i-1,s,i,s,new H(I.from(n.create(null,r.parent.copy())),1,0),1,!0)),r=new oe(o.doc.resolve(r.$from.pos),o.doc.resolve(s),r.depth));const l=lt(r);if(null==l)return!1;o.lift(r,l);let a=o.doc.resolve(o.mapping.map(i,-1)-1);ht(o.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&o.join(a.pos);return t(o.scrollIntoView()),!0}(t,n,e,i):function(e,t,n){let r=e.tr,o=n.parent;for(let h=n.end,p=n.endIndex-1,m=n.startIndex;p>m;p--)h-=o.child(p).nodeSize,r.delete(h-1,h+1);let i=r.doc.resolve(n.start),s=i.nodeAfter;if(r.mapping.map(n.end)!=n.start+i.nodeAfter.nodeSize)return!1;let l=0==n.startIndex,a=n.endIndex==o.childCount,c=i.node(-1),u=i.index(-1);if(!c.canReplace(u+(l?0:1),u+1,s.content.append(a?I.empty:I.from(o))))return!1;let d=i.pos,f=d+s.nodeSize;return r.step(new rt(d-(l?1:0),f+(a?1:0),d+1,f-1,new H((l?I.empty:I.from(o.copy(I.empty))).append(a?I.empty:I.from(o.copy(I.empty))),l?0:1,a?0:1),l?0:1)),t(r.scrollIntoView()),!0}(t,n,i)))}}function es(e){const{state:t,transaction:n}=e;let{selection:r}=n,{doc:o}=n,{storedMarks:i}=n;return{...t,apply:t.apply.bind(t),applyTransaction:t.applyTransaction.bind(t),plugins:t.plugins,schema:t.schema,reconfigure:t.reconfigure.bind(t),toJSON:t.toJSON.bind(t),get storedMarks(){return i},get selection(){return r},get doc(){return o},get tr(){return r=n.selection,o=n.doc,i=n.storedMarks,n}}}Yi(Ai,Pi,zi),Yi(Ai,$i,Fi),Yi(ji,Vi,Ui,Wi),"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();class ts{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:e,editor:t,state:n}=this,{view:r}=t,{tr:o}=n,i=this.buildProps(o);return Object.fromEntries(Object.entries(e).map((([e,t])=>[e,(...e)=>{const n=t(...e)(i);return o.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(o),n}])))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){const{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s=[],l=!!e,a=e||o.tr,c={...Object.fromEntries(Object.entries(n).map((([e,n])=>[e,(...e)=>{const r=this.buildProps(a,t),o=n(...e)(r);return s.push(o),c}]))),run:()=>(l||!t||a.getMeta("preventDispatch")||this.hasCustomState||i.dispatch(a),s.every((e=>!0===e)))};return c}createCan(e){const{rawCommands:t,state:n}=this,r=!1,o=e||n.tr,i=this.buildProps(o,r);return{...Object.fromEntries(Object.entries(t).map((([e,t])=>[e,(...e)=>t(...e)({...i,dispatch:void 0})]))),chain:()=>this.createChain(o,r)}}buildProps(e,t=!0){const{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s={tr:e,editor:r,view:i,state:es({state:o,transaction:e}),dispatch:t?()=>{}:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(n).map((([e,t])=>[e,(...e)=>t(...e)(s)])))}};return s}}class ns{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const n=this.callbacks[e];return n&&n.forEach((e=>e.apply(this,t))),this}off(e,t){const n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter((e=>e!==t)):delete this.callbacks[e]),this}once(e,t){const n=(...r)=>{this.off(e,n),t.apply(this,r)};return this.on(e,n)}removeAllListeners(){this.callbacks={}}}function rs(e,t,n){if(void 0===e.config[t]&&e.parent)return rs(e.parent,t,n);if("function"==typeof e.config[t]){return e.config[t].bind({...n,parent:e.parent?rs(e.parent,t,n):null})}return e.config[t]}function is(e){return{baseExtensions:e.filter((e=>"extension"===e.type)),nodeExtensions:e.filter((e=>"node"===e.type)),markExtensions:e.filter((e=>"mark"===e.type))}}function ss(e){const t=[],{nodeExtensions:n,markExtensions:r}=is(e),o=[...n,...r],i={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return e.forEach((e=>{const n=rs(e,"addGlobalAttributes",{name:e.name,options:e.options,storage:e.storage,extensions:o});if(!n)return;n().forEach((e=>{e.types.forEach((n=>{Object.entries(e.attributes).forEach((([e,r])=>{t.push({type:n,name:e,attribute:{...i,...r}})}))}))}))})),o.forEach((e=>{const n={name:e.name,options:e.options,storage:e.storage},r=rs(e,"addAttributes",n);if(!r)return;const o=r();Object.entries(o).forEach((([n,r])=>{const o={...i,...r};"function"==typeof(null==o?void 0:o.default)&&(o.default=o.default()),(null==o?void 0:o.isRequired)&&void 0===(null==o?void 0:o.default)&&delete o.default,t.push({type:e.name,name:n,attribute:o})}))})),t}function ls(e,t){if("string"==typeof e){if(!t.nodes[e])throw Error(`There is no node type named '${e}'. Maybe you forgot to add the extension?`);return t.nodes[e]}return e}function as(...e){return e.filter((e=>!!e)).reduce(((e,t)=>{const n={...e};return Object.entries(t).forEach((([e,t])=>{if(n[e])if("class"===e){const r=t?String(t).split(" "):[],o=n[e]?n[e].split(" "):[],i=r.filter((e=>!o.includes(e)));n[e]=[...o,...i].join(" ")}else if("style"===e){const r=t?t.split(";").map((e=>e.trim())).filter(Boolean):[],o=n[e]?n[e].split(";").map((e=>e.trim())).filter(Boolean):[],i=new Map;o.forEach((e=>{const[t,n]=e.split(":").map((e=>e.trim()));i.set(t,n)})),r.forEach((e=>{const[t,n]=e.split(":").map((e=>e.trim()));i.set(t,n)})),n[e]=Array.from(i.entries()).map((([e,t])=>`${e}: ${t}`)).join("; ")}else n[e]=t;else n[e]=t})),n}),{})}function cs(e,t){return t.filter((t=>t.type===e.type.name)).filter((e=>e.attribute.rendered)).map((t=>t.attribute.renderHTML?t.attribute.renderHTML(e.attrs)||{}:{[t.name]:e.attrs[t.name]})).reduce(((e,t)=>as(e,t)),{})}function us(e){return"function"==typeof e}function ds(e,t=void 0,...n){return us(e)?t?e.bind(t)(...n):e(...n):e}function fs(e,t){return"style"in e?e:{...e,getAttrs:n=>{const r=e.getAttrs?e.getAttrs(n):e.attrs;if(!1===r)return!1;const o=t.reduce(((e,t)=>{const r=t.attribute.parseHTML?t.attribute.parseHTML(n):function(e){return"string"!=typeof e?e:e.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(e):"true"===e||"false"!==e&&e}(n.getAttribute(t.name));return null==r?e:{...e,[t.name]:r}}),{});return{...r,...o}}}}function hs(e){return Object.fromEntries(Object.entries(e).filter((([e,t])=>("attrs"!==e||!function(e={}){return 0===Object.keys(e).length&&e.constructor===Object}(t))&&null!=t)))}function ps(e,t){return t.nodes[e]||t.marks[e]||null}function ms(e,t){return Array.isArray(t)?t.some((t=>("string"==typeof t?t:t.name)===e.name)):t}function gs(e,t){const n=_e.fromSchema(t).serializeFragment(e),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}function ys(e){return"[object RegExp]"===Object.prototype.toString.call(e)}class vs{constructor(e){this.find=e.find,this.handler=e.handler}}function bs(e){var t;const{editor:n,from:r,to:o,text:i,rules:s,plugin:l}=e,{view:a}=n;if(a.composing)return!1;const c=a.state.doc.resolve(r);if(c.parent.type.spec.code||(null===(t=c.nodeBefore||c.nodeAfter)||void 0===t?void 0:t.marks.find((e=>e.type.spec.code))))return!1;let u=!1;const d=((e,t=500)=>{let n="";const r=e.parentOffset;return e.parent.nodesBetween(Math.max(0,r-t),r,((e,t,o,i)=>{var s,l;const a=(null===(l=(s=e.type.spec).toText)||void 0===l?void 0:l.call(s,{node:e,pos:t,parent:o,index:i}))||e.textContent||"%leaf%";n+=e.isAtom&&!e.isText?a:a.slice(0,Math.max(0,r-t))})),n})(c)+i;return s.forEach((e=>{if(u)return;const t=((e,t)=>{if(ys(t))return t.exec(e);const n=t(e);if(!n)return null;const r=[n.text];return r.index=n.index,r.input=e,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r})(d,e.find);if(!t)return;const s=a.state.tr,c=es({state:a.state,transaction:s}),f={from:r-(t[0].length-i.length),to:o},{commands:h,chain:p,can:m}=new ts({editor:n,state:c});null!==e.handler({state:c,range:f,match:t,commands:h,chain:p,can:m})&&s.steps.length&&(s.setMeta(l,{transform:s,from:r,to:o,text:i}),a.dispatch(s),u=!0)})),u}function ks(e){const{editor:t,rules:n}=e,r=new Yt({state:{init:()=>null,apply(e,o,i){const s=e.getMeta(r);if(s)return s;const l=e.getMeta("applyInputRules");return!!l&&setTimeout((()=>{let{text:e}=l;"string"==typeof e||(e=gs(I.from(e),i.schema));const{from:o}=l,s=o+e.length;bs({editor:t,from:o,to:s,text:e,rules:n,plugin:r})})),e.selectionSet||e.docChanged?null:o}},props:{handleTextInput:(e,o,i,s)=>bs({editor:t,from:o,to:i,text:s,rules:n,plugin:r}),handleDOMEvents:{compositionend:e=>(setTimeout((()=>{const{$cursor:o}=e.state.selection;o&&bs({editor:t,from:o.pos,to:o.pos,text:"",rules:n,plugin:r})})),!1)},handleKeyDown(e,o){if("Enter"!==o.key)return!1;const{$cursor:i}=e.state.selection;return!!i&&bs({editor:t,from:i.pos,to:i.pos,text:"\n",rules:n,plugin:r})}},isInputRules:!0});return r}function ws(e){return"Object"===function(e){return Object.prototype.toString.call(e).slice(8,-1)}(e)&&(e.constructor===Object&&Object.getPrototypeOf(e)===Object.prototype)}function Ss(e,t){const n={...e};return ws(e)&&ws(t)&&Object.keys(t).forEach((r=>{ws(t[r])&&ws(e[r])?n[r]=Ss(e[r],t[r]):n[r]=t[r]})),n}class xs{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=ds(rs(this,"addOptions",{name:this.name}))),this.storage=ds(rs(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new xs(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>Ss(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new xs(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=ds(rs(t,"addOptions",{name:t.name})),t.storage=ds(rs(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:n}=e.state,r=e.state.selection.$from;if(r.pos===r.end()){const o=r.marks();if(!!!o.find((e=>(null==e?void 0:e.type.name)===t.name)))return!1;const i=o.find((e=>(null==e?void 0:e.type.name)===t.name));return i&&n.removeStoredMark(i),n.insertText(" ",r.pos),e.view.dispatch(n),!0}return!1}}class Cs{constructor(e){this.find=e.find,this.handler=e.handler}}function Es(e){const{editor:t,state:n,from:r,to:o,rule:i,pasteEvent:s,dropEvent:l}=e,{commands:a,chain:c,can:u}=new ts({editor:t,state:n}),d=[];n.doc.nodesBetween(r,o,((e,t)=>{if(!e.isTextblock||e.type.spec.code)return;const f=Math.max(r,t),h=Math.min(o,t+e.content.size);((e,t,n)=>{if(ys(t))return[...e.matchAll(t)];const r=t(e,n);return r?r.map((t=>{const n=[t.text];return n.index=t.index,n.input=e,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(t.replaceWith)),n})):[]})(e.textBetween(f-t,h-t,void 0,"￼"),i.find,s).forEach((e=>{if(void 0===e.index)return;const t=f+e.index+1,r=t+e[0].length,o={from:n.tr.mapping.map(t),to:n.tr.mapping.map(r)},h=i.handler({state:n,range:o,match:e,commands:a,chain:c,can:u,pasteEvent:s,dropEvent:l});d.push(h)}))}));return d.every((e=>null!==e))}let Ms=null;function Ts(e){const{editor:t,rules:n}=e;let r,o=null,i=!1,s=!1,l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{r="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{r=null}const a=({state:e,from:n,to:o,rule:i,pasteEvt:s})=>{const a=e.tr,c=es({state:e,transaction:a});if(Es({editor:t,state:c,from:Math.max(n-1,0),to:o.b-1,rule:i,pasteEvent:s,dropEvent:r})&&a.steps.length){try{r="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{r=null}return l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,a}};return n.map((e=>new Yt({view(e){const n=n=>{var r;o=(null===(r=e.dom.parentElement)||void 0===r?void 0:r.contains(n.target))?e.dom.parentElement:null,o&&(Ms=t)},r=()=>{Ms&&(Ms=null)};return window.addEventListener("dragstart",n),window.addEventListener("dragend",r),{destroy(){window.removeEventListener("dragstart",n),window.removeEventListener("dragend",r)}}},props:{handleDOMEvents:{drop:(e,t)=>{if(s=o===e.dom.parentElement,r=t,!s){const e=Ms;e&&setTimeout((()=>{const t=e.state.selection;t&&e.commands.deleteRange({from:t.from,to:t.to})}),10)}return!1},paste:(e,t)=>{var n;const r=null===(n=t.clipboardData)||void 0===n?void 0:n.getData("text/html");return l=t,i=!!(null==r?void 0:r.includes("data-pm-slice")),!1}}},appendTransaction:(t,n,r)=>{const o=t[0],c="paste"===o.getMeta("uiEvent")&&!i,u="drop"===o.getMeta("uiEvent")&&!s,d=o.getMeta("applyPasteRules"),f=!!d;if(!c&&!u&&!f)return;if(f){let{text:t}=d;"string"==typeof t||(t=gs(I.from(t),r.schema));const{from:n}=d,o=n+t.length,i=(e=>{var t;const n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null===(t=n.clipboardData)||void 0===t||t.setData("text/html",e),n})(t);return a({rule:e,state:r,from:n,to:{b:o},pasteEvt:i})}const h=n.doc.content.findDiffStart(r.doc.content),p=n.doc.content.findDiffEnd(r.doc.content);return"number"==typeof h&&p&&h!==p.b?a({rule:e,state:r,from:h,to:p,pasteEvt:l}):void 0}})))}class Os{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=Os.resolve(e),this.schema=function(e,t){var n;const r=ss(e),{nodeExtensions:o,markExtensions:i}=is(e),s=null===(n=o.find((e=>rs(e,"topNode"))))||void 0===n?void 0:n.name,l=Object.fromEntries(o.map((n=>{const o=r.filter((e=>e.type===n.name)),i={name:n.name,options:n.options,storage:n.storage,editor:t},s=hs({...e.reduce(((e,t)=>{const r=rs(t,"extendNodeSchema",i);return{...e,...r?r(n):{}}}),{}),content:ds(rs(n,"content",i)),marks:ds(rs(n,"marks",i)),group:ds(rs(n,"group",i)),inline:ds(rs(n,"inline",i)),atom:ds(rs(n,"atom",i)),selectable:ds(rs(n,"selectable",i)),draggable:ds(rs(n,"draggable",i)),code:ds(rs(n,"code",i)),whitespace:ds(rs(n,"whitespace",i)),linebreakReplacement:ds(rs(n,"linebreakReplacement",i)),defining:ds(rs(n,"defining",i)),isolating:ds(rs(n,"isolating",i)),attrs:Object.fromEntries(o.map((e=>{var t;return[e.name,{default:null===(t=null==e?void 0:e.attribute)||void 0===t?void 0:t.default}]})))}),l=ds(rs(n,"parseHTML",i));l&&(s.parseDOM=l.map((e=>fs(e,o))));const a=rs(n,"renderHTML",i);a&&(s.toDOM=e=>a({node:e,HTMLAttributes:cs(e,o)}));const c=rs(n,"renderText",i);return c&&(s.toText=c),[n.name,s]}))),a=Object.fromEntries(i.map((n=>{const o=r.filter((e=>e.type===n.name)),i={name:n.name,options:n.options,storage:n.storage,editor:t},s=hs({...e.reduce(((e,t)=>{const r=rs(t,"extendMarkSchema",i);return{...e,...r?r(n):{}}}),{}),inclusive:ds(rs(n,"inclusive",i)),excludes:ds(rs(n,"excludes",i)),group:ds(rs(n,"group",i)),spanning:ds(rs(n,"spanning",i)),code:ds(rs(n,"code",i)),attrs:Object.fromEntries(o.map((e=>{var t;return[e.name,{default:null===(t=null==e?void 0:e.attribute)||void 0===t?void 0:t.default}]})))}),l=ds(rs(n,"parseHTML",i));l&&(s.parseDOM=l.map((e=>fs(e,o))));const a=rs(n,"renderHTML",i);return a&&(s.toDOM=e=>a({mark:e,HTMLAttributes:cs(e,o)})),[n.name,s]})));return new Ee({topNode:s,nodes:l,marks:a})}(this.extensions,t),this.setupExtensions()}static resolve(e){const t=Os.sort(Os.flatten(e)),n=function(e){const t=e.filter(((t,n)=>e.indexOf(t)!==n));return Array.from(new Set(t))}(t.map((e=>e.name)));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map((e=>`'${e}'`)).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map((e=>{const t=rs(e,"addExtensions",{name:e.name,options:e.options,storage:e.storage});return t?[e,...this.flatten(t())]:e})).flat(10)}static sort(e){return e.sort(((e,t)=>{const n=rs(e,"priority")||100,r=rs(t,"priority")||100;return n>r?-1:n<r?1:0}))}get commands(){return this.extensions.reduce(((e,t)=>{const n=rs(t,"addCommands",{name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:ps(t.name,this.schema)});return n?{...e,...n()}:e}),{})}get plugins(){const{editor:e}=this,t=Os.sort([...this.extensions].reverse()),n=[],r=[],o=t.map((t=>{const o={name:t.name,options:t.options,storage:t.storage,editor:e,type:ps(t.name,this.schema)},i=[],s=rs(t,"addKeyboardShortcuts",o);let l={};if("mark"===t.type&&rs(t,"exitable",o)&&(l.ArrowRight=()=>xs.handleExit({editor:e,mark:t})),s){const t=Object.fromEntries(Object.entries(s()).map((([t,n])=>[t,()=>n({editor:e})])));l={...l,...t}}const a=new Yt({props:{handleKeyDown:Ni(l)}});i.push(a);const c=rs(t,"addInputRules",o);ms(t,e.options.enableInputRules)&&c&&n.push(...c());const u=rs(t,"addPasteRules",o);ms(t,e.options.enablePasteRules)&&u&&r.push(...u());const d=rs(t,"addProseMirrorPlugins",o);if(d){const e=d();i.push(...e)}return i})).flat();return[ks({editor:e,rules:n}),...Ts({editor:e,rules:r}),...o]}get attributes(){return ss(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=is(this.extensions);return Object.fromEntries(t.filter((e=>!!rs(e,"addNodeView"))).map((t=>{const n=this.attributes.filter((e=>e.type===t.name)),r={name:t.name,options:t.options,storage:t.storage,editor:e,type:ls(t.name,this.schema)},o=rs(t,"addNodeView",r);if(!o)return[];return[t.name,(r,i,s,l,a)=>{const c=cs(r,n);return o()({node:r,view:i,getPos:s,decorations:l,innerDecorations:a,editor:e,extension:t,HTMLAttributes:c})}]})))}setupExtensions(){this.extensions.forEach((e=>{var t;this.editor.extensionStorage[e.name]=e.storage;const n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:ps(e.name,this.schema)};if("mark"===e.type){(null===(t=ds(rs(e,"keepOnSplit",n)))||void 0===t||t)&&this.splittableMarks.push(e.name)}const r=rs(e,"onBeforeCreate",n),o=rs(e,"onCreate",n),i=rs(e,"onUpdate",n),s=rs(e,"onSelectionUpdate",n),l=rs(e,"onTransaction",n),a=rs(e,"onFocus",n),c=rs(e,"onBlur",n),u=rs(e,"onDestroy",n);r&&this.editor.on("beforeCreate",r),o&&this.editor.on("create",o),i&&this.editor.on("update",i),s&&this.editor.on("selectionUpdate",s),l&&this.editor.on("transaction",l),a&&this.editor.on("focus",a),c&&this.editor.on("blur",c),u&&this.editor.on("destroy",u)}))}}class Ns{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=ds(rs(this,"addOptions",{name:this.name}))),this.storage=ds(rs(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Ns(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>Ss(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new Ns({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=ds(rs(t,"addOptions",{name:t.name})),t.storage=ds(rs(t,"addStorage",{name:t.name,options:t.options})),t}}function As(e,t,n){const{from:r,to:o}=t,{blockSeparator:i="\n\n",textSerializers:s={}}=n||{};let l="";return e.nodesBetween(r,o,((e,n,a,c)=>{var u;e.isBlock&&n>r&&(l+=i);const d=null==s?void 0:s[e.type.name];if(d)return a&&(l+=d({node:e,pos:n,parent:a,index:c,range:t})),!1;e.isText&&(l+=null===(u=null==e?void 0:e.text)||void 0===u?void 0:u.slice(Math.max(r,n)-n,o-n))})),l}function Ds(e){return Object.fromEntries(Object.entries(e.nodes).filter((([,e])=>e.spec.toText)).map((([e,t])=>[e,t.spec.toText])))}const Ps=Ns.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new Yt({key:new en("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:e}=this,{state:t,schema:n}=e,{doc:r,selection:o}=t,{ranges:i}=o,s=Math.min(...i.map((e=>e.$from.pos))),l=Math.max(...i.map((e=>e.$to.pos))),a=Ds(n);return As(r,{from:s,to:l},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}});function Rs(e,t,n={strict:!0}){const r=Object.keys(t);return!r.length||r.every((r=>n.strict?t[r]===e[r]:ys(t[r])?t[r].test(e[r]):t[r]===e[r]))}function Ls(e,t,n={}){return e.find((e=>e.type===t&&Rs(Object.fromEntries(Object.keys(n).map((t=>[t,e.attrs[t]]))),n)))}function zs(e,t,n={}){return!!Ls(e,t,n)}function Is(e,t,n){var r;if(!e||!t)return;let o=e.parent.childAfter(e.parentOffset);if(o.node&&o.node.marks.some((e=>e.type===t))||(o=e.parent.childBefore(e.parentOffset)),!o.node||!o.node.marks.some((e=>e.type===t)))return;n=n||(null===(r=o.node.marks[0])||void 0===r?void 0:r.attrs);if(!Ls([...o.node.marks],t,n))return;let i=o.index,s=e.start()+o.offset,l=i+1,a=s+o.node.nodeSize;for(;i>0&&zs([...e.parent.child(i-1).marks],t,n);)i-=1,s-=e.parent.child(i).nodeSize;for(;l<e.parent.childCount&&zs([...e.parent.child(l).marks],t,n);)a+=e.parent.child(l).nodeSize,l+=1;return{from:s,to:a}}function _s(e,t){if("string"==typeof e){if(!t.marks[e])throw Error(`There is no mark type named '${e}'. Maybe you forgot to add the extension?`);return t.marks[e]}return e}function $s(e){return e instanceof It}function Fs(e=0,t=0,n=0){return Math.min(Math.max(e,t),n)}function Bs(e,t=null){if(!t)return null;const n=Pt.atStart(e),r=Pt.atEnd(e);if("start"===t||!0===t)return n;if("end"===t)return r;const o=n.from,i=r.to;return"all"===t?It.create(e,Fs(0,o,i),Fs(e.content.size,o,i)):It.create(e,Fs(t,o,i),Fs(t,o,i))}function js(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Hs=e=>{const t=e.childNodes;for(let n=t.length-1;n>=0;n-=1){const r=t[n];3===r.nodeType&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?e.removeChild(r):1===r.nodeType&&Hs(r)}return e};function Vs(e){const t=`<body>${e}</body>`,n=(new window.DOMParser).parseFromString(t,"text/html").body;return Hs(n)}function Us(e,t,n){if(e instanceof se||e instanceof I)return e;n={slice:!0,parseOptions:{},...n};const r="string"==typeof e;if("object"==typeof e&&null!==e)try{if(Array.isArray(e)&&e.length>0)return I.fromArray(e.map((e=>t.nodeFromJSON(e))));const r=t.nodeFromJSON(e);return n.errorOnInvalidContent&&r.check(),r}catch(o){if(n.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:o});return console.warn("[tiptap warn]: Invalid content.","Passed value:",e,"Error:",o),Us("",t,n)}if(r){if(n.errorOnInvalidContent){let r=!1,o="";const i=new Ee({topNode:t.spec.topNode,marks:t.spec.marks,nodes:t.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:e=>(r=!0,o="string"==typeof e?e:e.outerHTML,null)}]}})});if(n.slice?Te.fromSchema(i).parseSlice(Vs(e),n.parseOptions):Te.fromSchema(i).parse(Vs(e),n.parseOptions),n.errorOnInvalidContent&&r)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${o}`)})}const r=Te.fromSchema(t);return n.slice?r.parseSlice(Vs(e),n.parseOptions).content:r.parse(Vs(e),n.parseOptions)}return Us("",t,n)}function Ws(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function qs(e,t,n={}){const{from:r,to:o,empty:i}=e.selection,s=t?ls(t,e.schema):null,l=[];e.doc.nodesBetween(r,o,((e,t)=>{if(e.isText)return;const n=Math.max(r,t),i=Math.min(o,t+e.nodeSize);l.push({node:e,from:n,to:i})}));const a=o-r,c=l.filter((e=>!s||s.name===e.node.type.name)).filter((e=>Rs(e.node.attrs,n,{strict:!1})));if(i)return!!c.length;return c.reduce(((e,t)=>e+t.to-t.from),0)>=a}function Ks(e,t){return t.nodes[e]?"node":t.marks[e]?"mark":null}function Js(e,t){const n="string"==typeof t?[t]:t;return Object.keys(e).reduce(((t,r)=>(n.includes(r)||(t[r]=e[r]),t)),{})}function Qs(e,t,n={},r={}){return Us(e,t,{slice:!1,parseOptions:n,errorOnInvalidContent:r.errorOnInvalidContent})}function Gs(e,t){const n=_s(t,e.schema),{from:r,to:o,empty:i}=e.selection,s=[];i?(e.storedMarks&&s.push(...e.storedMarks),s.push(...e.selection.$head.marks())):e.doc.nodesBetween(r,o,(e=>{s.push(...e.marks)}));const l=s.find((e=>e.type.name===n.name));return l?{...l.attrs}:{}}function Ys(e){return t=>function(e,t){for(let n=e.depth;n>0;n-=1){const r=e.node(n);if(t(r))return{pos:n>0?e.before(n):0,start:e.start(n),depth:n,node:r}}}(t.$from,e)}function Xs(e,t){const n=Ks("string"==typeof t?t:t.name,e.schema);return"node"===n?function(e,t){const n=ls(t,e.schema),{from:r,to:o}=e.selection,i=[];e.doc.nodesBetween(r,o,(e=>{i.push(e)}));const s=i.reverse().find((e=>e.type.name===n.name));return s?{...s.attrs}:{}}(e,t):"mark"===n?Gs(e,t):{}}function Zs(e){const t=function(e,t=JSON.stringify){const n={};return e.filter((e=>{const r=t(e);return!Object.prototype.hasOwnProperty.call(n,r)&&(n[r]=!0)}))}(e);return 1===t.length?t:t.filter(((e,n)=>{const r=t.filter(((e,t)=>t!==n));return!r.some((t=>e.oldRange.from>=t.oldRange.from&&e.oldRange.to<=t.oldRange.to&&e.newRange.from>=t.newRange.from&&e.newRange.to<=t.newRange.to))}))}function el(e,t,n){const r=[];return e===t?n.resolve(e).marks().forEach((t=>{const o=Is(n.resolve(e),t.type);o&&r.push({mark:t,...o})})):n.nodesBetween(e,t,((e,t)=>{e&&void 0!==(null==e?void 0:e.nodeSize)&&r.push(...e.marks.map((n=>({from:t,to:t+e.nodeSize,mark:n}))))})),r}function tl(e,t,n){return Object.fromEntries(Object.entries(n).filter((([n])=>{const r=e.find((e=>e.type===t&&e.name===n));return!!r&&r.attribute.keepOnSplit})))}function nl(e,t,n={}){const{empty:r,ranges:o}=e.selection,i=t?_s(t,e.schema):null;if(r)return!!(e.storedMarks||e.selection.$from.marks()).filter((e=>!i||i.name===e.type.name)).find((e=>Rs(e.attrs,n,{strict:!1})));let s=0;const l=[];if(o.forEach((({$from:t,$to:n})=>{const r=t.pos,o=n.pos;e.doc.nodesBetween(r,o,((e,t)=>{if(!e.isText&&!e.marks.length)return;const n=Math.max(r,t),i=Math.min(o,t+e.nodeSize);s+=i-n,l.push(...e.marks.map((e=>({mark:e,from:n,to:i}))))}))})),0===s)return!1;const a=l.filter((e=>!i||i.name===e.mark.type.name)).filter((e=>Rs(e.mark.attrs,n,{strict:!1}))).reduce(((e,t)=>e+t.to-t.from),0),c=l.filter((e=>!i||e.mark.type!==i&&e.mark.type.excludes(i))).reduce(((e,t)=>e+t.to-t.from),0);return(a>0?a+c:a)>=s}function rl(e,t){const{nodeExtensions:n}=is(t),r=n.find((t=>t.name===e));if(!r)return!1;const o=ds(rs(r,"group",{name:r.name,options:r.options,storage:r.storage}));return"string"==typeof o&&o.split(" ").includes("list")}function ol(e,{checkChildren:t=!0,ignoreWhitespace:n=!1}={}){var r;if(n){if("hardBreak"===e.type.name)return!0;if(e.isText)return/^\s*$/m.test(null!==(r=e.text)&&void 0!==r?r:"")}if(e.isText)return!e.text;if(e.isAtom||e.isLeaf)return!1;if(0===e.content.childCount)return!0;if(t){let r=!0;return e.content.forEach((e=>{!1!==r&&(ol(e,{ignoreWhitespace:n,checkChildren:t})||(r=!1))})),r}return!1}function il(e,t){const n=e.storedMarks||e.selection.$to.parentOffset&&e.selection.$from.marks();if(n){const r=n.filter((e=>null==t?void 0:t.includes(e.type.name)));e.tr.ensureMarks(r)}}const sl=(e,t)=>{const n=Ys((e=>e.type===t))(e.selection);if(!n)return!0;const r=e.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===r)return!0;const o=e.doc.nodeAt(r);return n.node.type!==(null==o?void 0:o.type)||!ht(e.doc,n.pos)||(e.join(n.pos),!0)},ll=(e,t)=>{const n=Ys((e=>e.type===t))(e.selection);if(!n)return!0;const r=e.doc.resolve(n.start).after(n.depth);if(void 0===r)return!0;const o=e.doc.nodeAt(r);return n.node.type!==(null==o?void 0:o.type)||!ht(e.doc,r)||(e.join(r),!0)};var al=Object.freeze({__proto__:null,blur:()=>({editor:e,view:t})=>(requestAnimationFrame((()=>{var n;e.isDestroyed||(t.dom.blur(),null===(n=null===window||void 0===window?void 0:window.getSelection())||void 0===n||n.removeAllRanges())})),!0),clearContent:(e=!1)=>({commands:t})=>t.setContent("",e),clearNodes:()=>({state:e,tr:t,dispatch:n})=>{const{selection:r}=t,{ranges:o}=r;return!n||(o.forEach((({$from:n,$to:r})=>{e.doc.nodesBetween(n.pos,r.pos,((e,n)=>{if(e.type.isText)return;const{doc:r,mapping:o}=t,i=r.resolve(o.map(n)),s=r.resolve(o.map(n+e.nodeSize)),l=i.blockRange(s);if(!l)return;const a=lt(l);if(e.type.isTextblock){const{defaultType:e}=i.parent.contentMatchAt(i.index());t.setNodeMarkup(l.start,e)}(a||0===a)&&t.lift(l,a)}))})),!0)},command:e=>t=>e(t),createParagraphNear:()=>({state:e,dispatch:t})=>Vi(e,t),cut:(e,t)=>({editor:n,tr:r})=>{const{state:o}=n,i=o.doc.slice(e.from,e.to);r.deleteRange(e.from,e.to);const s=r.mapping.map(t);return r.insert(s,i.content),r.setSelection(new It(r.doc.resolve(s-1))),!0},deleteCurrentNode:()=>({tr:e,dispatch:t})=>{const{selection:n}=e,r=n.$anchor.node();if(r.content.size>0)return!1;const o=e.selection.$anchor;for(let i=o.depth;i>0;i-=1){if(o.node(i).type===r.type){if(t){const t=o.before(i),n=o.after(i);e.delete(t,n).scrollIntoView()}return!0}}return!1},deleteNode:e=>({tr:t,state:n,dispatch:r})=>{const o=ls(e,n.schema),i=t.selection.$anchor;for(let e=i.depth;e>0;e-=1){if(i.node(e).type===o){if(r){const n=i.before(e),r=i.after(e);t.delete(n,r).scrollIntoView()}return!0}}return!1},deleteRange:e=>({tr:t,dispatch:n})=>{const{from:r,to:o}=e;return n&&t.delete(r,o),!0},deleteSelection:()=>({state:e,dispatch:t})=>Ai(e,t),enter:()=>({commands:e})=>e.keyboardShortcut("Enter"),exitCode:()=>({state:e,dispatch:t})=>((e,t)=>{let{$head:n,$anchor:r}=e.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let o=n.node(-1),i=n.indexAfter(-1),s=Hi(o.contentMatchAt(i));if(!s||!o.canReplaceWith(i,i,s))return!1;if(t){let r=n.after(),o=e.tr.replaceWith(r,r,s.createAndFill());o.setSelection(Pt.near(o.doc.resolve(r),1)),t(o.scrollIntoView())}return!0})(e,t),extendMarkRange:(e,t={})=>({tr:n,state:r,dispatch:o})=>{const i=_s(e,r.schema),{doc:s,selection:l}=n,{$from:a,from:c,to:u}=l;if(o){const e=Is(a,i,t);if(e&&e.from<=c&&e.to>=u){const t=It.create(s,e.from,e.to);n.setSelection(t)}}return!0},first:e=>t=>{const n="function"==typeof e?e(t):e;for(let e=0;e<n.length;e+=1)if(n[e](t))return!0;return!1},focus:(e=null,t={})=>({editor:n,view:r,tr:o,dispatch:i})=>{t={scrollIntoView:!0,...t};const s=()=>{(js()||"Android"===navigator.platform||/android/i.test(navigator.userAgent))&&r.dom.focus(),requestAnimationFrame((()=>{n.isDestroyed||(r.focus(),(null==t?void 0:t.scrollIntoView)&&n.commands.scrollIntoView())}))};if(r.hasFocus()&&null===e||!1===e)return!0;if(i&&null===e&&!$s(n.state.selection))return s(),!0;const l=Bs(o.doc,e)||n.state.selection,a=n.state.selection.eq(l);return i&&(a||o.setSelection(l),a&&o.storedMarks&&o.setStoredMarks(o.storedMarks),s()),!0},forEach:(e,t)=>n=>e.every(((e,r)=>t(e,{...n,index:r}))),insertContent:(e,t)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},e,t),insertContentAt:(e,t,n)=>({tr:r,dispatch:o,editor:i})=>{var s;if(o){let o;n={parseOptions:i.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};try{o=Us(t,i.schema,{parseOptions:{preserveWhitespace:"full",...n.parseOptions},errorOnInvalidContent:null!==(s=n.errorOnInvalidContent)&&void 0!==s?s:i.options.enableContentCheck})}catch(l){return i.emit("contentError",{editor:i,error:l,disableCollaboration:()=>{i.storage.collaboration&&(i.storage.collaboration.isDisabled=!0)}}),!1}let{from:a,to:c}="number"==typeof e?{from:e,to:e}:{from:e.from,to:e.to},u=!0,d=!0;if(("type"in o?[o]:o).forEach((e=>{e.check(),u=!!u&&(e.isText&&0===e.marks.length),d=!!d&&e.isBlock})),a===c&&d){const{parent:e}=r.doc.resolve(a);e.isTextblock&&!e.type.spec.code&&!e.childCount&&(a-=1,c+=1)}let f;if(u){if(Array.isArray(t))f=t.map((e=>e.text||"")).join("");else if(t instanceof I){let e="";t.forEach((t=>{t.text&&(e+=t.text)})),f=e}else f="object"==typeof t&&t&&t.text?t.text:t;r.insertText(f,a,c)}else f=o,r.replaceWith(a,c,f);n.updateSelection&&function(e,t,n){const r=e.steps.length-1;if(r<t)return;const o=e.steps[r];if(!(o instanceof nt||o instanceof rt))return;const i=e.mapping.maps[r];let s=0;i.forEach(((e,t,n,r)=>{0===s&&(s=r)})),e.setSelection(Pt.near(e.doc.resolve(s),n))}(r,r.steps.length-1,-1),n.applyInputRules&&r.setMeta("applyInputRules",{from:a,text:f}),n.applyPasteRules&&r.setMeta("applyPasteRules",{from:a,text:f})}return!0},joinBackward:()=>({state:e,dispatch:t})=>Pi(e,t),joinDown:()=>({state:e,dispatch:t})=>((e,t)=>{let n,r=e.selection;if(r instanceof $t){if(r.node.isTextblock||!ht(e.doc,r.to))return!1;n=r.to}else if(n=mt(e.doc,r.to,1),null==n)return!1;return t&&t(e.tr.join(n).scrollIntoView()),!0})(e,t),joinForward:()=>({state:e,dispatch:t})=>$i(e,t),joinItemBackward:()=>({state:e,dispatch:t,tr:n})=>{try{const r=mt(e.doc,e.selection.$from.pos,-1);return null!=r&&(n.join(r,2),t&&t(n),!0)}catch{return!1}},joinItemForward:()=>({state:e,dispatch:t,tr:n})=>{try{const r=mt(e.doc,e.selection.$from.pos,1);return null!=r&&(n.join(r,2),t&&t(n),!0)}catch{return!1}},joinTextblockBackward:()=>({state:e,dispatch:t})=>((e,t,n)=>{let r=Di(e,n);if(!r)return!1;let o=Ii(r);return!!o&&Ri(e,o,t)})(e,t),joinTextblockForward:()=>({state:e,dispatch:t})=>((e,t,n)=>{let r=_i(e,n);if(!r)return!1;let o=Bi(r);return!!o&&Ri(e,o,t)})(e,t),joinUp:()=>({state:e,dispatch:t})=>((e,t)=>{let n,r=e.selection,o=r instanceof $t;if(o){if(r.node.isTextblock||!ht(e.doc,r.from))return!1;n=r.from}else if(n=mt(e.doc,r.from,-1),null==n)return!1;if(t){let r=e.tr.join(n);o&&r.setSelection($t.create(r.doc,n-e.doc.resolve(n).nodeBefore.nodeSize)),t(r.scrollIntoView())}return!0})(e,t),keyboardShortcut:e=>({editor:t,view:n,tr:r,dispatch:o})=>{const i=function(e){const t=e.split(/-(?!$)/);let n,r,o,i,s=t[t.length-1];"Space"===s&&(s=" ");for(let l=0;l<t.length-1;l+=1){const e=t[l];if(/^(cmd|meta|m)$/i.test(e))i=!0;else if(/^a(lt)?$/i.test(e))n=!0;else if(/^(c|ctrl|control)$/i.test(e))r=!0;else if(/^s(hift)?$/i.test(e))o=!0;else{if(!/^mod$/i.test(e))throw new Error(`Unrecognized modifier name: ${e}`);js()||Ws()?i=!0:r=!0}}return n&&(s=`Alt-${s}`),r&&(s=`Ctrl-${s}`),i&&(s=`Meta-${s}`),o&&(s=`Shift-${s}`),s}(e).split(/-(?!$)/),s=i.find((e=>!["Alt","Ctrl","Meta","Shift"].includes(e))),l=new KeyboardEvent("keydown",{key:"Space"===s?" ":s,altKey:i.includes("Alt"),ctrlKey:i.includes("Ctrl"),metaKey:i.includes("Meta"),shiftKey:i.includes("Shift"),bubbles:!0,cancelable:!0}),a=t.captureTransaction((()=>{n.someProp("handleKeyDown",(e=>e(n,l)))}));return null==a||a.steps.forEach((e=>{const t=e.map(r.mapping);t&&o&&r.maybeStep(t)})),!0},lift:(e,t={})=>({state:n,dispatch:r})=>!!qs(n,ls(e,n.schema),t)&&((e,t)=>{let{$from:n,$to:r}=e.selection,o=n.blockRange(r),i=o&&lt(o);return null!=i&&(t&&t(e.tr.lift(o,i).scrollIntoView()),!0)})(n,r),liftEmptyBlock:()=>({state:e,dispatch:t})=>Ui(e,t),liftListItem:e=>({state:t,dispatch:n})=>Zi(ls(e,t.schema))(t,n),newlineInCode:()=>({state:e,dispatch:t})=>ji(e,t),resetAttributes:(e,t)=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null;const l=Ks("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(i=ls(e,r.schema)),"mark"===l&&(s=_s(e,r.schema)),o&&n.selection.ranges.forEach((e=>{r.doc.nodesBetween(e.$from.pos,e.$to.pos,((e,r)=>{i&&i===e.type&&n.setNodeMarkup(r,void 0,Js(e.attrs,t)),s&&e.marks.length&&e.marks.forEach((o=>{s===o.type&&n.addMark(r,r+e.nodeSize,s.create(Js(o.attrs,t)))}))}))})),!0)},scrollIntoView:()=>({tr:e,dispatch:t})=>(t&&e.scrollIntoView(),!0),selectAll:()=>({tr:e,dispatch:t})=>{if(t){const t=new Bt(e.doc);e.setSelection(t)}return!0},selectNodeBackward:()=>({state:e,dispatch:t})=>zi(e,t),selectNodeForward:()=>({state:e,dispatch:t})=>Fi(e,t),selectParentNode:()=>({state:e,dispatch:t})=>((e,t)=>{let n,{$from:r,to:o}=e.selection,i=r.sharedDepth(o);return 0!=i&&(n=r.before(i),t&&t(e.tr.setSelection($t.create(e.doc,n))),!0)})(e,t),selectTextblockEnd:()=>({state:e,dispatch:t})=>Qi(e,t),selectTextblockStart:()=>({state:e,dispatch:t})=>Ji(e,t),setContent:(e,t=!1,n={},r={})=>({editor:o,tr:i,dispatch:s,commands:l})=>{var a,c;const{doc:u}=i;if("full"!==n.preserveWhitespace){const l=Qs(e,o.schema,n,{errorOnInvalidContent:null!==(a=r.errorOnInvalidContent)&&void 0!==a?a:o.options.enableContentCheck});return s&&i.replaceWith(0,u.content.size,l).setMeta("preventUpdate",!t),!0}return s&&i.setMeta("preventUpdate",!t),l.insertContentAt({from:0,to:u.content.size},e,{parseOptions:n,errorOnInvalidContent:null!==(c=r.errorOnInvalidContent)&&void 0!==c?c:o.options.enableContentCheck})},setMark:(e,t={})=>({tr:n,state:r,dispatch:o})=>{const{selection:i}=n,{empty:s,ranges:l}=i,a=_s(e,r.schema);if(o)if(s){const e=Gs(r,a);n.addStoredMark(a.create({...e,...t}))}else l.forEach((e=>{const o=e.$from.pos,i=e.$to.pos;r.doc.nodesBetween(o,i,((e,r)=>{const s=Math.max(r,o),l=Math.min(r+e.nodeSize,i);e.marks.find((e=>e.type===a))?e.marks.forEach((e=>{a===e.type&&n.addMark(s,l,a.create({...e.attrs,...t}))})):n.addMark(s,l,a.create(t))}))}));return function(e,t,n){var r;const{selection:o}=t;let i=null;if($s(o)&&(i=o.$cursor),i){const t=null!==(r=e.storedMarks)&&void 0!==r?r:i.marks();return!!n.isInSet(t)||!t.some((e=>e.type.excludes(n)))}const{ranges:s}=o;return s.some((({$from:t,$to:r})=>{let o=0===t.depth&&e.doc.inlineContent&&e.doc.type.allowsMarkType(n);return e.doc.nodesBetween(t.pos,r.pos,((e,t,r)=>{if(o)return!1;if(e.isInline){const t=!r||r.type.allowsMarkType(n),i=!!n.isInSet(e.marks)||!e.marks.some((e=>e.type.excludes(n)));o=t&&i}return!o})),o}))}(r,n,a)},setMeta:(e,t)=>({tr:n})=>(n.setMeta(e,t),!0),setNode:(e,t={})=>({state:n,dispatch:r,chain:o})=>{const i=ls(e,n.schema);let s;return n.selection.$anchor.sameParent(n.selection.$head)&&(s=n.selection.$anchor.parent.attrs),i.isTextblock?o().command((({commands:e})=>!!Gi(i,{...s,...t})(n)||e.clearNodes())).command((({state:e})=>Gi(i,{...s,...t})(e,r))).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:e=>({tr:t,dispatch:n})=>{if(n){const{doc:n}=t,r=Fs(e,0,n.content.size),o=$t.create(n,r);t.setSelection(o)}return!0},setTextSelection:e=>({tr:t,dispatch:n})=>{if(n){const{doc:n}=t,{from:r,to:o}="number"==typeof e?{from:e,to:e}:e,i=It.atStart(n).from,s=It.atEnd(n).to,l=Fs(r,i,s),a=Fs(o,i,s),c=It.create(n,l,a);t.setSelection(c)}return!0},sinkListItem:e=>({state:t,dispatch:n})=>{const r=ls(e,t.schema);return(o=r,function(e,t){let{$from:n,$to:r}=e.selection,i=n.blockRange(r,(e=>e.childCount>0&&e.firstChild.type==o));if(!i)return!1;let s=i.startIndex;if(0==s)return!1;let l=i.parent,a=l.child(s-1);if(a.type!=o)return!1;if(t){let n=a.lastChild&&a.lastChild.type==l.type,r=I.from(n?o.create():null),s=new H(I.from(o.create(null,I.from(l.type.create(null,r)))),n?3:1,0),c=i.start,u=i.end;t(e.tr.step(new rt(c-(n?3:1),u,c,u,s,1,!0)).scrollIntoView())}return!0})(t,n);var o},splitBlock:({keepMarks:e=!0}={})=>({tr:t,state:n,dispatch:r,editor:o})=>{const{selection:i,doc:s}=t,{$from:l,$to:a}=i,c=tl(o.extensionManager.attributes,l.node().type.name,l.node().attrs);if(i instanceof $t&&i.node.isBlock)return!(!l.parentOffset||!ft(s,l.pos))&&(r&&(e&&il(n,o.extensionManager.splittableMarks),t.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;const u=a.parentOffset===a.parent.content.size,d=0===l.depth?void 0:function(e){for(let t=0;t<e.edgeCount;t+=1){const{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(l.node(-1).contentMatchAt(l.indexAfter(-1)));let f=u&&d?[{type:d,attrs:c}]:void 0,h=ft(t.doc,t.mapping.map(l.pos),1,f);if(f||h||!ft(t.doc,t.mapping.map(l.pos),1,d?[{type:d}]:void 0)||(h=!0,f=d?[{type:d,attrs:c}]:void 0),r){if(h&&(i instanceof It&&t.deleteSelection(),t.split(t.mapping.map(l.pos),1,f),d&&!u&&!l.parentOffset&&l.parent.type!==d)){const e=t.mapping.map(l.before()),n=t.doc.resolve(e);l.node(-1).canReplaceWith(n.index(),n.index()+1,d)&&t.setNodeMarkup(t.mapping.map(l.before()),d)}e&&il(n,o.extensionManager.splittableMarks),t.scrollIntoView()}return h},splitListItem:(e,t={})=>({tr:n,state:r,dispatch:o,editor:i})=>{var s;const l=ls(e,r.schema),{$from:a,$to:c}=r.selection,u=r.selection.node;if(u&&u.isBlock||a.depth<2||!a.sameParent(c))return!1;const d=a.node(-1);if(d.type!==l)return!1;const f=i.extensionManager.attributes;if(0===a.parent.content.size&&a.node(-1).childCount===a.indexAfter(-1)){if(2===a.depth||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(o){let e=I.empty;const r=a.index(-1)?1:a.index(-2)?2:3;for(let t=a.depth-r;t>=a.depth-3;t-=1)e=I.from(a.node(t).copy(e));const o=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,i={...tl(f,a.node().type.name,a.node().attrs),...t},c=(null===(s=l.contentMatch.defaultType)||void 0===s?void 0:s.createAndFill(i))||void 0;e=e.append(I.from(l.createAndFill(null,c)||void 0));const u=a.before(a.depth-(r-1));n.replace(u,a.after(-o),new H(e,4-r,0));let d=-1;n.doc.nodesBetween(u,n.doc.content.size,((e,t)=>{if(d>-1)return!1;e.isTextblock&&0===e.content.size&&(d=t+1)})),d>-1&&n.setSelection(It.near(n.doc.resolve(d))),n.scrollIntoView()}return!0}const h=c.pos===a.end()?d.contentMatchAt(0).defaultType:null,p={...tl(f,d.type.name,d.attrs),...t},m={...tl(f,a.node().type.name,a.node().attrs),...t};n.delete(a.pos,c.pos);const g=h?[{type:l,attrs:p},{type:h,attrs:m}]:[{type:l,attrs:p}];if(!ft(n.doc,a.pos,2))return!1;if(o){const{selection:e,storedMarks:t}=r,{splittableMarks:s}=i.extensionManager,l=t||e.$to.parentOffset&&e.$from.marks();if(n.split(a.pos,2,g).scrollIntoView(),!l||!o)return!0;const c=l.filter((e=>s.includes(e.type.name)));n.ensureMarks(c)}return!0},toggleList:(e,t,n,r={})=>({editor:o,tr:i,state:s,dispatch:l,chain:a,commands:c,can:u})=>{const{extensions:d,splittableMarks:f}=o.extensionManager,h=ls(e,s.schema),p=ls(t,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:v}=m,b=y.blockRange(v),k=g||m.$to.parentOffset&&m.$from.marks();if(!b)return!1;const w=Ys((e=>rl(e.type.name,d)))(m);if(b.depth>=1&&w&&b.depth-w.depth<=1){if(w.node.type===h)return c.liftListItem(p);if(rl(w.node.type.name,d)&&h.validContent(w.node.content)&&l)return a().command((()=>(i.setNodeMarkup(w.pos,h),!0))).command((()=>sl(i,h))).command((()=>ll(i,h))).run()}return n&&k&&l?a().command((()=>{const e=u().wrapInList(h,r),t=k.filter((e=>f.includes(e.type.name)));return i.ensureMarks(t),!!e||c.clearNodes()})).wrapInList(h,r).command((()=>sl(i,h))).command((()=>ll(i,h))).run():a().command((()=>!!u().wrapInList(h,r)||c.clearNodes())).wrapInList(h,r).command((()=>sl(i,h))).command((()=>ll(i,h))).run()},toggleMark:(e,t={},n={})=>({state:r,commands:o})=>{const{extendEmptyMarkRange:i=!1}=n,s=_s(e,r.schema);return nl(r,s,t)?o.unsetMark(s,{extendEmptyMarkRange:i}):o.setMark(s,t)},toggleNode:(e,t,n={})=>({state:r,commands:o})=>{const i=ls(e,r.schema),s=ls(t,r.schema),l=qs(r,i,n);let a;return r.selection.$anchor.sameParent(r.selection.$head)&&(a=r.selection.$anchor.parent.attrs),l?o.setNode(s,a):o.setNode(i,{...a,...n})},toggleWrap:(e,t={})=>({state:n,commands:r})=>{const o=ls(e,n.schema);return qs(n,o,t)?r.lift(o):r.wrapIn(o,t)},undoInputRule:()=>({state:e,dispatch:t})=>{const n=e.plugins;for(let r=0;r<n.length;r+=1){const o=n[r];let i;if(o.spec.isInputRules&&(i=o.getState(e))){if(t){const t=e.tr,n=i.transform;for(let e=n.steps.length-1;e>=0;e-=1)t.step(n.steps[e].invert(n.docs[e]));if(i.text){const n=t.doc.resolve(i.from).marks();t.replaceWith(i.from,i.to,e.schema.text(i.text,n))}else t.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:e,dispatch:t})=>{const{selection:n}=e,{empty:r,ranges:o}=n;return r||t&&o.forEach((t=>{e.removeMark(t.$from.pos,t.$to.pos)})),!0},unsetMark:(e,t={})=>({tr:n,state:r,dispatch:o})=>{var i;const{extendEmptyMarkRange:s=!1}=t,{selection:l}=n,a=_s(e,r.schema),{$from:c,empty:u,ranges:d}=l;if(!o)return!0;if(u&&s){let{from:e,to:t}=l;const r=null===(i=c.marks().find((e=>e.type===a)))||void 0===i?void 0:i.attrs,o=Is(c,a,r);o&&(e=o.from,t=o.to),n.removeMark(e,t,a)}else d.forEach((e=>{n.removeMark(e.$from.pos,e.$to.pos,a)}));return n.removeStoredMark(a),!0},updateAttributes:(e,t={})=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null;const l=Ks("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(i=ls(e,r.schema)),"mark"===l&&(s=_s(e,r.schema)),o&&n.selection.ranges.forEach((e=>{const o=e.$from.pos,l=e.$to.pos;let a,c,u,d;n.selection.empty?r.doc.nodesBetween(o,l,((e,t)=>{i&&i===e.type&&(u=Math.max(t,o),d=Math.min(t+e.nodeSize,l),a=t,c=e)})):r.doc.nodesBetween(o,l,((e,r)=>{r<o&&i&&i===e.type&&(u=Math.max(r,o),d=Math.min(r+e.nodeSize,l),a=r,c=e),r>=o&&r<=l&&(i&&i===e.type&&n.setNodeMarkup(r,void 0,{...e.attrs,...t}),s&&e.marks.length&&e.marks.forEach((i=>{if(s===i.type){const a=Math.max(r,o),c=Math.min(r+e.nodeSize,l);n.addMark(a,c,s.create({...i.attrs,...t}))}})))})),c&&(void 0!==a&&n.setNodeMarkup(a,void 0,{...c.attrs,...t}),s&&c.marks.length&&c.marks.forEach((e=>{s===e.type&&n.addMark(u,d,s.create({...e.attrs,...t}))})))})),!0)},wrapIn:(e,t={})=>({state:n,dispatch:r})=>function(e,t=null){return function(n,r){let{$from:o,$to:i}=n.selection,s=o.blockRange(i),l=s&&at(s,e,t);return!!l&&(r&&r(n.tr.wrap(s,l).scrollIntoView()),!0)}}(ls(e,n.schema),t)(n,r),wrapInList:(e,t={})=>({state:n,dispatch:r})=>Xi(ls(e,n.schema),t)(n,r)});const cl=Ns.create({name:"commands",addCommands:()=>({...al})}),ul=Ns.create({name:"drop",addProseMirrorPlugins(){return[new Yt({key:new en("tiptapDrop"),props:{handleDrop:(e,t,n,r)=>{this.editor.emit("drop",{editor:this.editor,event:t,slice:n,moved:r})}}})]}}),dl=Ns.create({name:"editable",addProseMirrorPlugins(){return[new Yt({key:new en("editable"),props:{editable:()=>this.editor.options.editable}})]}}),fl=new en("focusEvents"),hl=Ns.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:e}=this;return[new Yt({key:fl,props:{handleDOMEvents:{focus:(t,n)=>{e.isFocused=!0;const r=e.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1},blur:(t,n)=>{e.isFocused=!1;const r=e.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1}}}})]}}),pl=Ns.create({name:"keymap",addKeyboardShortcuts(){const e=()=>this.editor.commands.first((({commands:e})=>[()=>e.undoInputRule(),()=>e.command((({tr:t})=>{const{selection:n,doc:r}=t,{empty:o,$anchor:i}=n,{pos:s,parent:l}=i,a=i.parent.isTextblock&&s>0?t.doc.resolve(s-1):i,c=a.parent.type.spec.isolating,u=i.pos-i.parentOffset,d=c&&1===a.parent.childCount?u===i.pos:Pt.atStart(r).from===s;return!(!o||!l.type.isTextblock||l.textContent.length||!d||d&&"paragraph"===i.parent.type.name)&&e.clearNodes()})),()=>e.deleteSelection(),()=>e.joinBackward(),()=>e.selectNodeBackward()])),t=()=>this.editor.commands.first((({commands:e})=>[()=>e.deleteSelection(),()=>e.deleteCurrentNode(),()=>e.joinForward(),()=>e.selectNodeForward()])),n={Enter:()=>this.editor.commands.first((({commands:e})=>[()=>e.newlineInCode(),()=>e.createParagraphNear(),()=>e.liftEmptyBlock(),()=>e.splitBlock()])),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:e,"Mod-Backspace":e,"Shift-Backspace":e,Delete:t,"Mod-Delete":t,"Mod-a":()=>this.editor.commands.selectAll()},r={...n},o={...n,"Ctrl-h":e,"Alt-Backspace":e,"Ctrl-d":t,"Ctrl-Alt-Backspace":t,"Alt-Delete":t,"Alt-d":t,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return js()||Ws()?o:r},addProseMirrorPlugins(){return[new Yt({key:new en("clearDocument"),appendTransaction:(e,t,n)=>{if(e.some((e=>e.getMeta("composition"))))return;const r=e.some((e=>e.docChanged))&&!t.doc.eq(n.doc),o=e.some((e=>e.getMeta("preventClearDocument")));if(!r||o)return;const{empty:i,from:s,to:l}=t.selection,a=Pt.atStart(t.doc).from,c=Pt.atEnd(t.doc).to;if(i||!(s===a&&l===c))return;if(!ol(n.doc))return;const u=n.tr,d=es({state:n,transaction:u}),{commands:f}=new ts({editor:this.editor,state:d});return f.clearNodes(),u.steps.length?u:void 0}})]}}),ml=Ns.create({name:"paste",addProseMirrorPlugins(){return[new Yt({key:new en("tiptapPaste"),props:{handlePaste:(e,t,n)=>{this.editor.emit("paste",{editor:this.editor,event:t,slice:n})}}})]}}),gl=Ns.create({name:"tabindex",addProseMirrorPlugins(){return[new Yt({key:new en("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class yl{get name(){return this.node.type.name}constructor(e,t,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=r}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return null!==(e=this.actualDepth)&&void 0!==e?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(0===this.depth)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new yl(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new yl(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new yl(e,this.editor)}get children(){const e=[];return this.node.content.forEach(((t,n)=>{const r=t.isBlock&&!t.isTextblock,o=t.isAtom&&!t.isText,i=this.pos+n+(o?0:1),s=this.resolvedPos.doc.resolve(i);if(!r&&s.depth<=this.depth)return;const l=new yl(s,this.editor,r,r?t:null);r&&(l.actualDepth=this.depth+1),e.push(new yl(s,this.editor,r,r?t:null))})),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,r=this.parent;for(;r&&!n;){if(r.node.type.name===e)if(Object.keys(t).length>0){const e=r.node.attrs,n=Object.keys(t);for(let r=0;r<n.length;r+=1){const o=n[r];if(e[o]!==t[o])break}}else n=r;r=r.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let r=[];if(!this.children||0===this.children.length)return r;const o=Object.keys(t);return this.children.forEach((i=>{if(!(n&&r.length>0)){if(i.node.type.name===e){o.every((e=>t[e]===i.node.attrs[e]))&&r.push(i)}n&&r.length>0||(r=r.concat(i.querySelectorAll(e,t,n)))}})),r}setAttribute(e){const{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}}class vl extends ns{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:e})=>{throw e},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",(({event:e,slice:t,moved:n})=>this.options.onDrop(e,t,n))),this.on("paste",(({event:e,slice:t})=>this.options.onPaste(e,t))),window.setTimeout((()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)}),0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(e,t){const n=document.querySelector("style[data-tiptap-style]");if(null!==n)return n;const r=document.createElement("style");return t&&r.setAttribute("nonce",t),r.setAttribute("data-tiptap-style",""),r.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(r),r}('.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: "liga" 0; /* the above doesn\'t seem to work in Edge */\n}\n\n.ProseMirror [contenteditable="false"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable="false"] [contenteditable="true"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: "";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n\n.tippy-box[data-animation=fade][data-state=hidden] {\n  opacity: 0\n}',this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const n=us(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}unregisterPlugin(e){if(this.isDestroyed)return;const t=this.state.plugins;let n=t;if([].concat(e).forEach((e=>{const t="string"==typeof e?`${e}$`:e.key;n=n.filter((e=>!e.key.startsWith(t)))})),t.length===n.length)return;const r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}createExtensionManager(){var e,t;const n=[...this.options.enableCoreExtensions?[dl,Ps.configure({blockSeparator:null===(t=null===(e=this.options.coreExtensionOptions)||void 0===e?void 0:e.clipboardTextSerializer)||void 0===t?void 0:t.blockSeparator}),cl,hl,pl,gl,ul,ml].filter((e=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[e.name])):[],...this.options.extensions].filter((e=>["extension","node","mark"].includes(null==e?void 0:e.type)));this.extensionManager=new Os(n,this)}createCommandManager(){this.commandManager=new ts({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=Qs(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(o){if(!(o instanceof Error&&["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(o.message)))throw o;this.emit("contentError",{editor:this,error:o,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter((e=>"collaboration"!==e.name)),this.createExtensionManager()}}),t=Qs(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}const n=Bs(t,this.options.autofocus);this.view=new hi(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...null===(e=this.options.editorProps)||void 0===e?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:Qt.create({doc:t,selection:n||void 0})});const r=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(r),this.createNodeViews(),this.prependClass();this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction)return this.capturedTransaction?void e.steps.forEach((e=>{var t;return null===(t=this.capturedTransaction)||void 0===t?void 0:t.step(e)})):void(this.capturedTransaction=e);const t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});const r=e.getMeta("focus"),o=e.getMeta("blur");r&&this.emit("focus",{editor:this,event:r.event,transaction:e}),o&&this.emit("blur",{editor:this,event:o.event,transaction:e}),e.docChanged&&!e.getMeta("preventUpdate")&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return Xs(this.state,e)}isActive(e,t){const n="string"==typeof e?e:null,r="string"==typeof e?t:e;return function(e,t,n={}){if(!t)return qs(e,null,n)||nl(e,null,n);const r=Ks(t,e.schema);return"node"===r?qs(e,t,n):"mark"===r&&nl(e,t,n)}(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return gs(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t="\n\n",textSerializers:n={}}=e||{};return function(e,t){return As(e,{from:0,to:e.content.size},t)}(this.state.doc,{blockSeparator:t,textSerializers:{...Ds(this.schema),...n}})}get isEmpty(){return ol(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){const e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(null===(e=this.view)||void 0===e?void 0:e.docView)}$node(e,t){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new yl(t,this)}get $doc(){return this.$pos(0)}}function bl(e){return new vs({find:e.find,handler:({state:t,range:n,match:r})=>{const o=ds(e.getAttributes,void 0,r);if(!1===o||null===o)return null;const{tr:i}=t,s=r[r.length-1],l=r[0];if(s){const r=l.search(/\S/),a=n.from+l.indexOf(s),c=a+s.length;if(el(n.from,n.to,t.doc).filter((t=>t.mark.type.excluded.find((n=>n===e.type&&n!==t.mark.type)))).filter((e=>e.to>a)).length)return null;c<n.to&&i.delete(c,n.to),a>n.from&&i.delete(n.from+r,a);const u=n.from+r+s.length;i.addMark(n.from+r,u,e.type.create(o||{})),i.removeStoredMark(e.type)}}})}function kl(e){return new vs({find:e.find,handler:({state:t,range:n,match:r})=>{const o=t.doc.resolve(n.from),i=ds(e.getAttributes,void 0,r)||{};if(!o.node(-1).canReplaceWith(o.index(-1),o.indexAfter(-1),e.type))return null;t.tr.delete(n.from,n.to).setBlockType(n.from,n.from,e.type,i)}})}function wl(e){return new vs({find:e.find,handler:({state:t,range:n,match:r,chain:o})=>{const i=ds(e.getAttributes,void 0,r)||{},s=t.tr.delete(n.from,n.to),l=s.doc.resolve(n.from).blockRange(),a=l&&at(l,e.type,i);if(!a)return null;if(s.wrap(l,a),e.keepMarks&&e.editor){const{selection:n,storedMarks:r}=t,{splittableMarks:o}=e.editor.extensionManager,i=r||n.$to.parentOffset&&n.$from.marks();if(i){const e=i.filter((e=>o.includes(e.type.name)));s.ensureMarks(e)}}if(e.keepAttributes){const t="bulletList"===e.type.name||"orderedList"===e.type.name?"listItem":"taskList";o().updateAttributes(t,i).run()}const c=s.doc.resolve(n.from-1).nodeBefore;c&&c.type===e.type&&ht(s.doc,n.from-1)&&(!e.joinPredicate||e.joinPredicate(r,c))&&s.join(n.from-1)}})}class Sl{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=ds(rs(this,"addOptions",{name:this.name}))),this.storage=ds(rs(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Sl(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>Ss(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new Sl(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=ds(rs(t,"addOptions",{name:t.name})),t.storage=ds(rs(t,"addStorage",{name:t.name,options:t.options})),t}}function xl(e){return new Cs({find:e.find,handler:({state:t,range:n,match:r,pasteEvent:o})=>{const i=ds(e.getAttributes,void 0,r,o);if(!1===i||null===i)return null;const{tr:s}=t,l=r[r.length-1],a=r[0];let c=n.to;if(l){const r=a.search(/\S/),o=n.from+a.indexOf(l),u=o+l.length;if(el(n.from,n.to,t.doc).filter((t=>t.mark.type.excluded.find((n=>n===e.type&&n!==t.mark.type)))).filter((e=>e.to>o)).length)return null;u<n.to&&s.delete(u,n.to),o>n.from&&s.delete(n.from+r,o),c=n.from+r+l.length,s.addMark(n.from+r,c,e.type.create(i||{})),s.removeStoredMark(e.type)}}})}const Cl=n(A());function El(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ml,Tl={exports:{}},Ol={};Tl.exports=function(){if(Ml)return Ol;Ml=1;var e=m,t="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=e.useState,r=e.useEffect,o=e.useLayoutEffect,i=e.useDebugValue;function s(e){var n=e.getSnapshot;e=e.value;try{var r=n();return!t(e,r)}catch(o){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var l=t(),a=n({inst:{value:l,getSnapshot:t}}),c=a[0].inst,u=a[1];return o((function(){c.value=l,c.getSnapshot=t,s(c)&&u({inst:c})}),[e,l,t]),r((function(){return s(c)&&u({inst:c}),e((function(){s(c)&&u({inst:c})}))}),[e]),i(l),l};return Ol.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:l,Ol}();var Nl=Tl.exports;const Al=(...e)=>t=>{e.forEach((e=>{"function"==typeof e?e(t):e&&(e.current=t)}))},Dl=({contentComponent:e})=>{const t=Nl.useSyncExternalStore(e.subscribe,e.getSnapshot,e.getServerSnapshot);return m.createElement(m.Fragment,null,Object.values(t))};class Pl extends m.Component{constructor(e){var t;super(e),this.editorContentRef=m.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:Boolean(null===(t=e.editor)||void 0===t?void 0:t.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){const e=this.props.editor;if(e&&!e.isDestroyed&&e.options.element){if(e.contentComponent)return;const t=this.editorContentRef.current;t.append(...e.options.element.childNodes),e.setOptions({element:t}),e.contentComponent=function(){const e=new Set;let t={};return{subscribe:t=>(e.add(t),()=>{e.delete(t)}),getSnapshot:()=>t,getServerSnapshot:()=>t,setRenderer(n,r){t={...t,[n]:Cl.createPortal(r.reactElement,r.element,n)},e.forEach((e=>e()))},removeRenderer(n){const r={...t};delete r[n],t=r,e.forEach((e=>e()))}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=e.contentComponent.subscribe((()=>{this.setState((e=>e.hasContentComponentInitialized?e:{hasContentComponentInitialized:!0})),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()}))),e.createNodeViews(),this.initialized=!0}}componentWillUnmount(){const e=this.props.editor;if(!e)return;if(this.initialized=!1,e.isDestroyed||e.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),e.contentComponent=null,!e.options.element.firstChild)return;const t=document.createElement("div");t.append(...e.options.element.childNodes),e.setOptions({element:t})}render(){const{editor:e,innerRef:t,...n}=this.props;return m.createElement(m.Fragment,null,m.createElement("div",{ref:Al(t,this.editorContentRef),...n}),(null==e?void 0:e.contentComponent)&&m.createElement(Dl,{contentComponent:e.contentComponent}))}}const Rl=p.forwardRef(((e,t)=>{const n=m.useMemo((()=>Math.floor(4294967295*Math.random()).toString()),[e.editor]);return m.createElement(Pl,{key:n,innerRef:t,...e})})),Ll=m.memo(Rl);var zl,Il=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var r,o,i;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(o=r;0!==o--;)if(!e(t[o],n[o]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;for(o of t.entries())if(!e(o[1],n.get(o[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(o=r;0!==o--;)if(t[o]!==n[o])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(o=r;0!==o--;)if(!Object.prototype.hasOwnProperty.call(n,i[o]))return!1;for(o=r;0!==o--;){var s=i[o];if(("_owner"!==s||!t.$$typeof)&&!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n},_l=El(Il),$l={exports:{}},Fl={};$l.exports=function(){if(zl)return Fl;zl=1;var e=m,t=Nl,n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=t.useSyncExternalStore,o=e.useRef,i=e.useEffect,s=e.useMemo,l=e.useDebugValue;return Fl.useSyncExternalStoreWithSelector=function(e,t,a,c,u){var d=o(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=s((function(){function e(e){if(!i){if(i=!0,r=e,e=c(e),void 0!==u&&f.hasValue){var t=f.value;if(u(t,e))return o=t}return o=e}if(t=o,n(r,e))return t;var s=c(e);return void 0!==u&&u(t,s)?t:(r=e,o=s)}var r,o,i=!1,s=void 0===a?null:a;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]}),[t,a,c,u]);var h=r(e,d[0],d[1]);return i((function(){f.hasValue=!0,f.value=h}),[h]),l(h),h},Fl}();var Bl=$l.exports;const jl="undefined"!=typeof window?p.useLayoutEffect:p.useEffect;class Hl{constructor(e){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=e,this.lastSnapshot={editor:e,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}watch(e){if(this.editor=e,this.editor){const e=()=>{this.transactionNumber+=1,this.subscribers.forEach((e=>e()))},t=this.editor;return t.on("transaction",e),()=>{t.off("transaction",e)}}}}const Vl="undefined"==typeof window,Ul=Vl||Boolean("undefined"!=typeof window&&window.next);class Wl{constructor(e){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=e,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(e){this.editor=e,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach((e=>e()))}getInitialEditor(){return void 0===this.options.current.immediatelyRender?Vl||Ul?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){const e={...this.options.current,onBeforeCreate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onBeforeCreate)||void 0===n?void 0:n.call(t,...e)},onBlur:(...e)=>{var t,n;return null===(n=(t=this.options.current).onBlur)||void 0===n?void 0:n.call(t,...e)},onCreate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onCreate)||void 0===n?void 0:n.call(t,...e)},onDestroy:(...e)=>{var t,n;return null===(n=(t=this.options.current).onDestroy)||void 0===n?void 0:n.call(t,...e)},onFocus:(...e)=>{var t,n;return null===(n=(t=this.options.current).onFocus)||void 0===n?void 0:n.call(t,...e)},onSelectionUpdate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onSelectionUpdate)||void 0===n?void 0:n.call(t,...e)},onTransaction:(...e)=>{var t,n;return null===(n=(t=this.options.current).onTransaction)||void 0===n?void 0:n.call(t,...e)},onUpdate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onUpdate)||void 0===n?void 0:n.call(t,...e)},onContentError:(...e)=>{var t,n;return null===(n=(t=this.options.current).onContentError)||void 0===n?void 0:n.call(t,...e)},onDrop:(...e)=>{var t,n;return null===(n=(t=this.options.current).onDrop)||void 0===n?void 0:n.call(t,...e)},onPaste:(...e)=>{var t,n;return null===(n=(t=this.options.current).onPaste)||void 0===n?void 0:n.call(t,...e)}};return new vl(e)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(e){return this.subscriptions.add(e),()=>{this.subscriptions.delete(e)}}static compareOptions(e,t){return Object.keys(e).every((n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&e.extensions&&t.extensions?e.extensions.length===t.extensions.length&&e.extensions.every(((e,n)=>{var r;return e===(null===(r=t.extensions)||void 0===r?void 0:r[n])})):e[n]===t[n])))}onRender(e){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===e.length?Wl.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(e),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(e){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps)return void(this.previousDeps=e);if(this.previousDeps.length===e.length&&this.previousDeps.every(((t,n)=>t===e[n])))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=e}scheduleDestroy(){const e=this.instanceId,t=this.editor;this.scheduledDestructionTimeout=setTimeout((()=>{this.isComponentMounted&&this.instanceId===e?t&&t.setOptions(this.options.current):t&&!t.isDestroyed&&(t.destroy(),this.instanceId===e&&this.setEditor(null))}),1)}}function ql(e={},t=[]){const n=p.useRef(e);n.current=e;const[r]=p.useState((()=>new Wl(n))),o=Nl.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return p.useDebugValue(o),p.useEffect(r.onRender(t)),function(e){var t;const[n]=p.useState((()=>new Hl(e.editor))),r=Bl.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,e.selector,null!==(t=e.equalityFn)&&void 0!==t?t:_l);jl((()=>n.watch(e.editor)),[e.editor,n]),p.useDebugValue(r)}({editor:o,selector:({transactionNumber:t})=>!1===e.shouldRerenderOnTransaction?null:e.immediatelyRender&&0===t?0:t+1}),o}p.createContext({editor:null}).Consumer;const Kl=p.createContext({onDragStart:void 0});m.forwardRef(((e,t)=>{const{onDragStart:n}=p.useContext(Kl),r=e.as||"div";return m.createElement(r,{...e,ref:t,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...e.style}})}));const Jl=/^\s*>\s$/,Ql=Sl.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:e}){return["blockquote",as(this.options.HTMLAttributes,e),0]},addCommands(){return{setBlockquote:()=>({commands:e})=>e.wrapIn(this.name),toggleBlockquote:()=>({commands:e})=>e.toggleWrap(this.name),unsetBlockquote:()=>({commands:e})=>e.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[wl({find:Jl,type:this.type})]}}),Gl=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,Yl=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,Xl=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,Zl=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,ea=xs.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:e=>"normal"!==e.style.fontWeight&&null},{style:"font-weight=400",clearMark:e=>e.type.name===this.name},{style:"font-weight",getAttrs:e=>/^(bold(er)?|[5-9]\d{2,})$/.test(e)&&null}]},renderHTML({HTMLAttributes:e}){return["strong",as(this.options.HTMLAttributes,e),0]},addCommands(){return{setBold:()=>({commands:e})=>e.setMark(this.name),toggleBold:()=>({commands:e})=>e.toggleMark(this.name),unsetBold:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[bl({find:Gl,type:this.type}),bl({find:Xl,type:this.type})]},addPasteRules(){return[xl({find:Yl,type:this.type}),xl({find:Zl,type:this.type})]}}),ta="textStyle",na=/^\s*([-+*])\s$/,ra=Sl.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:e}){return["ul",as(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleBulletList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(ta)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let e=wl({find:na,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(e=wl({find:na,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(ta),editor:this.editor})),[e]}}),oa=/(^|[^`])`([^`]+)`(?!`)/,ia=/(^|[^`])`([^`]+)`(?!`)/g,sa=xs.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:e}){return["code",as(this.options.HTMLAttributes,e),0]},addCommands(){return{setCode:()=>({commands:e})=>e.setMark(this.name),toggleCode:()=>({commands:e})=>e.toggleMark(this.name),unsetCode:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[bl({find:oa,type:this.type})]},addPasteRules(){return[xl({find:ia,type:this.type})]}}),la=/^```([a-z]+)?[\s\n]$/,aa=/^~~~([a-z]+)?[\s\n]$/,ca=Sl.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:e=>{var t;const{languageClassPrefix:n}=this.options,r=[...(null===(t=e.firstElementChild)||void 0===t?void 0:t.classList)||[]].filter((e=>e.startsWith(n))).map((e=>e.replace(n,"")))[0];return r||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:e,HTMLAttributes:t}){return["pre",as(this.options.HTMLAttributes,t),["code",{class:e.attrs.language?this.options.languageClassPrefix+e.attrs.language:null},0]]},addCommands(){return{setCodeBlock:e=>({commands:t})=>t.setNode(this.name,e),toggleCodeBlock:e=>({commands:t})=>t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:e,$anchor:t}=this.editor.state.selection,n=1===t.pos;return!(!e||t.parent.type.name!==this.name)&&(!(!n&&t.parent.textContent.length)&&this.editor.commands.clearNodes())},Enter:({editor:e})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:t}=e,{selection:n}=t,{$from:r,empty:o}=n;if(!o||r.parent.type!==this.type)return!1;const i=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith("\n\n");return!(!i||!s)&&e.chain().command((({tr:e})=>(e.delete(r.pos-2,r.pos),!0))).exitCode().run()},ArrowDown:({editor:e})=>{if(!this.options.exitOnArrowDown)return!1;const{state:t}=e,{selection:n,doc:r}=t,{$from:o,empty:i}=n;if(!i||o.parent.type!==this.type)return!1;if(!(o.parentOffset===o.parent.nodeSize-2))return!1;const s=o.after();if(void 0===s)return!1;return r.nodeAt(s)?e.commands.command((({tr:e})=>(e.setSelection(Pt.near(r.resolve(s))),!0))):e.commands.exitCode()}}},addInputRules(){return[kl({find:la,type:this.type,getAttributes:e=>({language:e[1]})}),kl({find:aa,type:this.type,getAttributes:e=>({language:e[1]})})]},addProseMirrorPlugins(){return[new Yt({key:new en("codeBlockVSCodeHandler"),props:{handlePaste:(e,t)=>{if(!t.clipboardData)return!1;if(this.editor.isActive(this.type.name))return!1;const n=t.clipboardData.getData("text/plain"),r=t.clipboardData.getData("vscode-editor-data"),o=r?JSON.parse(r):void 0,i=null==o?void 0:o.mode;if(!n||!i)return!1;const{tr:s,schema:l}=e.state,a=l.text(n.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:i},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(It.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),e.dispatch(s),!0}}})]}}),ua=Sl.create({name:"doc",topNode:!0,content:"block+"});function da(e={}){return new Yt({view:t=>new fa(t,e)})}class fa{constructor(e,t){var n;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!==(n=t.width)&&void 0!==n?n:1,this.color=!1===t.color?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map((t=>{let n=e=>{this[t](e)};return e.dom.addEventListener(t,n),{name:t,handler:n}}))}destroy(){this.handlers.forEach((({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t)))}update(e,t){null!=this.cursorPos&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,null==e?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e,t=this.editorView.state.doc.resolve(this.cursorPos),n=!t.parent.inlineContent,r=this.editorView.dom,o=r.getBoundingClientRect(),i=o.width/r.offsetWidth,s=o.height/r.offsetHeight;if(n){let n=t.nodeBefore,r=t.nodeAfter;if(n||r){let t=this.editorView.nodeDOM(this.cursorPos-(n?n.nodeSize:0));if(t){let o=t.getBoundingClientRect(),i=n?o.bottom:o.top;n&&r&&(i=(i+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let l=this.width/2*s;e={left:o.left,right:o.right,top:i-l,bottom:i+l}}}}if(!e){let t=this.editorView.coordsAtPos(this.cursorPos),n=this.width/2*i;e={left:t.left-n,right:t.left+n,top:t.top,bottom:t.bottom}}let l,a,c=this.editorView.dom.offsetParent;if(this.element||(this.element=c.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",n),this.element.classList.toggle("prosemirror-dropcursor-inline",!n),!c||c==document.body&&"static"==getComputedStyle(c).position)l=-pageXOffset,a=-pageYOffset;else{let e=c.getBoundingClientRect(),t=e.width/c.offsetWidth,n=e.height/c.offsetHeight;l=e.left-c.scrollLeft*t,a=e.top-c.scrollTop*n}this.element.style.left=(e.left-l)/i+"px",this.element.style.top=(e.top-a)/s+"px",this.element.style.width=(e.right-e.left)/i+"px",this.element.style.height=(e.bottom-e.top)/s+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout((()=>this.setCursor(null)),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),r=n&&n.type.spec.disableDropCursor,o="function"==typeof r?r(this.editorView,t,e):r;if(t&&!o){let e=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let t=gt(this.editorView.state.doc,e,this.editorView.dragging.slice);null!=t&&(e=t)}this.setCursor(e),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}const ha=Ns.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[da(this.options)]}});class pa extends Pt{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return pa.valid(n)?new pa(n):Pt.near(n)}content(){return H.empty}eq(e){return e instanceof pa&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if("number"!=typeof t.pos)throw new RangeError("Invalid input for GapCursor.fromJSON");return new pa(e.resolve(t.pos))}getBookmark(){return new ma(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!function(e){for(let t=e.depth;t>=0;t--){let n=e.index(t),r=e.node(t);if(0!=n)for(let e=r.child(n-1);;e=e.lastChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}else if(r.type.spec.isolating)return!0}return!0}(e)||!function(e){for(let t=e.depth;t>=0;t--){let n=e.indexAfter(t),r=e.node(t);if(n!=r.childCount)for(let e=r.child(n);;e=e.firstChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}else if(r.type.spec.isolating)return!0}return!0}(e))return!1;let n=t.type.spec.allowGapCursor;if(null!=n)return n;let r=t.contentMatchAt(e.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(e,t,n=!1){e:for(;;){if(!n&&pa.valid(e))return e;let r=e.pos,o=null;for(let n=e.depth;;n--){let i=e.node(n);if(t>0?e.indexAfter(n)<i.childCount:e.index(n)>0){o=i.child(t>0?e.indexAfter(n):e.index(n)-1);break}if(0==n)return null;r+=t;let s=e.doc.resolve(r);if(pa.valid(s))return s}for(;;){let i=t>0?o.firstChild:o.lastChild;if(!i){if(o.isAtom&&!o.isText&&!$t.isSelectable(o)){e=e.doc.resolve(r+o.nodeSize*t),n=!1;continue e}break}o=i,r+=t;let s=e.doc.resolve(r);if(pa.valid(s))return s}return null}}}pa.prototype.visible=!1,pa.findFrom=pa.findGapCursorFrom,Pt.jsonID("gapcursor",pa);class ma{constructor(e){this.pos=e}map(e){return new ma(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return pa.valid(t)?new pa(t):Pt.near(t)}}const ga=Ni({ArrowLeft:ya("horiz",-1),ArrowRight:ya("horiz",1),ArrowUp:ya("vert",-1),ArrowDown:ya("vert",1)});function ya(e,t){const n="vert"==e?t>0?"down":"up":t>0?"right":"left";return function(e,r,o){let i=e.selection,s=t>0?i.$to:i.$from,l=i.empty;if(i instanceof It){if(!o.endOfTextblock(n)||0==s.depth)return!1;l=!1,s=e.doc.resolve(t>0?s.after():s.before())}let a=pa.findGapCursorFrom(s,t,l);return!!a&&(r&&r(e.tr.setSelection(new pa(a))),!0)}}function va(e,t,n){if(!e||!e.editable)return!1;let r=e.state.doc.resolve(t);if(!pa.valid(r))return!1;let o=e.posAtCoords({left:n.clientX,top:n.clientY});return!(o&&o.inside>-1&&$t.isSelectable(e.state.doc.nodeAt(o.inside)))&&(e.dispatch(e.state.tr.setSelection(new pa(r))),!0)}function ba(e,t){if("insertCompositionText"!=t.inputType||!(e.state.selection instanceof pa))return!1;let{$from:n}=e.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(e.state.schema.nodes.text);if(!r)return!1;let o=I.empty;for(let s=r.length-1;s>=0;s--)o=I.from(r[s].createAndFill(null,o));let i=e.state.tr.replace(n.pos,n.pos,new H(o,0,0));return i.setSelection(It.near(i.doc.resolve(n.pos+1))),e.dispatch(i),!1}function ka(e){if(!(e.selection instanceof pa))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",Ho.create(e.doc,[Fo.widget(e.selection.head,t,{key:"gapcursor"})])}const wa=Ns.create({name:"gapCursor",addProseMirrorPlugins:()=>[new Yt({props:{decorations:ka,createSelectionBetween:(e,t,n)=>t.pos==n.pos&&pa.valid(n)?new pa(n):null,handleClick:va,handleKeyDown:ga,handleDOMEvents:{beforeinput:ba}}})],extendNodeSchema(e){var t;return{allowGapCursor:null!==(t=ds(rs(e,"allowGapCursor",{name:e.name,options:e.options,storage:e.storage})))&&void 0!==t?t:null}}}),Sa=Sl.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:e}){return["br",as(this.options.HTMLAttributes,e)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:e,chain:t,state:n,editor:r})=>e.first([()=>e.exitCode(),()=>e.command((()=>{const{selection:e,storedMarks:o}=n;if(e.$from.parent.type.spec.isolating)return!1;const{keepMarks:i}=this.options,{splittableMarks:s}=r.extensionManager,l=o||e.$to.parentOffset&&e.$from.marks();return t().insertContent({type:this.name}).command((({tr:e,dispatch:t})=>{if(t&&l&&i){const t=l.filter((e=>s.includes(e.type.name)));e.ensureMarks(t)}return!0})).run()}))])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),xa=Sl.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map((e=>({tag:`h${e}`,attrs:{level:e}})))},renderHTML({node:e,HTMLAttributes:t}){return[`h${this.options.levels.includes(e.attrs.level)?e.attrs.level:this.options.levels[0]}`,as(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.setNode(this.name,e),toggleHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return this.options.levels.reduce(((e,t)=>({...e,[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})})),{})},addInputRules(){return this.options.levels.map((e=>kl({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${e}})\\s$`),type:this.type,getAttributes:{level:e}})))}});var Ca=200,Ea=function(){};Ea.prototype.append=function(e){return e.length?(e=Ea.from(e),!this.length&&e||e.length<Ca&&this.leafAppend(e)||this.length<Ca&&e.leafPrepend(this)||this.appendInner(e)):this},Ea.prototype.prepend=function(e){return e.length?Ea.from(e).append(this):this},Ea.prototype.appendInner=function(e){return new Ta(this,e)},Ea.prototype.slice=function(e,t){return void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t?Ea.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},Ea.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)},Ea.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},Ea.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var r=[];return this.forEach((function(t,n){return r.push(e(t,n))}),t,n),r},Ea.from=function(e){return e instanceof Ea?e:e&&e.length?new Ma(e):Ea.empty};var Ma=function(e){function t(t){e.call(this),this.values=t}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(e,n){return 0==e&&n==this.length?this:new t(this.values.slice(e,n))},t.prototype.getInner=function(e){return this.values[e]},t.prototype.forEachInner=function(e,t,n,r){for(var o=t;o<n;o++)if(!1===e(this.values[o],r+o))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){for(var o=t-1;o>=n;o--)if(!1===e(this.values[o],r+o))return!1},t.prototype.leafAppend=function(e){if(this.length+e.length<=Ca)return new t(this.values.concat(e.flatten()))},t.prototype.leafPrepend=function(e){if(this.length+e.length<=Ca)return new t(e.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(t.prototype,n),t}(Ea);Ea.empty=new Ma([]);var Ta=function(e){function t(t,n){e.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(e){return e<this.left.length?this.left.get(e):this.right.get(e-this.left.length)},t.prototype.forEachInner=function(e,t,n,r){var o=this.left.length;return!(t<o&&!1===this.left.forEachInner(e,t,Math.min(n,o),r))&&(!(n>o&&!1===this.right.forEachInner(e,Math.max(t-o,0),Math.min(this.length,n)-o,r+o))&&void 0)},t.prototype.forEachInvertedInner=function(e,t,n,r){var o=this.left.length;return!(t>o&&!1===this.right.forEachInvertedInner(e,t-o,Math.max(n,o)-o,r+o))&&(!(n<o&&!1===this.left.forEachInvertedInner(e,Math.min(t,o),n,r))&&void 0)},t.prototype.sliceInner=function(e,t){if(0==e&&t==this.length)return this;var n=this.left.length;return t<=n?this.left.slice(e,t):e>=n?this.right.slice(e-n,t-n):this.left.slice(e,n).append(this.right.slice(0,t-n))},t.prototype.leafAppend=function(e){var n=this.right.leafAppend(e);if(n)return new t(this.left,n)},t.prototype.leafPrepend=function(e){var n=this.left.leafPrepend(e);if(n)return new t(n,this.right)},t.prototype.appendInner=function(e){return this.left.depth>=Math.max(this.right.depth,e.depth)+1?new t(this.left,new t(this.right,e)):new t(this,e)},t}(Ea);class Oa{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(0==this.eventCount)return null;let n,r,o=this.items.length;for(;;o--){if(this.items.get(o-1).selection){--o;break}}t&&(n=this.remapping(o,this.items.length),r=n.maps.length);let i,s,l=e.tr,a=[],c=[];return this.items.forEach(((e,t)=>{if(!e.step)return n||(n=this.remapping(o,t+1),r=n.maps.length),r--,void c.push(e);if(n){c.push(new Na(e.map));let t,o=e.step.map(n.slice(r));o&&l.maybeStep(o).doc&&(t=l.mapping.maps[l.mapping.maps.length-1],a.push(new Na(t,void 0,void 0,a.length+c.length))),r--,t&&n.appendMap(t,r)}else l.maybeStep(e.step);return e.selection?(i=n?e.selection.map(n.slice(r)):e.selection,s=new Oa(this.items.slice(0,o).append(c.reverse().concat(a)),this.eventCount-1),!1):void 0}),this.items.length,0),{remaining:s,transform:l,selection:i}}addTransform(e,t,n,r){let o=[],i=this.eventCount,s=this.items,l=!r&&s.length?s.get(s.length-1):null;for(let c=0;c<e.steps.length;c++){let n,a=e.steps[c].invert(e.docs[c]),u=new Na(e.mapping.maps[c],a,t);(n=l&&l.merge(u))&&(u=n,c?o.pop():s=s.slice(0,s.length-1)),o.push(u),t&&(i++,t=void 0),r||(l=u)}let a=i-n.depth;return a>Da&&(s=function(e,t){let n;return e.forEach(((e,r)=>{if(e.selection&&0==t--)return n=r,!1})),e.slice(n)}(s,a),i-=a),new Oa(s.append(o),i)}remapping(e,t){let n=new Ke;return this.items.forEach(((t,r)=>{let o=null!=t.mirrorOffset&&r-t.mirrorOffset>=e?n.maps.length-t.mirrorOffset:void 0;n.appendMap(t.map,o)}),e,t),n}addMaps(e){return 0==this.eventCount?this:new Oa(this.items.append(e.map((e=>new Na(e)))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-t),o=e.mapping,i=e.steps.length,s=this.eventCount;this.items.forEach((e=>{e.selection&&s--}),r);let l=t;this.items.forEach((t=>{let r=o.getMirror(--l);if(null==r)return;i=Math.min(i,r);let a=o.maps[r];if(t.step){let i=e.steps[r].invert(e.docs[r]),c=t.selection&&t.selection.map(o.slice(l+1,r));c&&s++,n.push(new Na(a,i,c))}else n.push(new Na(a))}),r);let a=[];for(let d=t;d<i;d++)a.push(new Na(o.maps[d]));let c=this.items.slice(0,r).append(a).append(n),u=new Oa(c,s);return u.emptyItemCount()>500&&(u=u.compress(this.items.length-n.length)),u}emptyItemCount(){let e=0;return this.items.forEach((t=>{t.step||e++})),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,r=[],o=0;return this.items.forEach(((i,s)=>{if(s>=e)r.push(i),i.selection&&o++;else if(i.step){let e=i.step.map(t.slice(n)),s=e&&e.getMap();if(n--,s&&t.appendMap(s,n),e){let l=i.selection&&i.selection.map(t.slice(n));l&&o++;let a,c=new Na(s.invert(),e,l),u=r.length-1;(a=r.length&&r[u].merge(c))?r[u]=a:r.push(c)}}else i.map&&n--}),this.items.length,0),new Oa(Ea.from(r.reverse()),o)}}Oa.empty=new Oa(Ea.empty,0);class Na{constructor(e,t,n,r){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=r}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new Na(t.getMap().invert(),t,this.selection)}}}class Aa{constructor(e,t,n,r,o){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=r,this.prevComposition=o}}const Da=20;function Pa(e){let t=[];for(let n=e.length-1;n>=0&&0==t.length;n--)e[n].forEach(((e,n,r,o)=>t.push(r,o)));return t}function Ra(e,t){if(!e)return null;let n=[];for(let r=0;r<e.length;r+=2){let o=t.map(e[r],1),i=t.map(e[r+1],-1);o<=i&&n.push(o,i)}return n}let La=!1,za=null;function Ia(e){let t=e.plugins;if(za!=t){La=!1,za=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){La=!0;break}}return La}const _a=new en("history"),$a=new en("closeHistory");function Fa(e={}){return e={depth:e.depth||100,newGroupDelay:e.newGroupDelay||500},new Yt({key:_a,state:{init:()=>new Aa(Oa.empty,Oa.empty,null,0,-1),apply:(t,n,r)=>function(e,t,n,r){let o,i=n.getMeta(_a);if(i)return i.historyState;n.getMeta($a)&&(e=new Aa(e.done,e.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return e;if(s&&s.getMeta(_a))return s.getMeta(_a).redo?new Aa(e.done.addTransform(n,void 0,r,Ia(t)),e.undone,Pa(n.mapping.maps),e.prevTime,e.prevComposition):new Aa(e.done,e.undone.addTransform(n,void 0,r,Ia(t)),null,e.prevTime,e.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))return(o=n.getMeta("rebased"))?new Aa(e.done.rebased(n,o),e.undone.rebased(n,o),Ra(e.prevRanges,n.mapping),e.prevTime,e.prevComposition):new Aa(e.done.addMaps(n.mapping.maps),e.undone.addMaps(n.mapping.maps),Ra(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);{let o=n.getMeta("composition"),i=0==e.prevTime||!s&&e.prevComposition!=o&&(e.prevTime<(n.time||0)-r.newGroupDelay||!function(e,t){if(!t)return!1;if(!e.docChanged)return!0;let n=!1;return e.mapping.maps[0].forEach(((e,r)=>{for(let o=0;o<t.length;o+=2)e<=t[o+1]&&r>=t[o]&&(n=!0)})),n}(n,e.prevRanges)),l=s?Ra(e.prevRanges,n.mapping):Pa(n.mapping.maps);return new Aa(e.done.addTransform(n,i?t.selection.getBookmark():void 0,r,Ia(t)),Oa.empty,l,n.time,null==o?e.prevComposition:o)}}(n,r,t,e)},config:e,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,r="historyUndo"==n?ja:"historyRedo"==n?Ha:null;return!!r&&(t.preventDefault(),r(e.state,e.dispatch))}}}})}function Ba(e,t){return(n,r)=>{let o=_a.getState(n);if(!o||0==(e?o.undone:o.done).eventCount)return!1;if(r){let i=function(e,t,n){let r=Ia(t),o=_a.get(t).spec.config,i=(n?e.undone:e.done).popEvent(t,r);if(!i)return null;let s=i.selection.resolve(i.transform.doc),l=(n?e.done:e.undone).addTransform(i.transform,t.selection.getBookmark(),o,r),a=new Aa(n?l:i.remaining,n?i.remaining:l,null,0,-1);return i.transform.setSelection(s).setMeta(_a,{redo:n,historyState:a})}(o,n,e);i&&r(t?i.scrollIntoView():i)}return!0}}const ja=Ba(!1,!0),Ha=Ba(!0,!0),Va=Ns.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:e,dispatch:t})=>ja(e,t),redo:()=>({state:e,dispatch:t})=>Ha(e,t)}),addProseMirrorPlugins(){return[Fa(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),Ua=Sl.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:e}){return["hr",as(this.options.HTMLAttributes,e)]},addCommands(){return{setHorizontalRule:()=>({chain:e,state:t})=>{const{selection:n}=t,{$from:r,$to:o}=n,i=e();return 0===r.parentOffset?i.insertContentAt({from:Math.max(r.pos-1,0),to:o.pos},{type:this.name}):n instanceof $t?i.insertContentAt(o.pos,{type:this.name}):i.insertContent({type:this.name}),i.command((({tr:e,dispatch:t})=>{var n;if(t){const{$to:t}=e.selection,r=t.end();if(t.nodeAfter)t.nodeAfter.isTextblock?e.setSelection(It.create(e.doc,t.pos+1)):t.nodeAfter.isBlock?e.setSelection($t.create(e.doc,t.pos)):e.setSelection(It.create(e.doc,t.pos));else{const o=null===(n=t.parent.type.contentMatch.defaultType)||void 0===n?void 0:n.create();o&&(e.insert(r,o),e.setSelection(It.create(e.doc,r+1)))}e.scrollIntoView()}return!0})).run()}}},addInputRules(){return[(e={find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type},new vs({find:e.find,handler:({state:t,range:n,match:r})=>{const o=ds(e.getAttributes,void 0,r)||{},{tr:i}=t,s=n.from;let l=n.to;const a=e.type.create(o);if(r[1]){let e=s+r[0].lastIndexOf(r[1]);e>l?e=l:l=e+r[1].length;const t=r[0][r[0].length-1];i.insertText(t,s+r[0].length-1),i.replaceWith(e,l,a)}else if(r[0]){const t=e.type.isInline?s:s-1;i.insert(t,e.type.create(o)).delete(i.mapping.map(s),i.mapping.map(l))}i.scrollIntoView()}}))];var e}}),Wa=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,qa=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,Ka=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,Ja=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,Qa=xs.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:e=>"normal"!==e.style.fontStyle&&null},{style:"font-style=normal",clearMark:e=>e.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:e}){return["em",as(this.options.HTMLAttributes,e),0]},addCommands(){return{setItalic:()=>({commands:e})=>e.setMark(this.name),toggleItalic:()=>({commands:e})=>e.toggleMark(this.name),unsetItalic:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[bl({find:Wa,type:this.type}),bl({find:Ka,type:this.type})]},addPasteRules(){return[xl({find:qa,type:this.type}),xl({find:Ja,type:this.type})]}}),Ga=Sl.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:e}){return["li",as(this.options.HTMLAttributes,e),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),Ya="textStyle",Xa=/^(\d+)\.\s$/,Za=Sl.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:e=>e.hasAttribute("start")?parseInt(e.getAttribute("start")||"",10):1},type:{default:null,parseHTML:e=>e.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:e}){const{start:t,...n}=e;return 1===t?["ol",as(this.options.HTMLAttributes,n),0]:["ol",as(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleOrderedList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(Ya)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let e=wl({find:Xa,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(e=wl({find:Xa,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(Ya)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[e]}}),ec=Sl.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:e}){return["p",as(this.options.HTMLAttributes,e),0]},addCommands(){return{setParagraph:()=>({commands:e})=>e.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),tc=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,nc=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,rc=xs.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("line-through")&&{}}],renderHTML({HTMLAttributes:e}){return["s",as(this.options.HTMLAttributes,e),0]},addCommands(){return{setStrike:()=>({commands:e})=>e.setMark(this.name),toggleStrike:()=>({commands:e})=>e.toggleMark(this.name),unsetStrike:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[bl({find:tc,type:this.type})]},addPasteRules(){return[xl({find:nc,type:this.type})]}}),oc=Sl.create({name:"text",group:"inline"}),ic=Ns.create({name:"starterKit",addExtensions(){const e=[];return!1!==this.options.bold&&e.push(ea.configure(this.options.bold)),!1!==this.options.blockquote&&e.push(Ql.configure(this.options.blockquote)),!1!==this.options.bulletList&&e.push(ra.configure(this.options.bulletList)),!1!==this.options.code&&e.push(sa.configure(this.options.code)),!1!==this.options.codeBlock&&e.push(ca.configure(this.options.codeBlock)),!1!==this.options.document&&e.push(ua.configure(this.options.document)),!1!==this.options.dropcursor&&e.push(ha.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&e.push(wa.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&e.push(Sa.configure(this.options.hardBreak)),!1!==this.options.heading&&e.push(xa.configure(this.options.heading)),!1!==this.options.history&&e.push(Va.configure(this.options.history)),!1!==this.options.horizontalRule&&e.push(Ua.configure(this.options.horizontalRule)),!1!==this.options.italic&&e.push(Qa.configure(this.options.italic)),!1!==this.options.listItem&&e.push(Ga.configure(this.options.listItem)),!1!==this.options.orderedList&&e.push(Za.configure(this.options.orderedList)),!1!==this.options.paragraph&&e.push(ec.configure(this.options.paragraph)),!1!==this.options.strike&&e.push(rc.configure(this.options.strike)),!1!==this.options.text&&e.push(oc.configure(this.options.text)),e}}),sc=xs.create({name:"underline",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("underline")&&{}}],renderHTML({HTMLAttributes:e}){return["u",as(this.options.HTMLAttributes,e),0]},addCommands(){return{setUnderline:()=>({commands:e})=>e.setMark(this.name),toggleUnderline:()=>({commands:e})=>e.toggleMark(this.name),unsetUnderline:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}}),lc=Ns.create({name:"textAlign",addOptions:()=>({types:[],alignments:["left","center","right","justify"],defaultAlignment:null}),addGlobalAttributes(){return[{types:this.options.types,attributes:{textAlign:{default:this.options.defaultAlignment,parseHTML:e=>{const t=e.style.textAlign;return this.options.alignments.includes(t)?t:this.options.defaultAlignment},renderHTML:e=>e.textAlign?{style:`text-align: ${e.textAlign}`}:{}}}}]},addCommands(){return{setTextAlign:e=>({commands:t})=>!!this.options.alignments.includes(e)&&this.options.types.map((n=>t.updateAttributes(n,{textAlign:e}))).every((e=>e)),unsetTextAlign:()=>({commands:e})=>this.options.types.map((t=>e.resetAttributes(t,"textAlign"))).every((e=>e)),toggleTextAlign:e=>({editor:t,commands:n})=>!!this.options.alignments.includes(e)&&(t.isActive({textAlign:e})?n.unsetTextAlign():n.setTextAlign(e))}},addKeyboardShortcuts(){return{"Mod-Shift-l":()=>this.editor.commands.setTextAlign("left"),"Mod-Shift-e":()=>this.editor.commands.setTextAlign("center"),"Mod-Shift-r":()=>this.editor.commands.setTextAlign("right"),"Mod-Shift-j":()=>this.editor.commands.setTextAlign("justify")}}}),ac=(e,t)=>{for(const n in t)e[n]=t[n];return e},cc="numeric",uc="ascii",dc="alpha",fc="asciinumeric",hc="alphanumeric",pc="domain",mc="emoji",gc="scheme",yc="slashscheme",vc="whitespace";function bc(e,t){return e in t||(t[e]=[]),t[e]}function kc(e,t,n){t[cc]&&(t[fc]=!0,t[hc]=!0),t[uc]&&(t[fc]=!0,t[dc]=!0),t[fc]&&(t[hc]=!0),t[dc]&&(t[hc]=!0),t[hc]&&(t[pc]=!0),t[mc]&&(t[pc]=!0);for(const r in t){const t=bc(r,n);t.indexOf(e)<0&&t.push(e)}}function wc(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}wc.groups={},wc.prototype={accepts(){return!!this.t},go(e){const t=this,n=t.j[e];if(n)return n;for(let r=0;r<t.jr.length;r++){const n=t.jr[r][0],o=t.jr[r][1];if(o&&n.test(e))return o}return t.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,r){for(let o=0;o<e.length;o++)this.tt(e[o],t,n,r)},tr(e,t,n,r){let o;return r=r||wc.groups,t&&t.j?o=t:(o=new wc(t),n&&r&&kc(t,n,r)),this.jr.push([e,o]),o},ts(e,t,n,r){let o=this;const i=e.length;if(!i)return o;for(let s=0;s<i-1;s++)o=o.tt(e[s]);return o.tt(e[i-1],t,n,r)},tt(e,t,n,r){r=r||wc.groups;const o=this;if(t&&t.j)return o.j[e]=t,t;const i=t;let s,l=o.go(e);if(l?(s=new wc,ac(s.j,l.j),s.jr.push.apply(s.jr,l.jr),s.jd=l.jd,s.t=l.t):s=new wc,i){if(r)if(s.t&&"string"==typeof s.t){const e=ac(function(e,t){const n={};for(const r in t)t[r].indexOf(e)>=0&&(n[r]=!0);return n}(s.t,r),n);kc(i,e,r)}else n&&kc(i,n,r);s.t=i}return o.j[e]=s,s}};const Sc=(e,t,n,r,o)=>e.ta(t,n,r,o),xc=(e,t,n,r,o)=>e.tr(t,n,r,o),Cc=(e,t,n,r,o)=>e.ts(t,n,r,o),Ec=(e,t,n,r,o)=>e.tt(t,n,r,o),Mc="WORD",Tc="UWORD",Oc="ASCIINUMERICAL",Nc="ALPHANUMERICAL",Ac="LOCALHOST",Dc="TLD",Pc="UTLD",Rc="SCHEME",Lc="SLASH_SCHEME",zc="NUM",Ic="WS",_c="NL",$c="OPENBRACE",Fc="CLOSEBRACE",Bc="OPENBRACKET",jc="CLOSEBRACKET",Hc="OPENPAREN",Vc="CLOSEPAREN",Uc="OPENANGLEBRACKET",Wc="CLOSEANGLEBRACKET",qc="FULLWIDTHLEFTPAREN",Kc="FULLWIDTHRIGHTPAREN",Jc="LEFTCORNERBRACKET",Qc="RIGHTCORNERBRACKET",Gc="LEFTWHITECORNERBRACKET",Yc="RIGHTWHITECORNERBRACKET",Xc="FULLWIDTHLESSTHAN",Zc="FULLWIDTHGREATERTHAN",eu="AMPERSAND",tu="APOSTROPHE",nu="ASTERISK",ru="AT",ou="BACKSLASH",iu="BACKTICK",su="CARET",lu="COLON",au="COMMA",cu="DOLLAR",uu="DOT",du="EQUALS",fu="EXCLAMATION",hu="HYPHEN",pu="PERCENT",mu="PIPE",gu="PLUS",yu="POUND",vu="QUERY",bu="QUOTE",ku="FULLWIDTHMIDDLEDOT",wu="SEMI",Su="SLASH",xu="TILDE",Cu="UNDERSCORE",Eu="EMOJI",Mu="SYM";var Tu=Object.freeze({__proto__:null,ALPHANUMERICAL:Nc,AMPERSAND:eu,APOSTROPHE:tu,ASCIINUMERICAL:Oc,ASTERISK:nu,AT:ru,BACKSLASH:ou,BACKTICK:iu,CARET:su,CLOSEANGLEBRACKET:Wc,CLOSEBRACE:Fc,CLOSEBRACKET:jc,CLOSEPAREN:Vc,COLON:lu,COMMA:au,DOLLAR:cu,DOT:uu,EMOJI:Eu,EQUALS:du,EXCLAMATION:fu,FULLWIDTHGREATERTHAN:Zc,FULLWIDTHLEFTPAREN:qc,FULLWIDTHLESSTHAN:Xc,FULLWIDTHMIDDLEDOT:ku,FULLWIDTHRIGHTPAREN:Kc,HYPHEN:hu,LEFTCORNERBRACKET:Jc,LEFTWHITECORNERBRACKET:Gc,LOCALHOST:Ac,NL:_c,NUM:zc,OPENANGLEBRACKET:Uc,OPENBRACE:$c,OPENBRACKET:Bc,OPENPAREN:Hc,PERCENT:pu,PIPE:mu,PLUS:gu,POUND:yu,QUERY:vu,QUOTE:bu,RIGHTCORNERBRACKET:Qc,RIGHTWHITECORNERBRACKET:Yc,SCHEME:Rc,SEMI:wu,SLASH:Su,SLASH_SCHEME:Lc,SYM:Mu,TILDE:xu,TLD:Dc,UNDERSCORE:Cu,UTLD:Pc,UWORD:Tc,WORD:Mc,WS:Ic});const Ou=/[a-z]/,Nu=new RegExp("\\p{L}","u"),Au=new RegExp("\\p{Emoji}","u"),Du=/\d/,Pu=/\s/;let Ru=null,Lu=null;function zu(e,t){const n=function(e){const t=[],n=e.length;let r=0;for(;r<n;){let o,i=e.charCodeAt(r),s=i<55296||i>56319||r+1===n||(o=e.charCodeAt(r+1))<56320||o>57343?e[r]:e.slice(r,r+2);t.push(s),r+=s.length}return t}(t.replace(/[A-Z]/g,(e=>e.toLowerCase()))),r=n.length,o=[];let i=0,s=0;for(;s<r;){let l=e,a=null,c=0,u=null,d=-1,f=-1;for(;s<r&&(a=l.go(n[s]));)l=a,l.accepts()?(d=0,f=0,u=l):d>=0&&(d+=n[s].length,f++),c+=n[s].length,i+=n[s].length,s++;i-=d,s-=f,c-=d,o.push({t:u.t,v:t.slice(i-c,i),s:i-c,e:i})}return o}function Iu(e,t,n,r,o){let i;const s=t.length;for(let l=0;l<s-1;l++){const n=t[l];e.j[n]?i=e.j[n]:(i=new wc(r),i.jr=o.slice(),e.j[n]=i),e=i}return i=new wc(n),i.jr=o.slice(),e.j[t[s-1]]=i,i}function _u(e){const t=[],n=[];let r=0;for(;r<e.length;){let o=0;for(;"0123456789".indexOf(e[r+o])>=0;)o++;if(o>0){t.push(n.join(""));for(let t=parseInt(e.substring(r,r+o),10);t>0;t--)n.pop();r+=o}else n.push(e[r]),r++}return t}const $u={defaultProtocol:"http",events:null,format:Bu,formatHref:Bu,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Fu(e,t=null){let n=ac({},$u);e&&(n=ac(n,e instanceof Fu?e.o:e));const r=n.ignoreTags,o=[];for(let i=0;i<r.length;i++)o.push(r[i].toUpperCase());this.o=n,t&&(this.defaultRender=t),this.ignoreTags=o}function Bu(e){return e}function ju(e,t){this.t="token",this.v=e,this.tk=t}function Hu(e,t){class n extends ju{constructor(t,n){super(t,n),this.t=e}}for(const r in t)n.prototype[r]=t[r];return n.t=e,n}Fu.prototype={o:$u,ignoreTags:[],defaultRender:e=>e,check(e){return this.get("validate",e.toString(),e)},get(e,t,n){const r=null!=t;let o=this.o[e];return o?("object"==typeof o?(o=n.t in o?o[n.t]:$u[e],"function"==typeof o&&r&&(o=o(t,n))):"function"==typeof o&&r&&(o=o(t,n.t,n)),o):o},getObj(e,t,n){let r=this.o[e];return"function"==typeof r&&null!=t&&(r=r(t,n.t,n)),r},render(e){const t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}},ju.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){const t=this.toString(),n=e.get("truncate",t,this),r=e.get("format",t,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=$u.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){const t=this,n=this.toHref(e.get("defaultProtocol")),r=e.get("formatHref",n,this),o=e.get("tagName",n,t),i=this.toFormattedString(e),s={},l=e.get("className",n,t),a=e.get("target",n,t),c=e.get("rel",n,t),u=e.getObj("attributes",n,t),d=e.getObj("events",n,t);return s.href=r,l&&(s.class=l),a&&(s.target=a),c&&(s.rel=c),u&&ac(s,u),{tagName:o,attributes:s,content:i,eventListeners:d}}};const Vu=Hu("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),Uu=Hu("text"),Wu=Hu("nl"),qu=Hu("url",{isLink:!0,toHref(e=$u.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){const e=this.tk;return e.length>=2&&e[0].t!==Ac&&e[1].t===lu}}),Ku=e=>new wc(e);function Ju(e,t,n){const r=n[0].s,o=n[n.length-1].e;return new e(t.slice(r,o),n)}const Qu="undefined"!=typeof console&&console&&console.warn||(()=>{}),Gu={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function Yu(e,t=!1){if(Gu.initialized&&Qu(`linkifyjs: already initialized - will not register custom scheme "${e}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw new Error('linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or "-"\n2. Cannot start or end with "-"\n3. "-" cannot repeat');Gu.customSchemes.push([e,t])}function Xu(){Gu.scanner=function(e=[]){const t={};wc.groups=t;const n=new wc;null==Ru&&(Ru=_u("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==Lu&&(Lu=_u("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),Ec(n,"'",tu),Ec(n,"{",$c),Ec(n,"}",Fc),Ec(n,"[",Bc),Ec(n,"]",jc),Ec(n,"(",Hc),Ec(n,")",Vc),Ec(n,"<",Uc),Ec(n,">",Wc),Ec(n,"（",qc),Ec(n,"）",Kc),Ec(n,"「",Jc),Ec(n,"」",Qc),Ec(n,"『",Gc),Ec(n,"』",Yc),Ec(n,"＜",Xc),Ec(n,"＞",Zc),Ec(n,"&",eu),Ec(n,"*",nu),Ec(n,"@",ru),Ec(n,"`",iu),Ec(n,"^",su),Ec(n,":",lu),Ec(n,",",au),Ec(n,"$",cu),Ec(n,".",uu),Ec(n,"=",du),Ec(n,"!",fu),Ec(n,"-",hu),Ec(n,"%",pu),Ec(n,"|",mu),Ec(n,"+",gu),Ec(n,"#",yu),Ec(n,"?",vu),Ec(n,'"',bu),Ec(n,"/",Su),Ec(n,";",wu),Ec(n,"~",xu),Ec(n,"_",Cu),Ec(n,"\\",ou),Ec(n,"・",ku);const r=xc(n,Du,zc,{[cc]:!0});xc(r,Du,r);const o=xc(r,Ou,Oc,{[fc]:!0}),i=xc(r,Nu,Nc,{[hc]:!0}),s=xc(n,Ou,Mc,{[uc]:!0});xc(s,Du,o),xc(s,Ou,s),xc(o,Du,o),xc(o,Ou,o);const l=xc(n,Nu,Tc,{[dc]:!0});xc(l,Ou),xc(l,Du,i),xc(l,Nu,l),xc(i,Du,i),xc(i,Ou),xc(i,Nu,i);const a=Ec(n,"\n",_c,{[vc]:!0}),c=Ec(n,"\r",Ic,{[vc]:!0}),u=xc(n,Pu,Ic,{[vc]:!0});Ec(n,"￼",u),Ec(c,"\n",a),Ec(c,"￼",u),xc(c,Pu,u),Ec(u,"\r"),Ec(u,"\n"),xc(u,Pu,u),Ec(u,"￼",u);const d=xc(n,Au,Eu,{[mc]:!0});Ec(d,"#"),xc(d,Au,d),Ec(d,"️",d);const f=Ec(d,"‍");Ec(f,"#"),xc(f,Au,d);const h=[[Ou,s],[Du,o]],p=[[Ou,null],[Nu,l],[Du,i]];for(let m=0;m<Ru.length;m++)Iu(n,Ru[m],Dc,Mc,h);for(let m=0;m<Lu.length;m++)Iu(n,Lu[m],Pc,Tc,p);kc(Dc,{tld:!0,ascii:!0},t),kc(Pc,{utld:!0,alpha:!0},t),Iu(n,"file",Rc,Mc,h),Iu(n,"mailto",Rc,Mc,h),Iu(n,"http",Lc,Mc,h),Iu(n,"https",Lc,Mc,h),Iu(n,"ftp",Lc,Mc,h),Iu(n,"ftps",Lc,Mc,h),kc(Rc,{scheme:!0,ascii:!0},t),kc(Lc,{slashscheme:!0,ascii:!0},t),e=e.sort(((e,t)=>e[0]>t[0]?1:-1));for(let m=0;m<e.length;m++){const t=e[m][0],r=e[m][1]?{[gc]:!0}:{[yc]:!0};t.indexOf("-")>=0?r[pc]=!0:Ou.test(t)?Du.test(t)?r[fc]=!0:r[uc]=!0:r[cc]=!0,Cc(n,t,t,r)}return Cc(n,"localhost",Ac,{ascii:!0}),n.jd=new wc(Mu),{start:n,tokens:ac({groups:t},Tu)}}(Gu.customSchemes);for(let e=0;e<Gu.tokenQueue.length;e++)Gu.tokenQueue[e][1]({scanner:Gu.scanner});Gu.parser=function({groups:e}){const t=e.domain.concat([eu,nu,ru,ou,iu,su,cu,du,hu,zc,pu,mu,gu,yu,Su,Mu,xu,Cu]),n=[tu,lu,au,uu,fu,pu,vu,bu,wu,Uc,Wc,$c,Fc,jc,Bc,Hc,Vc,qc,Kc,Jc,Qc,Gc,Yc,Xc,Zc],r=[eu,tu,nu,ou,iu,su,cu,du,hu,$c,Fc,pu,mu,gu,yu,vu,Su,Mu,xu,Cu],o=Ku(),i=Ec(o,xu);Sc(i,r,i),Sc(i,e.domain,i);const s=Ku(),l=Ku(),a=Ku();Sc(o,e.domain,s),Sc(o,e.scheme,l),Sc(o,e.slashscheme,a),Sc(s,r,i),Sc(s,e.domain,s);const c=Ec(s,ru);Ec(i,ru,c),Ec(l,ru,c),Ec(a,ru,c);const u=Ec(i,uu);Sc(u,r,i),Sc(u,e.domain,i);const d=Ku();Sc(c,e.domain,d),Sc(d,e.domain,d);const f=Ec(d,uu);Sc(f,e.domain,d);const h=Ku(Vu);Sc(f,e.tld,h),Sc(f,e.utld,h),Ec(c,Ac,h);const p=Ec(d,hu);Ec(p,hu,p),Sc(p,e.domain,d),Sc(h,e.domain,d),Ec(h,uu,f),Ec(h,hu,p);const m=Ec(h,lu);Sc(m,e.numeric,Vu);const g=Ec(s,hu),y=Ec(s,uu);Ec(g,hu,g),Sc(g,e.domain,s),Sc(y,r,i),Sc(y,e.domain,s);const v=Ku(qu);Sc(y,e.tld,v),Sc(y,e.utld,v),Sc(v,e.domain,s),Sc(v,r,i),Ec(v,uu,y),Ec(v,hu,g),Ec(v,ru,c);const b=Ec(v,lu),k=Ku(qu);Sc(b,e.numeric,k);const w=Ku(qu),S=Ku();Sc(w,t,w),Sc(w,n,S),Sc(S,t,w),Sc(S,n,S),Ec(v,Su,w),Ec(k,Su,w);const x=Ec(l,lu),C=Ec(a,lu),E=Ec(C,Su),M=Ec(E,Su);Sc(l,e.domain,s),Ec(l,uu,y),Ec(l,hu,g),Sc(a,e.domain,s),Ec(a,uu,y),Ec(a,hu,g),Sc(x,e.domain,w),Ec(x,Su,w),Ec(x,vu,w),Sc(M,e.domain,w),Sc(M,t,w),Ec(M,Su,w);const T=[[$c,Fc],[Bc,jc],[Hc,Vc],[Uc,Wc],[qc,Kc],[Jc,Qc],[Gc,Yc],[Xc,Zc]];for(let O=0;O<T.length;O++){const[e,r]=T[O],o=Ec(w,e);Ec(S,e,o),Ec(o,r,w);const i=Ku(qu);Sc(o,t,i);const s=Ku();Sc(o,n),Sc(i,t,i),Sc(i,n,s),Sc(s,t,i),Sc(s,n,s),Ec(i,r,w),Ec(s,r,w)}return Ec(o,Ac,v),Ec(o,_c,Wu),{start:o,tokens:Tu}}(Gu.scanner.tokens);for(let e=0;e<Gu.pluginQueue.length;e++)Gu.pluginQueue[e][1]({scanner:Gu.scanner,parser:Gu.parser});return Gu.initialized=!0,Gu}function Zu(e){return Gu.initialized||Xu(),function(e,t,n){let r=n.length,o=0,i=[],s=[];for(;o<r;){let l=e,a=null,c=null,u=0,d=null,f=-1;for(;o<r&&!(a=l.go(n[o].t));)s.push(n[o++]);for(;o<r&&(c=a||l.go(n[o].t));)a=null,l=c,l.accepts()?(f=0,d=l):f>=0&&f++,o++,u++;if(f<0)o-=u,o<r&&(s.push(n[o]),o++);else{s.length>0&&(i.push(Ju(Uu,t,s)),s=[]),o-=f,u-=f;const e=d.t,r=n.slice(o-u,o);i.push(Ju(e,t,r))}}return s.length>0&&i.push(Ju(Uu,t,s)),i}(Gu.parser.start,e,zu(Gu.scanner.start,e))}function ed(e,t=null,n=null){if(t&&"object"==typeof t){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}const r=new Fu(n),o=Zu(e),i=[];for(let s=0;s<o.length;s++){const e=o[s];!e.isLink||t&&e.t!==t||!r.check(e)||i.push(e.toFormattedObject(r))}return i}function td(e){return new Yt({key:new en("autolink"),appendTransaction:(t,n,r)=>{const o=t.some((e=>e.docChanged))&&!n.doc.eq(r.doc),i=t.some((e=>e.getMeta("preventAutolink")));if(!o||i)return;const{tr:s}=r,l=function(e,t){const n=new At(e);return t.forEach((e=>{e.steps.forEach((e=>{n.step(e)}))})),n}(n.doc,[...t]),a=function(e){const{mapping:t,steps:n}=e,r=[];return t.maps.forEach(((e,o)=>{const i=[];if(e.ranges.length)e.forEach(((e,t)=>{i.push({from:e,to:t})}));else{const{from:e,to:t}=n[o];if(void 0===e||void 0===t)return;i.push({from:e,to:t})}i.forEach((({from:e,to:n})=>{const i=t.slice(o).map(e,-1),s=t.slice(o).map(n),l=t.invert().map(i,-1),a=t.invert().map(s);r.push({oldRange:{from:l,to:a},newRange:{from:i,to:s}})}))})),Zs(r)}(l);return a.forEach((({newRange:t})=>{const n=function(e,t,n){const r=[];return e.nodesBetween(t.from,t.to,((e,t)=>{n(e)&&r.push({node:e,pos:t})})),r}(r.doc,t,(e=>e.isTextblock));let o,i;if(n.length>1?(o=n[0],i=r.doc.textBetween(o.pos,o.pos+o.node.nodeSize,void 0," ")):n.length&&r.doc.textBetween(t.from,t.to," "," ").endsWith(" ")&&(o=n[0],i=r.doc.textBetween(o.pos,t.to,void 0," ")),o&&i){const t=i.split(" ").filter((e=>""!==e));if(t.length<=0)return!1;const n=t[t.length-1],a=o.pos+i.lastIndexOf(n);if(!n)return!1;const c=Zu(n).map((t=>t.toObject(e.defaultProtocol)));if(!(1===(l=c).length?l[0].isLink:3===l.length&&l[1].isLink&&["()","[]"].includes(l[0].value+l[2].value)))return!1;c.filter((e=>e.isLink)).map((e=>({...e,from:a+e.start+1,to:a+e.end+1}))).filter((e=>!r.schema.marks.code||!r.doc.rangeHasMark(e.from,e.to,r.schema.marks.code))).filter((t=>e.validate(t.value))).filter((t=>e.shouldAutoLink(t.value))).forEach((t=>{el(t.from,t.to,r.doc).some((t=>t.mark.type===e.type))||s.addMark(t.from,t.to,e.type.create({href:t.href}))}))}var l})),s.steps.length?s:void 0}})}Zu.scan=zu;const nd=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function rd(e,t){const n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach((e=>{const t="string"==typeof e?e:e.scheme;t&&n.push(t)})),!e||e.replace(nd,"").match(new RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}const od=xs.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach((e=>{"string"!=typeof e?Yu(e.scheme,e.optionalSlashes):Yu(e)}))},onDestroy(){wc.groups={},Gu.scanner=null,Gu.parser=null,Gu.tokenQueue=[],Gu.pluginQueue=[],Gu.customSchemes=[],Gu.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!rd(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}),addAttributes(){return{href:{default:null,parseHTML:e=>e.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{const t=e.getAttribute("href");return!(!t||!this.options.isAllowedUri(t,{defaultValidate:e=>!!rd(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:e=>!!rd(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",as(this.options.HTMLAttributes,e),0]:["a",as(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{const{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!rd(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().setMark(this.name,e).setMeta("preventAutolink",!0).run()},toggleLink:e=>({chain:t})=>{const{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!rd(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[xl({find:e=>{const t=[];if(e){const{protocols:n,defaultProtocol:r}=this.options,o=ed(e).filter((e=>e.isLink&&this.options.isAllowedUri(e.value,{defaultValidate:e=>!!rd(e,n),protocols:n,defaultProtocol:r})));o.length&&o.forEach((e=>t.push({text:e.value,data:{href:e.href},index:e.start})))}return t},type:this.type,getAttributes:e=>{var t;return{href:null===(t=e.data)||void 0===t?void 0:t.href}}})]},addProseMirrorPlugins(){const e=[],{protocols:t,defaultProtocol:n}=this.options;var r;return this.options.autolink&&e.push(td({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:e=>this.options.isAllowedUri(e,{defaultValidate:e=>!!rd(e,t),protocols:t,defaultProtocol:n}),shouldAutoLink:this.options.shouldAutoLink})),!0===this.options.openOnClick&&e.push((r={type:this.type},new Yt({key:new en("handleClickLink"),props:{handleClick:(e,t,n)=>{var o,i;if(0!==n.button)return!1;if(!e.editable)return!1;let s=n.target;const l=[];for(;"DIV"!==s.nodeName;)l.push(s),s=s.parentNode;if(!l.find((e=>"A"===e.nodeName)))return!1;const a=Xs(e.state,r.type.name),c=n.target,u=null!==(o=null==c?void 0:c.href)&&void 0!==o?o:a.href,d=null!==(i=null==c?void 0:c.target)&&void 0!==i?i:a.target;return!(!c||!u||(window.open(u,d),0))}}}))),this.options.linkOnPaste&&e.push(function(e){return new Yt({key:new en("handlePasteLink"),props:{handlePaste:(t,n,r)=>{const{state:o}=t,{selection:i}=o,{empty:s}=i;if(s)return!1;let l="";r.content.forEach((e=>{l+=e.textContent}));const a=ed(l,{defaultProtocol:e.defaultProtocol}).find((e=>e.isLink&&e.value===l));return!(!l||!a)&&e.editor.commands.setMark(e.type,{href:a.href})}}})}({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),e}}),id=Sl.create({name:"taskList",addOptions:()=>({itemTypeName:"taskItem",HTMLAttributes:{}}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:`ul[data-type="${this.name}"]`,priority:51}]},renderHTML({HTMLAttributes:e}){return["ul",as(this.options.HTMLAttributes,e,{"data-type":this.name}),0]},addCommands(){return{toggleTaskList:()=>({commands:e})=>e.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-9":()=>this.editor.commands.toggleTaskList()}}}),sd=/^\s*(\[([( |x])?\])\s$/,ld=Sl.create({name:"taskItem",addOptions:()=>({nested:!1,HTMLAttributes:{},taskListTypeName:"taskList"}),content(){return this.options.nested?"paragraph block*":"paragraph+"},defining:!0,addAttributes:()=>({checked:{default:!1,keepOnSplit:!1,parseHTML:e=>{const t=e.getAttribute("data-checked");return""===t||"true"===t},renderHTML:e=>({"data-checked":e.checked})}}),parseHTML(){return[{tag:`li[data-type="${this.name}"]`,priority:51}]},renderHTML({node:e,HTMLAttributes:t}){return["li",as(this.options.HTMLAttributes,t,{"data-type":this.name}),["label",["input",{type:"checkbox",checked:e.attrs.checked?"checked":null}],["span"]],["div",0]]},addKeyboardShortcuts(){const e={Enter:()=>this.editor.commands.splitListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)};return this.options.nested?{...e,Tab:()=>this.editor.commands.sinkListItem(this.name)}:e},addNodeView(){return({node:e,HTMLAttributes:t,getPos:n,editor:r})=>{const o=document.createElement("li"),i=document.createElement("label"),s=document.createElement("span"),l=document.createElement("input"),a=document.createElement("div");return i.contentEditable="false",l.type="checkbox",l.addEventListener("mousedown",(e=>e.preventDefault())),l.addEventListener("change",(t=>{if(!r.isEditable&&!this.options.onReadOnlyChecked)return void(l.checked=!l.checked);const{checked:o}=t.target;r.isEditable&&"function"==typeof n&&r.chain().focus(void 0,{scrollIntoView:!1}).command((({tr:e})=>{const t=n();if("number"!=typeof t)return!1;const r=e.doc.nodeAt(t);return e.setNodeMarkup(t,void 0,{...null==r?void 0:r.attrs,checked:o}),!0})).run(),!r.isEditable&&this.options.onReadOnlyChecked&&(this.options.onReadOnlyChecked(e,o)||(l.checked=!l.checked))})),Object.entries(this.options.HTMLAttributes).forEach((([e,t])=>{o.setAttribute(e,t)})),o.dataset.checked=e.attrs.checked,l.checked=e.attrs.checked,i.append(l,s),o.append(i,a),Object.entries(t).forEach((([e,t])=>{o.setAttribute(e,t)})),{dom:o,contentDOM:a,update:e=>e.type===this.type&&(o.dataset.checked=e.attrs.checked,l.checked=e.attrs.checked,!0)}}},addInputRules(){return[wl({find:sd,type:this.type,getAttributes:e=>({checked:"x"===e[e.length-1]})})]}}),ad=({editor:e})=>{if(!e)return null;return c.jsxs("div",{className:"menu-bar",children:[c.jsxs("div",{className:"formatting-group",children:[c.jsx("button",{onClick:()=>e.chain().focus().toggleBold().run(),className:e.isActive("bold")?"is-active":"",title:"Bold",children:c.jsx("span",{className:"icon",children:"B"})}),c.jsx("button",{onClick:()=>e.chain().focus().toggleItalic().run(),className:e.isActive("italic")?"is-active":"",title:"Italic",children:c.jsx("span",{className:"icon",children:"I"})}),c.jsx("button",{onClick:()=>e.chain().focus().toggleUnderline().run(),className:e.isActive("underline")?"is-active":"",title:"Underline",children:c.jsx("span",{className:"icon",children:"U"})})]}),c.jsxs("div",{className:"formatting-group",children:[c.jsx("button",{onClick:()=>e.chain().focus().toggleHeading({level:1}).run(),className:e.isActive("heading",{level:1})?"is-active":"",title:"Heading 1",children:c.jsx("span",{className:"icon",children:"H1"})}),c.jsx("button",{onClick:()=>e.chain().focus().toggleHeading({level:2}).run(),className:e.isActive("heading",{level:2})?"is-active":"",title:"Heading 2",children:c.jsx("span",{className:"icon",children:"H2"})}),c.jsx("button",{onClick:()=>e.chain().focus().toggleHeading({level:3}).run(),className:e.isActive("heading",{level:3})?"is-active":"",title:"Heading 3",children:c.jsx("span",{className:"icon",children:"H3"})})]}),c.jsxs("div",{className:"formatting-group",children:[c.jsx("button",{onClick:()=>e.chain().focus().toggleBulletList().run(),className:e.isActive("bulletList")?"is-active":"",title:"Bullet List",children:c.jsx("span",{className:"icon",children:"•"})}),c.jsx("button",{onClick:()=>e.chain().focus().toggleOrderedList().run(),className:e.isActive("orderedList")?"is-active":"",title:"Ordered List",children:c.jsx("span",{className:"icon",children:"1."})}),c.jsx("button",{onClick:()=>{e.chain().focus().toggleTaskList().run()},className:e.isActive("taskList")?"is-active":"",title:"代办事项",children:c.jsx("span",{className:"icon",children:"☑"})})]}),c.jsxs("div",{className:"formatting-group",children:[c.jsx("button",{onClick:()=>e.chain().focus().setTextAlign("left").run(),className:e.isActive({textAlign:"left"})?"is-active":"",title:"Align Left",children:c.jsx("span",{className:"icon",children:"←"})}),c.jsx("button",{onClick:()=>e.chain().focus().setTextAlign("center").run(),className:e.isActive({textAlign:"center"})?"is-active":"",title:"Align Center",children:c.jsx("span",{className:"icon",children:"↔"})}),c.jsx("button",{onClick:()=>e.chain().focus().setTextAlign("right").run(),className:e.isActive({textAlign:"right"})?"is-active":"",title:"Align Right",children:c.jsx("span",{className:"icon",children:"→"})})]}),c.jsxs("div",{className:"formatting-group",children:[c.jsx("button",{onClick:()=>{const t=window.prompt("URL");t&&e.chain().focus().extendMarkRange("link").setLink({href:t}).run()},className:e.isActive("link")?"is-active":"",title:"Add Link",children:c.jsx("span",{className:"icon",children:"🔗"})}),c.jsx("button",{onClick:()=>{e.chain().focus().unsetLink().run()},disabled:!e.isActive("link"),title:"Remove Link",children:c.jsx("span",{className:"icon",children:"🔗❌"})})]})]})},cd=class e{constructor(){t(this,"editor",null)}static getInstance(){return e.instance||(e.instance=new e),e.instance}setEditor(e){this.editor=e}getEditor(){return this.editor}toggleBold(){var e;null==(e=this.editor)||e.chain().focus().toggleBold().run()}toggleItalic(){var e;null==(e=this.editor)||e.chain().focus().toggleItalic().run()}toggleUnderline(){var e;null==(e=this.editor)||e.chain().focus().toggleUnderline().run()}toggleH1(){var e;null==(e=this.editor)||e.chain().focus().toggleHeading({level:1}).run()}toggleH2(){var e;null==(e=this.editor)||e.chain().focus().toggleHeading({level:2}).run()}toggleH3(){var e;null==(e=this.editor)||e.chain().focus().toggleHeading({level:3}).run()}toggleBulletList(){var e;null==(e=this.editor)||e.chain().focus().toggleBulletList().run()}toggleOrderedList(){var e;null==(e=this.editor)||e.chain().focus().toggleOrderedList().run()}toggleTaskList(){var e;null==(e=this.editor)||e.chain().focus().toggleTaskList().run()}alignLeft(){var e;null==(e=this.editor)||e.chain().focus().setTextAlign("left").run()}alignCenter(){var e;null==(e=this.editor)||e.chain().focus().setTextAlign("center").run()}alignRight(){var e;null==(e=this.editor)||e.chain().focus().setTextAlign("right").run()}addLink(e){var t;e&&(null==(t=this.editor)||t.chain().focus().extendMarkRange("link").setLink({href:e}).run())}removeLink(){var e;null==(e=this.editor)||e.chain().focus().unsetLink().run()}getContent(){var e;return(null==(e=this.editor)?void 0:e.getHTML())||""}setContent(e){var t;null==(t=this.editor)||t.commands.setContent(e)}clearContent(){var e;null==(e=this.editor)||e.commands.clearContent()}undo(){var e;null==(e=this.editor)||e.chain().focus().undo().run()}redo(){var e;null==(e=this.editor)||e.chain().focus().redo().run()}isBold(){var e;return(null==(e=this.editor)?void 0:e.isActive("bold"))||!1}isItalic(){var e;return(null==(e=this.editor)?void 0:e.isActive("italic"))||!1}isUnderline(){var e;return(null==(e=this.editor)?void 0:e.isActive("underline"))||!1}isH1(){var e;return(null==(e=this.editor)?void 0:e.isActive("heading",{level:1}))||!1}isH2(){var e;return(null==(e=this.editor)?void 0:e.isActive("heading",{level:2}))||!1}isH3(){var e;return(null==(e=this.editor)?void 0:e.isActive("heading",{level:3}))||!1}isBulletList(){var e;return(null==(e=this.editor)?void 0:e.isActive("bulletList"))||!1}isOrderedList(){var e;return(null==(e=this.editor)?void 0:e.isActive("orderedList"))||!1}isTaskList(){var e;return(null==(e=this.editor)?void 0:e.isActive("taskList"))||!1}getTextAlign(){var e,t,n;return(null==(e=this.editor)?void 0:e.isActive({textAlign:"left"}))?"left":(null==(t=this.editor)?void 0:t.isActive({textAlign:"center"}))?"center":(null==(n=this.editor)?void 0:n.isActive({textAlign:"right"}))?"right":"left"}isLink(){var e;return(null==(e=this.editor)?void 0:e.isActive("link"))||!1}};t(cd,"instance");const ud=cd.getInstance();"undefined"!=typeof window&&(window.editorBridge=ud);const dd=()=>{const e=ql({extensions:[ic,sc,lc.configure({defaultAlignment:"left",types:["heading","paragraph"]}),od.configure({openOnClick:!0}),id,ld.configure({HTMLAttributes:{class:"task-item"},nested:!0})],content:'<p>Hello World! This is a rich text editor with a menu bar.</p><ul data-type="taskList"><li data-type="taskItem" data-checked="true"><p>已完成的任务</p></li><li data-type="taskItem" data-checked="false"><p>未完成的任务</p></li></ul>'});return p.useEffect((()=>{var t;if(!e)return;ud.setEditor(e);const n=()=>{var t;const n=e.getHTML();console.log("Editor content updated:",n.substring(0,50)+"..."),(null==(t=window.AndroidInterface)?void 0:t.onContentChanged)&&window.AndroidInterface.onContentChanged(n)};return e.on("update",n),(null==(t=window.AndroidInterface)?void 0:t.onEditorReady)&&window.AndroidInterface.onEditorReady(),()=>{e.off("update",n),ud.setEditor(null)}}),[e]),c.jsxs("div",{className:"editor-container",children:[c.jsx(ad,{editor:e}),c.jsx(Ll,{className:"tiptap-editor-content",editor:e})]})};function fd(){return c.jsx("div",{className:"app-container",children:c.jsx(dd,{})})}P.createRoot(document.getElementById("root")).render(c.jsx(p.StrictMode,{children:c.jsx(fd,{})}));
//# sourceMappingURL=index-C3eg607V.js.map
