// Tiptap 核心库和扩展的本地备份
// 这是一个简化版本，仅包含基本功能

// 定义全局命名空间
window.tiptap = window.tiptap || {};
window.tiptapStarterKit = window.tiptapStarterKit || {};
window.tiptapExtensionTaskList = window.tiptapExtensionTaskList || {};
window.tiptapExtensionTaskItem = window.tiptapExtensionTaskItem || {};

// 基本编辑器类
window.tiptap.Editor = class Editor {
  constructor(options) {
    this.options = options;
    this.element = options.element;
    this.extensions = options.extensions || [];
    this.content = options.content || '';
    this.onUpdate = options.onUpdate || (() => {});

    // 初始化编辑器
    this.init();
  }

  init() {
    // 设置初始内容
    if (this.content) {
      this.element.innerHTML = this.content;
    }

    // 使元素可编辑
    this.element.contentEditable = true;

    // 添加基本样式类
    this.element.classList.add('ProseMirror');

    // 模拟视图对象
    this.view = {
      state: {
        doc: {
          descendants: (callback) => {
            // 简单实现，仅用于演示
            const taskItems = this.element.querySelectorAll('[data-type="taskItem"]');
            taskItems.forEach((item, index) => {
              const isChecked = item.getAttribute('data-checked') === 'true';
              callback({
                type: { name: 'taskItem' },
                attrs: { checked: isChecked }
              }, index);
            });
          }
        },
        tr: {
          delete: (start, end) => {
            // 简单实现，仅用于演示
            console.log('删除从', start, '到', end);
          }
        }
      },
      dispatch: (tr) => {
        console.log('执行事务');
      }
    };

    // 添加输入事件监听
    this.element.addEventListener('input', () => {
      if (this.onUpdate) {
        this.onUpdate({ editor: this });
      }
    });

    // 添加键盘事件监听
    this.element.addEventListener('keydown', (e) => {
      // 处理基本键盘事件
      if (e.key === 'Tab') {
        e.preventDefault();
        document.execCommand('insertHTML', false, '&nbsp;&nbsp;&nbsp;&nbsp;');
      }
    });
  }

  // 命令链
  chain() {
    return {
      focus: () => {
        return {
          // 添加撤销功能
          undo: () => {
            return {
              run: () => {
                this.element.focus();
                document.execCommand('undo', false, null);
                this.onUpdate({ editor: this });
                return true;
              }
            };
          },
          // 添加重做功能
          redo: () => {
            return {
              run: () => {
                this.element.focus();
                document.execCommand('redo', false, null);
                this.onUpdate({ editor: this });
                return true;
              }
            };
          },
          toggleBold: () => {
            return {
              run: () => {
                this.element.focus();
                document.execCommand('bold', false, null);
              }
            };
          },
          toggleItalic: () => {
            return {
              run: () => {
                this.element.focus();
                document.execCommand('italic', false, null);
              }
            };
          },
          toggleStrike: () => {
            return {
              run: () => {
                this.element.focus();
                document.execCommand('strikeThrough', false, null);
              }
            };
          },
          toggleHeading: (attrs) => {
            return {
              run: () => {
                this.element.focus();
                const level = attrs.level;
                document.execCommand('formatBlock', false, `h${level}`);
              }
            };
          },
          toggleBulletList: () => {
            return {
              run: () => {
                this.element.focus();
                document.execCommand('insertUnorderedList', false, null);
              }
            };
          },
          toggleOrderedList: () => {
            return {
              run: () => {
                this.element.focus();
                document.execCommand('insertOrderedList', false, null);
              }
            };
          },
          toggleTaskList: () => {
            return {
              run: () => {
                this.element.focus();
                // 简单实现，插入一个任务项
                const taskItem = `
                  <ul data-type="taskList">
                    <li data-type="taskItem" data-checked="false">
                      <label>
                        <input type="checkbox">
                        <span>新的待办事项</span>
                      </label>
                    </li>
                  </ul>
                `;
                document.execCommand('insertHTML', false, taskItem);
              }
            };
          },
          toggleCodeBlock: () => {
            return {
              run: () => {
                this.element.focus();
                document.execCommand('formatBlock', false, 'pre');
              }
            };
          },
          toggleBlockquote: () => {
            return {
              run: () => {
                this.element.focus();
                document.execCommand('formatBlock', false, 'blockquote');
              }
            };
          },
          clearNodes: () => {
            return {
              unsetAllMarks: () => {
                return {
                  run: () => {
                    this.element.focus();
                    document.execCommand('removeFormat', false, null);
                  }
                };
              }
            };
          }
        };
      }
    };
  }

  // 命令对象
  commands = {
    focus: () => {
      this.element.focus();
    },
    undo: () => {
      document.execCommand('undo', false, null);
      this.onUpdate({ editor: this });
    },
    redo: () => {
      document.execCommand('redo', false, null);
      this.onUpdate({ editor: this });
    },
    insertImage: (url) => {
      this.element.focus();
      const imgHtml = `<img src="${url}" alt="插入的图片" style="max-width: 100%; height: auto;">`;
      document.execCommand('insertHTML', false, imgHtml);
      this.onUpdate({ editor: this });
    }
  };

  // 检查命令是否可用
  can() {
    return {
      undo: () => {
        // 简单实现，始终返回 true
        return true;
      },
      redo: () => {
        // 简单实现，始终返回 true
        return true;
      }
    };
  };

  // 检查是否激活
  isActive(type, attrs) {
    // 简单实现，仅用于演示
    switch(type) {
      case 'bold':
        return document.queryCommandState('bold');
      case 'italic':
        return document.queryCommandState('italic');
      case 'strike':
        return document.queryCommandState('strikeThrough');
      case 'heading':
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
          const parentElement = selection.getRangeAt(0).commonAncestorContainer;
          const headingElement = parentElement.nodeType === 1 ?
            parentElement : parentElement.parentElement;
          return headingElement && headingElement.tagName === `H${attrs.level}`;
        }
        return false;
      case 'bulletList':
        return document.queryCommandState('insertUnorderedList');
      case 'orderedList':
        return document.queryCommandState('insertOrderedList');
      case 'taskList':
        const selection2 = window.getSelection();
        if (selection2.rangeCount > 0) {
          const parentElement = selection2.getRangeAt(0).commonAncestorContainer;
          const listElement = parentElement.closest('[data-type="taskList"]');
          return !!listElement;
        }
        return false;
      case 'codeBlock':
        const selection3 = window.getSelection();
        if (selection3.rangeCount > 0) {
          const parentElement = selection3.getRangeAt(0).commonAncestorContainer;
          const preElement = parentElement.closest('pre');
          return !!preElement;
        }
        return false;
      case 'blockquote':
        const selection4 = window.getSelection();
        if (selection4.rangeCount > 0) {
          const parentElement = selection4.getRangeAt(0).commonAncestorContainer;
          const blockquoteElement = parentElement.closest('blockquote');
          return !!blockquoteElement;
        }
        return false;
      default:
        return false;
    }
  }

  // 获取HTML内容
  getHTML() {
    return this.element.innerHTML;
  }

  // 获取纯文本内容
  getText() {
    return this.element.innerText;
  }

  // 获取JSON内容
  getJSON() {
    // 简单实现，仅返回文本内容
    return {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: this.getText()
            }
          ]
        }
      ]
    };
  }
};

// StarterKit 扩展
window.tiptapStarterKit.StarterKit = {
  name: 'starterKit',
  // 这里简化实现，实际上StarterKit包含多个扩展
  configure: (options) => {
    return {
      ...window.tiptapStarterKit.StarterKit,
      options
    };
  }
};

// TaskList 扩展
window.tiptapExtensionTaskList.TaskList = {
  name: 'taskList',
};

// TaskItem 扩展
window.tiptapExtensionTaskItem.TaskItem = {
  name: 'taskItem',
  configure: (options) => {
    return {
      ...window.tiptapExtensionTaskItem.TaskItem,
      options
    };
  }
};

// 添加一个初始化完成的标志
window.tiptapLoaded = true;
console.log('Tiptap本地库已加载');
