<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>Slate.js 富文本编辑器</title>

    <style>
        html, body {
            font-family: system-ui, -apple-system, sans-serif;
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        body {
            display: flex;
            flex-direction: column;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .editor-container {
            display: flex;
            flex-direction: column;
            flex: 1;
            height: 100%;
            background-color: white;
            overflow: hidden;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .editor-title {
            font-size: 20px;
            font-weight: 500;
            color: #333;
        }

        .editor-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 0;
            margin: 0;
            position: relative;
            transition: padding-bottom 0.3s ease;
        }

        .editor-toolbar {
            display: flex;
            flex-wrap: wrap;
            padding: 8px;
            background-color: #f9f9f9;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .toolbar-button {
            background-color: transparent;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            cursor: pointer;
            color: #333;
        }

        .toolbar-button:active {
            background-color: #e0e0e0;
        }

        .toolbar-button.active {
            background-color: #e0e0e0;
            border-color: #bbb;
        }

        .editor-area {
            padding: 16px;
            min-height: 200px;
            outline: none;
            flex: 1;
        }

        .editor-actions {
            display: flex;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-top: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .editor-btn {
            background-color: #4285f4;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            margin-right: 12px;
            font-weight: 500;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }

        .editor-btn:active {
            background-color: #3367d6;
        }

        /* Slate 编辑器样式 */
        [data-slate-editor] {
            padding: 16px;
            min-height: 200px;
        }

        [data-slate-node="element"] {
            margin: 8px 0;
        }

        .slate-bold {
            font-weight: bold;
        }

        .slate-italic {
            font-style: italic;
        }

        .slate-underline {
            text-decoration: underline;
        }

        .slate-heading-one {
            font-size: 2em;
            font-weight: bold;
            margin: 16px 0 8px 0;
        }

        .slate-heading-two {
            font-size: 1.5em;
            font-weight: bold;
            margin: 14px 0 7px 0;
        }

        .slate-list-item {
            padding-left: 10px;
            margin-left: 20px;
            position: relative;
        }

        .slate-bulleted-list {
            list-style-type: disc;
            margin-left: 20px;
        }

        .slate-numbered-list {
            list-style-type: decimal;
            margin-left: 20px;
        }

        .slate-check-list-item {
            display: flex;
            align-items: flex-start;
            margin: 8px 0;
        }

        .slate-check-list-item-checkbox {
            margin-right: 8px;
            margin-top: 4px;
        }

        .slate-check-list-item-checked {
            text-decoration: line-through;
            color: #888;
        }
    </style>

    <!-- 添加错误处理 -->
    <script>
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('JS Error:', message, 'at', source, lineno, colno);
            document.getElementById('editor-root').innerHTML =
                '<div style="color: red; padding: 20px;">编辑器加载错误: ' + message + '</div>';
            return true;
        };

        // 定义加载状态标志
        window.slateLibsLoaded = false;

        // 尝试加载外部CDN库
        function loadExternalLibs() {
            return new Promise((resolve, reject) => {
                try {
                    // 设置超时
                    const timeout = setTimeout(() => {
                        console.log('CDN加载超时，切换到本地库');
                        reject(new Error('CDN加载超时'));
                    }, 5000);

                    // 加载 React
                    const script1 = document.createElement('script');
                    script1.src = 'https://cdn.jsdelivr.net/npm/react@17/umd/react.production.min.js';
                    script1.crossOrigin = 'anonymous';
                    script1.onload = () => {
                        // 加载 ReactDOM
                        const script2 = document.createElement('script');
                        script2.src = 'https://cdn.jsdelivr.net/npm/react-dom@17/umd/react-dom.production.min.js';
                        script2.crossOrigin = 'anonymous';
                        script2.onload = () => {
                            // 加载 Slate
                            const script3 = document.createElement('script');
                            script3.src = 'https://cdn.jsdelivr.net/npm/slate@0.66.5/dist/slate.min.js';
                            script3.crossOrigin = 'anonymous';
                            script3.onload = () => {
                                // 加载 Slate-React
                                const script4 = document.createElement('script');
                                script4.src = 'https://cdn.jsdelivr.net/npm/slate-react@0.66.5/dist/slate-react.min.js';
                                script4.crossOrigin = 'anonymous';
                                script4.onload = () => {
                                    // 加载 Babel
                                    const script5 = document.createElement('script');
                                    script5.src = 'https://cdn.jsdelivr.net/npm/@babel/standalone@7.18.8/babel.min.js';
                                    script5.crossOrigin = 'anonymous';
                                    script5.onload = () => {
                                        clearTimeout(timeout);
                                        window.slateLibsLoaded = true;
                                        console.log('CDN库加载成功');
                                        resolve();
                                    };
                                    script5.onerror = () => {
                                        clearTimeout(timeout);
                                        reject(new Error('加载 Babel 失败'));
                                    };
                                    document.head.appendChild(script5);
                                };
                                script4.onerror = () => {
                                    clearTimeout(timeout);
                                    reject(new Error('加载 Slate-React 失败'));
                                };
                                document.head.appendChild(script4);
                            };
                            script3.onerror = () => {
                                clearTimeout(timeout);
                                reject(new Error('加载 Slate 失败'));
                            };
                            document.head.appendChild(script3);
                        };
                        script2.onerror = () => {
                            clearTimeout(timeout);
                            reject(new Error('加载 ReactDOM 失败'));
                        };
                        document.head.appendChild(script2);
                    };
                    script1.onerror = () => {
                        clearTimeout(timeout);
                        reject(new Error('加载 React 失败'));
                    };
                    document.head.appendChild(script1);
                } catch (e) {
                    reject(e);
                }
            });
        }

        // 加载本地库
        function loadLocalLib() {
            return new Promise((resolve) => {
                // 加载 React
                const reactScript = document.createElement('script');
                reactScript.textContent = 'window.React = { createElement: function() { return {}; }, Fragment: {} };';
                document.head.appendChild(reactScript);

                // 加载 ReactDOM
                const reactDOMScript = document.createElement('script');
                reactDOMScript.textContent = 'window.ReactDOM = { render: function() {} };';
                document.head.appendChild(reactDOMScript);

                // 加载本地 Slate 库
                const script = document.createElement('script');
                script.src = 'slate-bundle.js';
                script.onload = () => {
                    console.log('本地库加载成功');
                    window.slateLibsLoaded = true;
                    resolve();
                };
                script.onerror = () => {
                    console.error('本地库加载失败');
                    // 即使本地库加载失败也尝试继续
                    resolve();
                };
                document.head.appendChild(script);
            });
        }

        // 先尝试加载外部库，失败后加载本地库
        window.loadSlateLibs = async function() {
            try {
                await loadExternalLibs();
            } catch (e) {
                console.error('外部库加载失败，切换到本地库', e);
                await loadLocalLib();
            }
        };
    </script>
</head>
<body>
    <div class="editor-container">
        <div class="editor-header">
            <div class="editor-title">Slate.js 富文本编辑器</div>
        </div>

        <div id="editor-root" class="editor-content"></div>

        <div class="editor-actions">
            <button class="editor-btn" onclick="saveContent()">保存内容</button>
            <button class="editor-btn" onclick="getHtml()">获取HTML</button>
            <button class="editor-btn" onclick="getText()">获取纯文本</button>
            <button class="editor-btn" onclick="clearCompletedTodos()" style="background-color: #ff5722;">清除已完成事项</button>
        </div>
    </div>

    <!-- 添加加载状态提示 -->
    <script>
        document.getElementById('editor-root').innerHTML = '<div style="padding: 20px; text-align: center;">正在加载编辑器...</div>';

        // 加载库并初始化编辑器
        (async function() {
            try {
                // 加载库
                await window.loadSlateLibs();

                // 检查库是否成功加载
                if (!window.slateLibsLoaded) {
                    document.getElementById('editor-root').innerHTML =
                        '<div style="color: red; padding: 20px;">编辑器库加载失败，请刷新页面重试</div>';
                    return;
                }

                // 添加编辑器脚本
                const script = document.createElement('script');
                script.type = 'text/babel';
                script.textContent = document.querySelector('script[type="text/babel"]').textContent;
                document.body.appendChild(script);
            } catch (error) {
                console.error('初始化编辑器失败:', error);
                document.getElementById('editor-root').innerHTML =
                    '<div style="color: red; padding: 20px;">编辑器初始化失败: ' + error.message + '</div>';
            }
        })();
    </script>

    <script type="text/babel" style="display:none">
        // 创建 Slate 编辑器
        const { useState, useCallback, useMemo, useEffect } = React;
        // 确保 Slate 和 SlateReact 已正确加载
        if (!window.Slate || !window.SlateReact) {
            console.error('Slate 或 SlateReact 库未加载');
            document.getElementById('editor-root').innerHTML =
                '<div style="color: red; padding: 20px;">编辑器库加载失败，请检查网络连接或刷新页面</div>';
            throw new Error('Slate 或 SlateReact 库未加载');
        }

        const { createEditor, Editor, Transforms, Element: SlateElement, Text, Node } = window.Slate;
        const { Slate, Editable, withReact, useSlate, ReactEditor } = window.SlateReact;

        // 自定义元素类型
        const ElementTypes = {
            PARAGRAPH: 'paragraph',
            HEADING_ONE: 'heading-one',
            HEADING_TWO: 'heading-two',
            BULLETED_LIST: 'bulleted-list',
            NUMBERED_LIST: 'numbered-list',
            LIST_ITEM: 'list-item',
            CHECK_LIST_ITEM: 'check-list-item',
        };

        // 自定义标记类型
        const MarkTypes = {
            BOLD: 'bold',
            ITALIC: 'italic',
            UNDERLINE: 'underline',
        };

        // 初始内容
        const initialValue = [
            {
                type: ElementTypes.HEADING_ONE,
                children: [{ text: 'Slate.js 富文本编辑器示例' }],
            },
            {
                type: ElementTypes.PARAGRAPH,
                children: [{ text: '这是一个使用 Slate.js 实现的富文本编辑器示例。' }],
            },
            {
                type: ElementTypes.HEADING_TWO,
                children: [{ text: '主要功能：' }],
            },
            {
                type: ElementTypes.BULLETED_LIST,
                children: [
                    {
                        type: ElementTypes.LIST_ITEM,
                        children: [{ text: '文本格式化（粗体、斜体、下划线等）' }],
                    },
                    {
                        type: ElementTypes.LIST_ITEM,
                        children: [{ text: '标题（一级标题、二级标题）' }],
                    },
                    {
                        type: ElementTypes.LIST_ITEM,
                        children: [{ text: '列表（有序和无序）' }],
                    },
                    {
                        type: ElementTypes.LIST_ITEM,
                        children: [{ text: '待办事项列表' }],
                    },
                ],
            },
            {
                type: ElementTypes.PARAGRAPH,
                children: [
                    { text: '试试看各种编辑功能吧！', italic: true, color: '#2196f3' },
                ],
            },
            {
                type: ElementTypes.HEADING_TWO,
                children: [{ text: '待办事项示例：' }],
            },
            {
                type: ElementTypes.CHECK_LIST_ITEM,
                checked: false,
                children: [{ text: '这是一个待办事项' }],
            },
            {
                type: ElementTypes.CHECK_LIST_ITEM,
                checked: true,
                children: [{ text: '这是一个已完成的待办事项' }],
            },
            {
                type: ElementTypes.CHECK_LIST_ITEM,
                checked: false,
                children: [{ text: '点击工具栏中的待办事项按钮添加新的待办事项' }],
            },
        ];

        // 工具栏按钮组件
        const ToolbarButton = ({ active, onMouseDown, children }) => (
            <button
                className={`toolbar-button ${active ? 'active' : ''}`}
                onMouseDown={onMouseDown}
            >
                {children}
            </button>
        );

        // 标记按钮
        const MarkButton = ({ format, icon }) => {
            const editor = useSlate();

            const isMarkActive = (editor, format) => {
                const marks = Editor.marks(editor);
                return marks ? marks[format] === true : false;
            };

            const toggleMark = (editor, format) => {
                const isActive = isMarkActive(editor, format);
                if (isActive) {
                    Editor.removeMark(editor, format);
                } else {
                    Editor.addMark(editor, format, true);
                }
            };

            return (
                <ToolbarButton
                    active={isMarkActive(editor, format)}
                    onMouseDown={(event) => {
                        event.preventDefault();
                        toggleMark(editor, format);
                    }}
                >
                    {icon}
                </ToolbarButton>
            );
        };

        // 块级元素按钮
        const BlockButton = ({ format, icon }) => {
            const editor = useSlate();

            const isBlockActive = (editor, format) => {
                const [match] = Editor.nodes(editor, {
                    match: n => !Editor.isEditor(n) && SlateElement.isElement(n) && n.type === format,
                });
                return !!match;
            };

            const toggleBlock = (editor, format) => {
                const isActive = isBlockActive(editor, format);

                Transforms.unwrapNodes(editor, {
                    match: n => !Editor.isEditor(n) && SlateElement.isElement(n) &&
                        [ElementTypes.BULLETED_LIST, ElementTypes.NUMBERED_LIST].includes(n.type),
                    split: true,
                });

                const newProperties = {
                    type: isActive ? ElementTypes.PARAGRAPH : format === ElementTypes.CHECK_LIST_ITEM
                        ? ElementTypes.CHECK_LIST_ITEM
                        : format === ElementTypes.LIST_ITEM
                            ? ElementTypes.LIST_ITEM
                            : format,
                };

                if (format === ElementTypes.CHECK_LIST_ITEM && !isActive) {
                    newProperties.checked = false;
                }

                Transforms.setNodes(editor, newProperties);

                if (!isActive && [ElementTypes.LIST_ITEM, ElementTypes.BULLETED_LIST, ElementTypes.NUMBERED_LIST].includes(format)) {
                    const block = { type: format === ElementTypes.LIST_ITEM ? ElementTypes.BULLETED_LIST : format, children: [] };
                    Transforms.wrapNodes(editor, block);
                }
            };

            return (
                <ToolbarButton
                    active={isBlockActive(editor, format)}
                    onMouseDown={(event) => {
                        event.preventDefault();
                        toggleBlock(editor, format);
                    }}
                >
                    {icon}
                </ToolbarButton>
            );
        };

        // 工具栏组件
        const Toolbar = () => {
            return (
                <div className="editor-toolbar">
                    <MarkButton format={MarkTypes.BOLD} icon="粗体" />
                    <MarkButton format={MarkTypes.ITALIC} icon="斜体" />
                    <MarkButton format={MarkTypes.UNDERLINE} icon="下划线" />
                    <BlockButton format={ElementTypes.HEADING_ONE} icon="标题1" />
                    <BlockButton format={ElementTypes.HEADING_TWO} icon="标题2" />
                    <BlockButton format={ElementTypes.BULLETED_LIST} icon="无序列表" />
                    <BlockButton format={ElementTypes.NUMBERED_LIST} icon="有序列表" />
                    <BlockButton format={ElementTypes.CHECK_LIST_ITEM} icon="待办事项" />
                </div>
            );
        };

        // 渲染元素
        const renderElement = useCallback(props => {
            const { attributes, children, element } = props;

            switch (element.type) {
                case ElementTypes.HEADING_ONE:
                    return <h1 className="slate-heading-one" {...attributes}>{children}</h1>;
                case ElementTypes.HEADING_TWO:
                    return <h2 className="slate-heading-two" {...attributes}>{children}</h2>;
                case ElementTypes.BULLETED_LIST:
                    return <ul className="slate-bulleted-list" {...attributes}>{children}</ul>;
                case ElementTypes.NUMBERED_LIST:
                    return <ol className="slate-numbered-list" {...attributes}>{children}</ol>;
                case ElementTypes.LIST_ITEM:
                    return <li className="slate-list-item" {...attributes}>{children}</li>;
                case ElementTypes.CHECK_LIST_ITEM:
                    return (
                        <div className={`slate-check-list-item ${element.checked ? 'slate-check-list-item-checked' : ''}`} {...attributes}>
                            <input
                                type="checkbox"
                                className="slate-check-list-item-checkbox"
                                checked={element.checked}
                                onChange={event => {
                                    try {
                                        const path = ReactEditor.findPath(editor, element);
                                        Transforms.setNodes(
                                            editor,
                                            { checked: event.target.checked },
                                            { at: path }
                                        );
                                    } catch (error) {
                                        console.error('更新复选框状态失败:', error);
                                    }
                                }}
                            />
                            <span>{children}</span>
                        </div>
                    );
                default:
                    return <p {...attributes}>{children}</p>;
            }
        }, []);

        // 渲染标记
        const renderLeaf = useCallback(({ attributes, children, leaf }) => {
            let el = <>{children}</>;

            if (leaf.bold) {
                el = <strong className="slate-bold">{el}</strong>;
            }

            if (leaf.italic) {
                el = <em className="slate-italic">{el}</em>;
            }

            if (leaf.underline) {
                el = <u className="slate-underline">{el}</u>;
            }

            if (leaf.color) {
                el = <span style={{ color: leaf.color }}>{el}</span>;
            }

            return <span {...attributes}>{el}</span>;
        }, []);

        // 主编辑器组件
        const SlateEditor = () => {
            const [value, setValue] = useState(initialValue);
            const editor = useMemo(() => withReact(createEditor()), []);

            // 确保 ReactEditor 已正确加载
            if (!ReactEditor) {
                console.error('ReactEditor 未加载');
                return <div style={{ color: 'red', padding: '20px' }}>编辑器组件加载失败</div>;
            }

            // 保存编辑器实例到全局变量，以便外部访问
            window.slateEditor = editor;
            window.slateValue = value;

            // 处理软键盘适配
            useEffect(() => {
                window.adjustEditorForKeyboard = (keyboardHeight) => {
                    const editorContent = document.querySelector('.editor-content');
                    if (editorContent) {
                        editorContent.style.paddingBottom = keyboardHeight + 'px';
                    }
                };

                window.resetEditorLayout = () => {
                    const editorContent = document.querySelector('.editor-content');
                    if (editorContent) {
                        editorContent.style.paddingBottom = '0px';
                    }
                };

                return () => {
                    window.adjustEditorForKeyboard = null;
                    window.resetEditorLayout = null;
                };
            }, []);

            return (
                <Slate editor={editor} value={value} onChange={value => setValue(value)}>
                    <Toolbar />
                    <Editable
                        className="editor-area"
                        renderElement={renderElement}
                        renderLeaf={renderLeaf}
                        placeholder="开始编辑内容..."
                        spellCheck={false}
                        autoFocus
                    />
                </Slate>
            );
        };

        // 渲染编辑器
        try {
            ReactDOM.render(<SlateEditor />, document.getElementById('editor-root'));
        } catch (error) {
            console.error('渲染编辑器失败:', error);
            document.getElementById('editor-root').innerHTML =
                `<div style="color: red; padding: 20px;">编辑器渲染失败: ${error.message}</div>`;
        }

        // 保存内容到 Android
        function saveContent() {
            const content = JSON.stringify(window.slateValue);
            // 调用 Android 的接口保存内容
            if (typeof Android !== 'undefined') {
                Android.saveContent(content);
            } else {
                alert('内容已保存：\n' + content.substring(0, 100) + '...');
            }
        }

        // 获取 HTML 内容
        function getHtml() {
            const content = document.querySelector('.editor-area').innerHTML;
            if (typeof Android !== 'undefined') {
                Android.getHtmlContent(content);
            } else {
                alert('HTML内容：\n' + content.substring(0, 100) + '...');
            }
        }

        // 获取纯文本内容
        function getText() {
            let text = '';
            const getNodeText = (node) => {
                if (Text.isText(node)) {
                    return node.text;
                } else {
                    return node.children.map(getNodeText).join('');
                }
            };

            window.slateValue.forEach(node => {
                text += getNodeText(node) + '\n';
            });

            if (typeof Android !== 'undefined') {
                Android.getTextContent(text);
            } else {
                alert('纯文本内容：\n' + text.substring(0, 100) + '...');
            }
        }

        // 清除已完成的待办事项
        function clearCompletedTodos() {
            const editor = window.slateEditor;
            const value = window.slateValue;

            // 查找所有已完成的待办事项
            const completedTodos = [];
            for (let i = 0; i < value.length; i++) {
                const node = value[i];
                if (node.type === ElementTypes.CHECK_LIST_ITEM && node.checked) {
                    completedTodos.push([i]);
                }
            }

            if (completedTodos.length === 0) {
                alert('没有已完成的待办事项');
                return;
            }

            // 从后向前删除，避免索引变化
            completedTodos.reverse().forEach(path => {
                Transforms.removeNodes(editor, { at: path });
            });

            alert(`已清除 ${completedTodos.length} 个已完成的待办事项`);
        }
    </script>
</body>
</html>
