<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>Tiptap.js 富文本编辑器</title>

    <style>
        html, body {
            font-family: system-ui, -apple-system, sans-serif;
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
            height: 100%;
            width: 100%;
            overflow: hidden;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        /* 背景选择器样式 */
        .background-selector {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .background-selector.active {
            display: flex;
        }

        .background-selector-content {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .background-selector-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .background-selector-title {
            font-size: 18px;
            font-weight: bold;
        }

        .background-selector-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
        }

        .background-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .background-option {
            height: 80px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.2s ease;
            background-size: cover;
            background-position: center;
        }

        .background-option:hover {
            border-color: #4285f4;
        }

        .background-option.selected {
            border-color: #4285f4;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.5);
        }

        .background-colors {
            margin-top: 15px;
        }

        .color-options {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-top: 10px;
        }

        .color-option {
            height: 40px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid #ddd;
            transition: border-color 0.2s ease;
        }

        .color-option:hover {
            border-color: #4285f4;
        }

        .color-option.selected {
            border-color: #4285f4;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.5);
        }

        .background-actions {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }

        .background-action-btn {
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            margin-left: 10px;
        }

        .background-apply {
            background-color: #4285f4;
            color: white;
        }

        .background-reset {
            background-color: #f1f1f1;
            color: #333;
        }

        body {
            display: flex;
            flex-direction: column;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            height: 100vh; /* 使用视口高度 */
            width: 100vw; /* 使用视口宽度 */
            overflow: hidden;
        }

        .editor-container {
            display: flex;
            flex-direction: column;
            flex: 1;
            height: 100%;
            width: 100%;
            background-color: white;
            overflow: hidden;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .editor-title {
            font-size: 20px;
            font-weight: 500;
            color: #333;
        }

        .editor-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 0;
            margin: 0;
            position: relative;
            transition: padding-bottom 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* 无限画布模式相关样式 */
        .canvas-mode .editor-content {
            overflow: hidden;
            height: 100%;
            padding: 0;
        }

        .canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #f0f0f0;
            background-image: radial-gradient(#ddd 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .canvas-wrapper {
            position: absolute;
            transform-origin: 0 0;
            transition: transform 0.1s ease;
            min-width: 100%;
            min-height: 100%;
        }

        .canvas-mode .editor-area {
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 4px;
            margin: 50px;
            padding: 30px;
            min-width: 600px;
            min-height: 800px;
            width: auto;
            height: auto;
        }

        /* 画布控制按钮 */
        .canvas-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .canvas-control-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            font-size: 18px;
            transition: all 0.2s ease;
        }

        .canvas-control-button:hover {
            background-color: #f5f5f5;
            transform: scale(1.05);
        }

        .editor-toolbar {
            display: flex;
            flex-wrap: wrap;
            padding: 8px;
            background-color: #f9f9f9;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .toolbar-button {
            background-color: transparent;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            cursor: pointer;
            color: #333;
        }

        .toolbar-button:active {
            background-color: #e0e0e0;
        }

        .toolbar-button.is-active {
            background-color: #e0e0e0;
            border-color: #bbb;
        }

        .toolbar-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .editor-area {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            background-color: #fff;
            background-image: none;
            background-size: cover;
            background-position: center;
            transition: background-color 0.3s ease, background-image 0.3s ease;
            min-height: 100%;
            box-sizing: border-box;
            -webkit-overflow-scrolling: touch; /* 提高iOS滚动性能 */
        }

        .ProseMirror {
            outline: none;
            min-height: 200px;
            -webkit-user-select: text;
            user-select: text;
            cursor: text;
            touch-action: manipulation;
            caret-color: #4285f4;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
            flex: 1;
            height: 100%;
            box-sizing: border-box;
        }

        .ProseMirror p {
            margin: 1em 0;
        }

        .ProseMirror h1 {
            font-size: 2em;
            margin: 0.67em 0;
        }

        .ProseMirror h2 {
            font-size: 1.5em;
            margin: 0.75em 0;
        }

        .ProseMirror ul, .ProseMirror ol {
            padding-left: 1.2em;
        }

        .ProseMirror blockquote {
            border-left: 3px solid #ddd;
            margin-left: 0;
            margin-right: 0;
            padding-left: 1em;
            color: #666;
        }

        .ProseMirror code {
            background-color: #f0f0f0;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: monospace;
        }

        .ProseMirror pre {
            background-color: #f0f0f0;
            padding: 0.7em 1em;
            border-radius: 5px;
            overflow-x: auto;
        }

        .ProseMirror pre code {
            background: none;
            padding: 0;
        }

        .ProseMirror img {
            max-width: 100%;
            height: auto;
        }

        /* 录音块样式 */
        .voice-block {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #e0e0e0;
        }

        .voice-block-icon {
            width: 24px;
            height: 24px;
            background-color: #f44336;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            flex-shrink: 0;
        }

        .voice-block-icon::before {
            content: '';
            display: block;
            width: 10px;
            height: 10px;
            background-color: white;
            border-radius: 50%;
        }

        .voice-block-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .voice-block-duration {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .voice-block-controls {
            display: flex;
            align-items: center;
        }

        .voice-block-play {
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 8px;
        }

        .voice-block-text {
            font-size: 14px;
            color: #333;
            margin-top: 6px;
            border-top: 1px solid #e0e0e0;
            padding-top: 6px;
        }

        .ProseMirror a {
            color: #0074d9;
            text-decoration: none;
        }

        .ProseMirror a:hover {
            text-decoration: underline;
        }

        .ProseMirror .task-list {
            list-style-type: none;

            padding-left: 0;
            /* 无障碍属性 */
            role: "list";
            aria-label: "待办事项列表";
        }

        .ProseMirror .task-list-item {
            display: flex;
            align-items: flex-start;
            margin: 0.5em 0;
            /* 无障碍属性 */
            role: "listitem";
            aria-roledescription: "待办事项";
        }

        .ProseMirror .task-checkbox {
            margin-right: 0.5em;
            margin-top: 0.25em;
            /* 无障碍属性 */
            role: "checkbox";
            aria-checked: "false";
            tabindex: "0";
        }

        .editor-actions {
            display: flex;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-top: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .editor-btn {
            background-color: #4285f4;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            margin-right: 12px;
            font-weight: 500;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }

        .editor-btn:active {
            background-color: #3367d6;
        }

        /* 录音控制栏样式 */
        .voice-recording-bar {
            display: none;
            margin: 10px 0;
            padding: 10px 15px;
            background-color: #f44336;
            color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .voice-recording-bar.active {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .voice-recording-status {
            display: flex;
            align-items: center;
        }

        .recording-indicator {
            width: 12px;
            height: 12px;
            background-color: white;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 1.5s infinite;
        }

        .recording-timer {
            font-weight: bold;
            margin-right: 15px;
        }

        .voice-recording-controls {
            display: flex;
        }

        .voice-control-button {
            background-color: white;
            color: #f44336;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            margin-left: 10px;
            font-weight: bold;
            cursor: pointer;
        }

        .voice-control-button:hover {
            background-color: #eeeeee;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.3;
            }
            100% {
                opacity: 1;
            }
        }
    </style>

    <!-- 引入 Tiptap 相关库 -->
    <script>
        // 添加错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('JS Error:', message, 'at', source, lineno, colno);
            // 不显示错误弹窗，避免影响用户体验
            // alert('编辑器加载错误: ' + message);
            return true;
        };

        // 定义加载状态标志
        window.tiptapLibsLoaded = false;

        // 尝试加载外部CDN库
        function loadExternalLibs() {
            return new Promise((resolve, reject) => {
                try {
                    // 设置超时
                    const timeout = setTimeout(() => {
                        console.log('CDN加载超时，切换到本地库');
                        reject(new Error('CDN加载超时'));
                    }, 3000);

                    // 加载核心库
                    const script1 = document.createElement('script');
                    script1.src = 'https://unpkg.com/@tiptap/core@2.0.3/dist/tiptap-core.umd.min.js';
                    script1.onload = () => {
                        // 加载其他库
                        const script2 = document.createElement('script');
                        script2.src = 'https://unpkg.com/@tiptap/starter-kit@2.0.3/dist/tiptap-starter-kit.umd.min.js';
                        script2.onload = () => {
                            const script3 = document.createElement('script');
                            script3.src = 'https://unpkg.com/@tiptap/extension-task-list@2.0.3/dist/tiptap-extension-task-list.umd.min.js';
                            script3.onload = () => {
                                const script4 = document.createElement('script');
                                script4.src = 'https://unpkg.com/@tiptap/extension-task-item@2.0.3/dist/tiptap-extension-task-item.umd.min.js';
                                script4.onload = () => {
                                    clearTimeout(timeout);
                                    window.tiptapLibsLoaded = true;
                                    console.log('CDN库加载成功');
                                    resolve();
                                };
                                script4.onerror = () => {
                                    clearTimeout(timeout);
                                    reject(new Error('加载 task-item 失败'));
                                };
                                document.head.appendChild(script4);
                            };
                            script3.onerror = () => {
                                clearTimeout(timeout);
                                reject(new Error('加载 task-list 失败'));
                            };
                            document.head.appendChild(script3);
                        };
                        script2.onerror = () => {
                            clearTimeout(timeout);
                            reject(new Error('加载 starter-kit 失败'));
                        };
                        document.head.appendChild(script2);
                    };
                    script1.onerror = () => {
                        clearTimeout(timeout);
                        reject(new Error('加载 core 失败'));
                    };
                    document.head.appendChild(script1);
                } catch (e) {
                    reject(e);
                }
            });
        }

        // 加载本地库
        function loadLocalLib() {
            return new Promise((resolve) => {
                const script = document.createElement('script');
                script.src = 'tiptap-bundle.js';
                script.onload = () => {
                    console.log('本地库加载成功');
                    window.tiptapLibsLoaded = true;
                    resolve();
                };
                script.onerror = () => {
                    console.error('本地库加载失败');
                    // 即使本地库加载失败也尝试继续
                    resolve();
                };
                document.head.appendChild(script);
            });
        }

        // 先尝试加载外部库，失败后加载本地库
        window.loadTiptapLibs = async function() {
            try {
                await loadExternalLibs();
            } catch (e) {
                console.error('外部库加载失败，切换到本地库', e);
                await loadLocalLib();
            }
        };
    </script>
</head>
<body>
    <div class="editor-container">
        <div class="editor-header">
            <div class="editor-title">Tiptap.js 富文本编辑器</div>
            <button class="toolbar-button" data-action="back" title="返回" style="margin-left: auto;">返回</button>
        </div>

        <div class="editor-toolbar" id="editor-toolbar">
            <button class="toolbar-button" data-action="undo" title="撤销">撤销</button>
            <button class="toolbar-button" data-action="redo" title="重做">重做</button>
            <button class="toolbar-button" data-action="bold" title="粗体">粗体</button>
            <button class="toolbar-button" data-action="italic" title="斜体">斜体</button>
            <button class="toolbar-button" data-action="strike" title="删除线">删除线</button>
            <button class="toolbar-button" data-action="heading-1" title="标题1">标题1</button>
            <button class="toolbar-button" data-action="heading-2" title="标题2">标题2</button>
            <button class="toolbar-button" data-action="bullet-list" title="无序列表">无序列表</button>
            <button class="toolbar-button" data-action="ordered-list" title="有序列表">有序列表</button>
            <button class="toolbar-button" data-action="task-list" title="待办事项">待办事项</button>
            <button class="toolbar-button" data-action="code" title="代码">代码</button>
            <button class="toolbar-button" data-action="blockquote" title="引用">引用</button>
            <button class="toolbar-button" data-action="image" title="插入图片" style="background-color: #e3f2fd;">插入图片</button>
            <button class="toolbar-button" data-action="voice" title="录音" style="background-color: #ffebee;">录音</button>
            <button class="toolbar-button" data-action="background" title="更换背景" style="background-color: #e8f5e9;">背景</button>
            <button class="toolbar-button" data-action="canvas-toggle" title="切换画布模式" style="background-color: #e1f5fe;">画布</button>
            <button class="toolbar-button" data-action="clear" title="清除格式">清除格式</button>
        </div>

        <div class="editor-content" id="editor-content">
            <div class="editor-area" id="editor"></div>
        </div>

        <!-- 画布控制按钮 -->
        <div class="canvas-controls" id="canvas-controls" style="display: none;">
            <button class="canvas-control-button" id="zoom-in" title="放大">+</button>
            <button class="canvas-control-button" id="zoom-out" title="缩小">-</button>
            <button class="canvas-control-button" id="zoom-reset" title="重置缩放">&#8634;</button>
        </div>

<!--        <div class="editor-actions">-->
<!--            <button class="editor-btn" onclick="saveContent()">保存内容</button>-->
<!--            <button class="editor-btn" onclick="getHtml()">获取HTML</button>-->
<!--            <button class="editor-btn" onclick="getText()">获取纯文本</button>-->
<!--            <button class="editor-btn" onclick="clearCompletedTodos()" style="background-color: #ff5722;">清除已完成事项</button>-->
<!--        </div>-->
    </div>

    <!-- 背景选择器 -->
    <div id="background-selector" class="background-selector">
        <div class="background-selector-content">
            <div class="background-selector-header">
                <div class="background-selector-title">选择背景</div>
                <button class="background-selector-close" onclick="hideBackgroundOptions()">&times;</button>
            </div>

            <div class="background-images">
                <h3>背景图片</h3>
                <div class="background-options" id="background-images">
                    <!-- 背景图片选项将通过JS动态生成 -->
                </div>
            </div>

            <div class="background-colors">
                <h3>背景颜色</h3>
                <div class="color-options" id="background-colors">
                    <!-- 颜色选项将通过JS动态生成 -->
                </div>
            </div>

            <div class="background-actions">
                <button class="background-action-btn background-reset" onclick="resetBackground()">重置</button>
                <button class="background-action-btn background-apply" onclick="applyBackground()">应用</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化编辑器
        let editor;

        // 初始内容 - 使用简单的HTML格式
        const initialContent = `
            <h1>Tiptap.js 富文本编辑器示例</h1>
            <p>这是一个使用 Tiptap.js 实现的富文本编辑器示例。</p>
            <h2>主要功能：</h2>
            <ul>
                <li>文本格式化（粗体、斜体、删除线等）</li>
                <li>标题（一级标题、二级标题）</li>
                <li>列表（有序和无序）</li>
                <li>待办事项列表</li>
                <li>代码和引用块</li>
            </ul>
            <p><em>试试看各种编辑功能吧！</em></p>
            <h2>待办事项示例：</h2>
            <ul>
                <li>这是一个待办事项</li>
                <li>这是另一个待办事项</li>
                <li>点击工具栏中的待办事项按钮添加新的待办事项</li>
            </ul>
        `;

        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // 加载 Tiptap 库
                await window.loadTiptapLibs();

                // 检查库是否成功加载
                if (typeof tiptap === 'undefined') {
                    console.error('Tiptap库加载失败');
                    document.querySelector('#editor').innerHTML = '<div style="color: red; padding: 20px;">编辑器加载失败，请刷新页面重试</div>';
                    return;
                }

                // 延迟初始化，确保 DOM 已完全准备好
                setTimeout(() => {
            // 创建编辑器
            try {
                console.log('开始初始化编辑器');

                // 首先确保元素存在
                const editorElement = document.querySelector('#editor');
                if (!editorElement) {
                    console.error('编辑器元素不存在');
                    return;
                }

                // 先设置一个简单的初始内容，确保可以显示
                editorElement.innerHTML = '<p>正在加载编辑器...</p>';

                editor = new tiptap.Editor({
                element: document.querySelector('#editor'),
                extensions: [
                    tiptapStarterKit.StarterKit.configure({
                        // 启用历史记录功能，支持撤销/重做
                        history: {
                            depth: 100, // 历史记录深度
                            newGroupDelay: 500, // 新组延迟（毫秒）
                        }
                    }),
                    tiptapExtensionTaskList.TaskList,
                    tiptapExtensionTaskItem.TaskItem.configure({
                        nested: true,
                    }),
                ],
                content: initialContent,
                onUpdate: ({ editor }) => {
                    // 更新工具栏按钮状态
                    updateToolbar(editor);
                },
                // 添加快捷键支持
                editorProps: {
                    handleKeyDown: (view, event) => {
                        // 处理 Ctrl+Z 和 Ctrl+Y 快捷键
                        if (event.ctrlKey || event.metaKey) {
                            if (event.key === 'z' && !event.shiftKey) {
                                editor.commands.undo();
                                return true;
                            }
                            if ((event.key === 'y' || (event.key === 'z' && event.shiftKey))) {
                                editor.commands.redo();
                                return true;
                            }
                        }
                        return false;
                    }
                }
            });

                console.log('编辑器初始化成功');

                // 初始化待办事项的无障碍支持
                initTaskListAccessibility();
            } catch (error) {
                console.error('初始化编辑器时出错:', error);
                document.querySelector('#editor').innerHTML = `<div style="color: red; padding: 20px;">编辑器初始化失败: ${error.message}</div>`;
                return;
            }

            // 初始化工具栏
            initToolbar();

            // 添加录音控制栏事件监听
            document.getElementById('pause-recording').addEventListener('click', function() {
                if (isPaused) {
                    resumeVoiceRecording();
                } else {
                    pauseVoiceRecording();
                }
            });

            document.getElementById('stop-recording').addEventListener('click', function() {
                stopVoiceRecording();
            });

            document.getElementById('convert-to-text').addEventListener('click', function() {
                convertVoiceToText();
            });

            // 处理软键盘适配
            window.adjustEditorForKeyboard = (keyboardHeight) => {
                const editorContent = document.querySelector('.editor-content');
                if (editorContent) {
                    editorContent.style.paddingBottom = keyboardHeight + 'px';
                }
            };

            window.resetEditorLayout = () => {
                const editorContent = document.querySelector('.editor-content');
                if (editorContent) {
                    editorContent.style.paddingBottom = '0px';
                }
            };

            // 添加触摸事件处理，确保编辑器可以获取焦点
            const editorElement = document.querySelector('#editor');
            // 不再阻止默认触摸行为，允许正常的文本选择
            editorElement.addEventListener('click', () => {
                // 点击时聚焦
                editor.commands.focus();
            });

            // 监听输入法状态
            if (typeof Android !== 'undefined' && Android.onInputMethodVisibilityChanged) {
                window.onInputMethodVisibilityChanged = (visible) => {
                    if (visible) {
                        // 输入法显示时聚焦
                        setTimeout(() => {
                            editor.commands.focus();
                        }, 300);
                    }
                };
            }

            // 初始化时自动聚焦
            setTimeout(() => {
                editor.commands.focus();
            }, 300);
                }, 100);
            } catch (error) {
                console.error('初始化编辑器时出错:', error);
                document.querySelector('#editor').innerHTML = '<div style="color: red; padding: 20px;">编辑器初始化失败，请刷新页面重试</div>';
            }
        });

        // 初始化工具栏
        function initToolbar() {
            // 确保编辑器已初始化
            if (!editor) {
                console.error('编辑器未初始化');
                return;
            }
            const toolbar = document.querySelector('#editor-toolbar');

            toolbar.addEventListener('click', (e) => {
                const button = e.target.closest('button');
                if (!button) return;

                const action = button.dataset.action;

                switch (action) {
                    case 'undo':
                        editor.chain().focus().undo().run();
                        break;
                    case 'redo':
                        editor.chain().focus().redo().run();
                        break;
                    case 'back':
                        // 调用Android的返回方法
                        if (typeof Android !== 'undefined' && Android.goBack) {
                            Android.goBack();
                        } else {
                            // 如果在浏览器中测试，则显示提示
                            alert('返回功能在浏览器中不可用');
                        }
                        break;
                    case 'image':
                        // 调用Android的选择图片方法
                        if (typeof Android !== 'undefined' && Android.selectImage) {
                            Android.selectImage();
                        } else {
                            // 如果在浏览器中测试，则显示提示
                            alert('选择图片功能在浏览器中不可用');
                        }
                        break;
                    case 'voice':
                        // 调用录音功能
                        startVoiceRecording();
                        break;
                    case 'background':
                        // 调用背景选择功能
                        if (typeof Android !== 'undefined' && Android.selectBackground) {
                            Android.selectBackground();
                        } else {
                            // 如果在浏览器中测试，显示内置背景选项
                            showBackgroundOptions();
                        }
                        break;
                    case 'canvas-toggle':
                        // 切换画布模式
                        toggleCanvasMode();
                        break;
                    case 'bold':
                        editor.chain().focus().toggleBold().run();
                        break;
                    case 'italic':
                        editor.chain().focus().toggleItalic().run();
                        break;
                    case 'strike':
                        editor.chain().focus().toggleStrike().run();
                        break;
                    case 'heading-1':
                        editor.chain().focus().toggleHeading({ level: 1 }).run();
                        break;
                    case 'heading-2':
                        editor.chain().focus().toggleHeading({ level: 2 }).run();
                        break;
                    case 'bullet-list':
                        editor.chain().focus().toggleBulletList().run();
                        break;
                    case 'ordered-list':
                        editor.chain().focus().toggleOrderedList().run();
                        break;
                    case 'task-list':
                        editor.chain().focus().toggleTaskList().run();
                        break;
                    case 'code':
                        editor.chain().focus().toggleCodeBlock().run();
                        break;
                    case 'blockquote':
                        editor.chain().focus().toggleBlockquote().run();
                        break;
                    case 'clear':
                        editor.chain().focus().clearNodes().unsetAllMarks().run();
                        break;
                }

                updateToolbar(editor);
            });
        }

        // 更新工具栏按钮状态
        function updateToolbar(editor) {
            if (!editor) {
                console.error('编辑器未初始化');
                return;
            }
            const toolbar = document.querySelector('#editor-toolbar');
            const buttons = toolbar.querySelectorAll('button');

            buttons.forEach(button => {
                const action = button.dataset.action;

                switch (action) {
                    case 'undo':
                        // 禁用状态基于历史记录是否可用
                        button.disabled = !editor.can().undo();
                        break;
                    case 'redo':
                        button.disabled = !editor.can().redo();
                        break;
                    case 'bold':
                        button.classList.toggle('is-active', editor.isActive('bold'));
                        break;
                    case 'italic':
                        button.classList.toggle('is-active', editor.isActive('italic'));
                        break;
                    case 'strike':
                        button.classList.toggle('is-active', editor.isActive('strike'));
                        break;
                    case 'heading-1':
                        button.classList.toggle('is-active', editor.isActive('heading', { level: 1 }));
                        break;
                    case 'heading-2':
                        button.classList.toggle('is-active', editor.isActive('heading', { level: 2 }));
                        break;
                    case 'bullet-list':
                        button.classList.toggle('is-active', editor.isActive('bulletList'));
                        break;
                    case 'ordered-list':
                        button.classList.toggle('is-active', editor.isActive('orderedList'));
                        break;
                    case 'task-list':
                        button.classList.toggle('is-active', editor.isActive('taskList'));
                        break;
                    case 'code':
                        button.classList.toggle('is-active', editor.isActive('codeBlock'));
                        break;
                    case 'blockquote':
                        button.classList.toggle('is-active', editor.isActive('blockquote'));
                        break;
                }
            });
        }

        // 保存内容到 Android
        function saveContent() {
            if (!editor) {
                alert('编辑器未初始化');
                return;
            }
            const content = JSON.stringify(editor.getJSON());
            // 调用 Android 的接口保存内容
            if (typeof Android !== 'undefined') {
                Android.saveContent(content);
            } else {
                alert('内容已保存：\n' + content.substring(0, 100) + '...');
            }
        }

        // 获取 HTML 内容
        function getHtml() {
            if (!editor) {
                alert('编辑器未初始化');
                return;
            }
            const html = editor.getHTML();
            if (typeof Android !== 'undefined') {
                Android.getHtmlContent(html);
            } else {
                alert('HTML内容：\n' + html.substring(0, 100) + '...');
            }
        }

        // 获取纯文本内容
        function getText() {
            if (!editor) {
                alert('编辑器未初始化');
                return;
            }
            const text = editor.getText();
            if (typeof Android !== 'undefined') {
                Android.getTextContent(text);
            } else {
                alert('纯文本内容：\n' + text.substring(0, 100) + '...');
            }
        }

        // 插入图片
        function insertImage(imageUrl) {
            if (!editor) {
                alert('编辑器未初始化');
                return;
            }

            try {
                // 将光标放在当前位置
                editor.commands.focus();

                // 插入HTML图片标签
                const imgHtml = `<img src="${imageUrl}" alt="插入的图片" style="max-width: 100%; height: auto;">`;
                document.execCommand('insertHTML', false, imgHtml);

                console.log('图片插入成功:', imageUrl);
            } catch (error) {
                console.error('插入图片时出错:', error);
                alert('插入图片失败: ' + error.message);
            }
        }

        // 录音相关变量
        let isRecording = false;
        let isPaused = false;
        let recordingStartTime = 0;
        let recordingTimer = null;
        let elapsedTimeBeforePause = 0;
        let currentRecordingBar = null;

        // 开始录音
        function startVoiceRecording() {
            if (isRecording) return;

            // 调用Android的录音方法
            if (typeof Android !== 'undefined' && Android.startRecording) {
                try {
                    Android.startRecording();
                    showRecordingBar();
                    isRecording = true;
                    isPaused = false;
                    recordingStartTime = Date.now();
                    elapsedTimeBeforePause = 0;
                    startRecordingTimer();
                } catch (error) {
                    console.error('开始录音失败:', error);
                    alert('开始录音失败: ' + error.message);
                }
            } else {
                // 如果在浏览器中测试，则显示提示
                alert('录音功能在浏览器中不可用');
                // 模拟录音效果（仅用于浏览器测试）
                showRecordingBar();
                isRecording = true;
                isPaused = false;
                recordingStartTime = Date.now();
                elapsedTimeBeforePause = 0;
                startRecordingTimer();
            }
        }

        // 暂停录音
        function pauseVoiceRecording() {
            if (!isRecording || isPaused) return;

            if (typeof Android !== 'undefined' && Android.pauseRecording) {
                Android.pauseRecording();
            }

            isPaused = true;
            elapsedTimeBeforePause += Date.now() - recordingStartTime;
            clearInterval(recordingTimer);

            // 更新暂停按钮文本
            const pauseButton = document.querySelector('#voice-recording-bar #pause-recording');
            if (pauseButton) {
                pauseButton.textContent = '继续';
            }
        }

        // 继续录音
        function resumeVoiceRecording() {
            if (!isRecording || !isPaused) return;

            if (typeof Android !== 'undefined' && Android.resumeRecording) {
                Android.resumeRecording();
            }

            isPaused = false;
            recordingStartTime = Date.now();
            startRecordingTimer();

            // 更新暂停按钮文本
            const pauseButton = document.querySelector('#voice-recording-bar #pause-recording');
            if (pauseButton) {
                pauseButton.textContent = '暂停';
            }
        }

        // 停止录音
        function stopVoiceRecording() {
            if (!isRecording) return;

            if (typeof Android !== 'undefined' && Android.stopRecording) {
                Android.stopRecording();
            }

            isRecording = false;
            isPaused = false;
            clearInterval(recordingTimer);
            hideRecordingBar();
        }

        // 录音转文字
        function convertVoiceToText() {
            if (!isRecording) return;

            if (typeof Android !== 'undefined' && Android.convertVoiceToText) {
                Android.convertVoiceToText();
                stopVoiceRecording();
            } else {
                // 浏览器测试模式
                stopVoiceRecording();
                setTimeout(() => {
                    // 模拟录音文件URL和识别文本
                    const mockAudioUrl = 'file:///mock_audio_path.3gp';
                    const mockText = '这是一段模拟的语音转文字结果。在真实环境中，这里将显示语音识别的文字内容。';
                    const mockDuration = '00:15';
                    insertVoiceText(mockText, mockAudioUrl, mockDuration);
                }, 1000);
            }
        }

        // 插入语音转换的文字
        function insertVoiceText(text, audioUrl = '', duration = '00:00') {
            if (!editor) {
                alert('编辑器未初始化');
                return;
            }

            try {
                // 将光标放在当前位置
                editor.commands.focus();

                if (audioUrl) {
                    // 插入录音块
                    const voiceBlockHtml = `
                        <div class="voice-block" data-audio-url="${audioUrl}">
                            <div class="voice-block-icon"></div>
                            <div class="voice-block-content">
                                <div class="voice-block-duration">时长: ${duration}</div>
                                <div class="voice-block-controls">
                                    <button class="voice-block-play" onclick="playVoiceBlock(this)">播放</button>
                                    <span>语音录音</span>
                                </div>
                                ${text ? `<div class="voice-block-text">${text}</div>` : ''}
                            </div>
                        </div>
                    `;
                    document.execCommand('insertHTML', false, voiceBlockHtml);
                } else {
                    // 只插入文本
                    document.execCommand('insertText', false, text);
                }

                console.log('语音内容插入成功');
            } catch (error) {
                console.error('插入语音内容时出错:', error);
                alert('插入语音内容失败: ' + error.message);
            }
        }

        // 播放录音块
        function playVoiceBlock(button) {
            const voiceBlock = button.closest('.voice-block');
            const audioUrl = voiceBlock.getAttribute('data-audio-url');

            if (!audioUrl) {
                alert('找不到录音文件');
                return;
            }

            // 调用Android的播放方法
            if (typeof Android !== 'undefined' && Android.playAudio) {
                Android.playAudio(audioUrl);
                button.textContent = '正在播放...';
            } else {
                // 浏览器中测试
                alert('在浏览器中无法播放录音');
            }
        }

        // 更新播放按钮状态
        function updatePlayButtonState(audioUrl, isPlaying) {
            // 查找所有匹配的录音块
            const voiceBlocks = document.querySelectorAll(`.voice-block[data-audio-url="${audioUrl}"]`);

            voiceBlocks.forEach(block => {
                const playButton = block.querySelector('.voice-block-play');
                if (playButton) {
                    playButton.textContent = isPlaying ? '正在播放...' : '播放';
                }
            });
        }

        // 创建录音控制栏
        function createRecordingBar() {
            // 创建录音控制栏元素
            const recordingBar = document.createElement('div');
            recordingBar.id = 'voice-recording-bar';
            recordingBar.className = 'voice-recording-bar active';

            // 创建录音状态区
            const statusDiv = document.createElement('div');
            statusDiv.className = 'voice-recording-status';

            const indicator = document.createElement('div');
            indicator.className = 'recording-indicator';

            const timer = document.createElement('span');
            timer.className = 'recording-timer';
            timer.textContent = '00:00';

            const statusText = document.createElement('span');
            statusText.textContent = '正在录音...';

            statusDiv.appendChild(indicator);
            statusDiv.appendChild(timer);
            statusDiv.appendChild(statusText);

            // 创建控制按钮区
            const controlsDiv = document.createElement('div');
            controlsDiv.className = 'voice-recording-controls';

            const pauseButton = document.createElement('button');
            pauseButton.id = 'pause-recording';
            pauseButton.className = 'voice-control-button';
            pauseButton.textContent = '暂停';
            pauseButton.addEventListener('click', function() {
                if (isPaused) {
                    resumeVoiceRecording();
                } else {
                    pauseVoiceRecording();
                }
            });

            const stopButton = document.createElement('button');
            stopButton.id = 'stop-recording';
            stopButton.className = 'voice-control-button';
            stopButton.textContent = '停止';
            stopButton.addEventListener('click', function() {
                stopVoiceRecording();
            });

            const convertButton = document.createElement('button');
            convertButton.id = 'convert-to-text';
            convertButton.className = 'voice-control-button';
            convertButton.textContent = '转文字';
            convertButton.addEventListener('click', function() {
                convertVoiceToText();
            });

            controlsDiv.appendChild(pauseButton);
            controlsDiv.appendChild(stopButton);
            controlsDiv.appendChild(convertButton);

            // 组装录音控制栏
            recordingBar.appendChild(statusDiv);
            recordingBar.appendChild(controlsDiv);

            return recordingBar;
        }

        // 显示录音控制栏
        function showRecordingBar() {
            // 先聚焦编辑器，确保光标位置正确
            editor.commands.focus();

            // 创建录音控制栏
            const recordingBar = createRecordingBar();

            // 将录音控制栏插入到光标位置
            document.execCommand('insertHTML', false, recordingBar.outerHTML);

            // 重新获取元素并添加事件监听器
            const insertedBar = document.getElementById('voice-recording-bar');
            if (insertedBar) {
                const pauseButton = insertedBar.querySelector('#pause-recording');
                const stopButton = insertedBar.querySelector('#stop-recording');
                const convertButton = insertedBar.querySelector('#convert-to-text');

                pauseButton.addEventListener('click', function() {
                    if (isPaused) {
                        resumeVoiceRecording();
                    } else {
                        pauseVoiceRecording();
                    }
                });

                stopButton.addEventListener('click', function() {
                    stopVoiceRecording();
                });

                convertButton.addEventListener('click', function() {
                    convertVoiceToText();
                });
            }

            // 保存录音控制栏的引用
            currentRecordingBar = insertedBar;
        }

        // 隐藏录音控制栏
        function hideRecordingBar() {
            if (currentRecordingBar) {
                // 移除录音控制栏
                currentRecordingBar.remove();
                currentRecordingBar = null;
            }
        }

        // 开始录音计时器
        function startRecordingTimer() {
            recordingTimer = setInterval(() => {
                // 每次都重新获取元素，因为它可能已经被重新创建
                const timerElement = document.querySelector('#voice-recording-bar .recording-timer');
                if (!timerElement) {
                    clearInterval(recordingTimer);
                    return;
                }

                const currentTime = Date.now();
                const elapsedTime = elapsedTimeBeforePause + (currentTime - recordingStartTime);

                // 格式化时间（分:秒）
                const minutes = Math.floor(elapsedTime / 60000);
                const seconds = Math.floor((elapsedTime % 60000) / 1000);

                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        // 背景相关变量
        let selectedBackgroundImage = null;
        let selectedBackgroundColor = null;

        // 预设背景图片
        const backgroundImages = [
            { id: 'bg1', url: 'https://source.unsplash.com/random/800x600/?nature', thumbnail: 'https://source.unsplash.com/random/800x600/?nature' },
            { id: 'bg2', url: 'https://source.unsplash.com/random/800x600/?water', thumbnail: 'https://source.unsplash.com/random/800x600/?water' },
            { id: 'bg3', url: 'https://source.unsplash.com/random/800x600/?mountain', thumbnail: 'https://source.unsplash.com/random/800x600/?mountain' },
            { id: 'bg4', url: 'https://source.unsplash.com/random/800x600/?forest', thumbnail: 'https://source.unsplash.com/random/800x600/?forest' },
            { id: 'bg5', url: 'https://source.unsplash.com/random/800x600/?beach', thumbnail: 'https://source.unsplash.com/random/800x600/?beach' },
            { id: 'bg6', url: 'https://source.unsplash.com/random/800x600/?sky', thumbnail: 'https://source.unsplash.com/random/800x600/?sky' },
        ];

        // 预设背景颜色
        const backgroundColors = [
            { id: 'color1', color: '#ffffff', name: '白色' },
            { id: 'color2', color: '#f5f5f5', name: '浅灰' },
            { id: 'color3', color: '#e8f5e9', name: '浅绿' },
            { id: 'color4', color: '#e3f2fd', name: '浅蓝' },
            { id: 'color5', color: '#fff8e1', name: '浅黄' },
            { id: 'color6', color: '#ffebee', name: '浅红' },
            { id: 'color7', color: '#f3e5f5', name: '浅紫' },
            { id: 'color8', color: '#fce4ec', name: '浅粉' },
            { id: 'color9', color: '#fffde7', name: '浅黄' },
            { id: 'color10', color: '#e0f7fa', name: '浅青' },
        ];

        // 显示背景选项
        function showBackgroundOptions() {
            // 生成背景图片选项
            const bgImagesContainer = document.getElementById('background-images');
            bgImagesContainer.innerHTML = '';

            backgroundImages.forEach(bg => {
                const bgOption = document.createElement('div');
                bgOption.className = 'background-option';
                bgOption.id = bg.id;
                bgOption.style.backgroundImage = `url(${bg.thumbnail})`;
                bgOption.onclick = () => selectBackgroundImage(bg.id);
                bgImagesContainer.appendChild(bgOption);
            });

            // 生成颜色选项
            const bgColorsContainer = document.getElementById('background-colors');
            bgColorsContainer.innerHTML = '';

            backgroundColors.forEach(color => {
                const colorOption = document.createElement('div');
                colorOption.className = 'color-option';
                colorOption.id = color.id;
                colorOption.style.backgroundColor = color.color;
                colorOption.title = color.name;
                colorOption.onclick = () => selectBackgroundColor(color.id);
                bgColorsContainer.appendChild(colorOption);
            });

            // 显示选择器
            document.getElementById('background-selector').classList.add('active');
        }

        // 隐藏背景选项
        function hideBackgroundOptions() {
            document.getElementById('background-selector').classList.remove('active');
        }

        // 选择背景图片
        function selectBackgroundImage(id) {
            // 清除之前的选中状态
            document.querySelectorAll('.background-option.selected').forEach(el => {
                el.classList.remove('selected');
            });

            // 设置新的选中状态
            const selected = document.getElementById(id);
            if (selected) {
                selected.classList.add('selected');
                selectedBackgroundImage = backgroundImages.find(bg => bg.id === id);
                selectedBackgroundColor = null; // 清除颜色选择

                // 清除颜色选中状态
                document.querySelectorAll('.color-option.selected').forEach(el => {
                    el.classList.remove('selected');
                });
            }
        }

        // 选择背景颜色
        function selectBackgroundColor(id) {
            // 清除之前的选中状态
            document.querySelectorAll('.color-option.selected').forEach(el => {
                el.classList.remove('selected');
            });

            // 设置新的选中状态
            const selected = document.getElementById(id);
            if (selected) {
                selected.classList.add('selected');
                selectedBackgroundColor = backgroundColors.find(color => color.id === id);
                selectedBackgroundImage = null; // 清除图片选择

                // 清除图片选中状态
                document.querySelectorAll('.background-option.selected').forEach(el => {
                    el.classList.remove('selected');
                });
            }
        }

        // 应用背景
        function applyBackground() {
            const editorArea = document.getElementById('editor');

            if (selectedBackgroundImage) {
                // 应用背景图片
                editorArea.style.backgroundImage = `url(${selectedBackgroundImage.url})`;
                editorArea.style.backgroundColor = 'transparent';

                // 如果有Android接口，通知Android保存背景设置
                if (typeof Android !== 'undefined' && Android.saveBackgroundSetting) {
                    Android.saveBackgroundSetting('image', selectedBackgroundImage.url);
                }
            } else if (selectedBackgroundColor) {
                // 应用背景颜色
                editorArea.style.backgroundImage = 'none';
                editorArea.style.backgroundColor = selectedBackgroundColor.color;

                // 如果有Android接口，通知Android保存背景设置
                if (typeof Android !== 'undefined' && Android.saveBackgroundSetting) {
                    Android.saveBackgroundSetting('color', selectedBackgroundColor.color);
                }
            }

            hideBackgroundOptions();
        }

        // 重置背景
        function resetBackground() {
            const editorArea = document.getElementById('editor');
            editorArea.style.backgroundImage = 'none';
            editorArea.style.backgroundColor = '#fff';

            // 清除选中状态
            document.querySelectorAll('.background-option.selected, .color-option.selected').forEach(el => {
                el.classList.remove('selected');
            });

            selectedBackgroundImage = null;
            selectedBackgroundColor = null;

            // 如果有Android接口，通知Android重置背景设置
            if (typeof Android !== 'undefined' && Android.saveBackgroundSetting) {
                Android.saveBackgroundSetting('reset', '');
            }

            hideBackgroundOptions();
        }

        // 设置背景（由Android调用）
        function setBackground(type, value) {
            const editorArea = document.getElementById('editor');

            if (type === 'image') {
                editorArea.style.backgroundImage = `url(${value})`;
                editorArea.style.backgroundColor = 'transparent';
            } else if (type === 'color') {
                editorArea.style.backgroundImage = 'none';
                editorArea.style.backgroundColor = value;
            } else if (type === 'reset') {
                editorArea.style.backgroundImage = 'none';
                editorArea.style.backgroundColor = '#fff';
            }
        }

        // 无限画布相关变量
        let isCanvasMode = false;
        let scale = 1;
        let translateX = 0;
        let translateY = 0;
        let startX = 0;
        let startY = 0;
        let isDragging = false;

        // 切换画布模式
        function toggleCanvasMode() {
            const editorContainer = document.querySelector('.editor-container');
            const editorContent = document.getElementById('editor-content');
            const canvasControls = document.getElementById('canvas-controls');
            const editorArea = document.getElementById('editor');

            if (!isCanvasMode) {
                // 进入画布模式
                editorContainer.classList.add('canvas-mode');

                // 创建画布容器
                if (!document.querySelector('.canvas-container')) {
                    const canvasContainer = document.createElement('div');
                    canvasContainer.className = 'canvas-container';

                    const canvasWrapper = document.createElement('div');
                    canvasWrapper.className = 'canvas-wrapper';
                    canvasWrapper.id = 'canvas-wrapper';

                    // 移动编辑区域到画布容器中
                    canvasWrapper.appendChild(editorArea);
                    canvasContainer.appendChild(canvasWrapper);
                    editorContent.appendChild(canvasContainer);

                    // 初始化画布事件
                    initCanvasEvents();
                }

                // 显示画布控制按钮
                canvasControls.style.display = 'flex';

                // 更新按钮文本
                document.querySelector('[data-action="canvas-toggle"]').textContent = '退出画布';
            } else {
                // 退出画布模式
                editorContainer.classList.remove('canvas-mode');

                // 移动编辑区域回原位置
                const canvasWrapper = document.getElementById('canvas-wrapper');
                if (canvasWrapper && canvasWrapper.contains(editorArea)) {
                    editorContent.appendChild(editorArea);

                    // 移除画布容器
                    const canvasContainer = document.querySelector('.canvas-container');
                    if (canvasContainer) {
                        canvasContainer.remove();
                    }
                }

                // 隐藏画布控制按钮
                canvasControls.style.display = 'none';

                // 重置缩放和平移
                resetCanvasTransform();

                // 更新按钮文本
                document.querySelector('[data-action="canvas-toggle"]').textContent = '画布';
            }

            isCanvasMode = !isCanvasMode;
        }

        // 初始化画布事件
        function initCanvasEvents() {
            const canvasContainer = document.querySelector('.canvas-container');
            const canvasWrapper = document.getElementById('canvas-wrapper');

            // 添加鼠标事件
            canvasContainer.addEventListener('mousedown', startDrag);
            canvasContainer.addEventListener('mousemove', drag);
            canvasContainer.addEventListener('mouseup', endDrag);
            canvasContainer.addEventListener('mouseleave', endDrag);

            // 添加触摸事件
            canvasContainer.addEventListener('touchstart', handleTouchStart);
            canvasContainer.addEventListener('touchmove', handleTouchMove);
            canvasContainer.addEventListener('touchend', handleTouchEnd);

            // 添加滚轮缩放
            canvasContainer.addEventListener('wheel', handleWheel);

            // 添加控制按钮事件
            document.getElementById('zoom-in').addEventListener('click', zoomIn);
            document.getElementById('zoom-out').addEventListener('click', zoomOut);
            document.getElementById('zoom-reset').addEventListener('click', resetCanvasTransform);
        }

        // 开始拖动
        function startDrag(e) {
            if (e.target.closest('.ProseMirror') || e.target.closest('.canvas-controls')) {
                return; // 如果点击编辑器区域或控制按钮，不处理
            }

            isDragging = true;
            startX = e.clientX - translateX;
            startY = e.clientY - translateY;

            // 改变光标样式
            document.body.style.cursor = 'grabbing';
        }

        // 拖动
        function drag(e) {
            if (!isDragging) return;

            translateX = e.clientX - startX;
            translateY = e.clientY - startY;

            updateCanvasTransform();
        }

        // 结束拖动
        function endDrag() {
            isDragging = false;
            document.body.style.cursor = 'default';
        }

        // 处理触摸开始
        function handleTouchStart(e) {
            if (e.target.closest('.ProseMirror') || e.target.closest('.canvas-controls')) {
                return; // 如果点击编辑器区域或控制按钮，不处理
            }

            if (e.touches.length === 1) {
                // 单指拖动
                isDragging = true;
                startX = e.touches[0].clientX - translateX;
                startY = e.touches[0].clientY - translateY;
            }
        }

        // 处理触摸移动
        function handleTouchMove(e) {
            if (!isDragging) return;

            if (e.touches.length === 1) {
                // 单指拖动
                translateX = e.touches[0].clientX - startX;
                translateY = e.touches[0].clientY - startY;

                updateCanvasTransform();
                e.preventDefault(); // 防止页面滚动
            }
        }

        // 处理触摸结束
        function handleTouchEnd() {
            isDragging = false;
        }

        // 处理滚轮缩放
        function handleWheel(e) {
            if (!isCanvasMode) return;

            e.preventDefault();

            // 计算缩放因子
            const delta = e.deltaY < 0 ? 1.1 : 0.9;

            // 计算鼠标相对于画布的位置
            const rect = document.querySelector('.canvas-container').getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            // 计算新的缩放和平移
            const newScale = scale * delta;

            // 限制缩放范围
            if (newScale >= 0.2 && newScale <= 3) {
                // 计算新的平移，使鼠标位置保持不变
                translateX = mouseX - (mouseX - translateX) * delta;
                translateY = mouseY - (mouseY - translateY) * delta;

                scale = newScale;
                updateCanvasTransform();
            }
        }

        // 放大
        function zoomIn() {
            if (scale < 3) {
                scale *= 1.2;
                updateCanvasTransform();
            }
        }

        // 缩小
        function zoomOut() {
            if (scale > 0.2) {
                scale /= 1.2;
                updateCanvasTransform();
            }
        }

        // 更新画布变换
        function updateCanvasTransform() {
            const canvasWrapper = document.getElementById('canvas-wrapper');
            if (canvasWrapper) {
                canvasWrapper.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`;
            }
        }

        // 重置画布变换
        function resetCanvasTransform() {
            scale = 1;
            translateX = 0;
            translateY = 0;
            updateCanvasTransform();
        }

        // 初始化待办事项的无障碍支持
        function initTaskListAccessibility() {
            // 监听 DOM 变化
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' || mutation.type === 'attributes') {
                        setupTaskListAccessibility();
                    }
                });
            });

            // 开始监听编辑器内容区域
            const editorContent = document.querySelector('#editor');
            if (editorContent) {
                observer.observe(editorContent, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeFilter: ['data-checked']
                });
            }

            // 初始设置
            setupTaskListAccessibility();
        }

        // 设置待办事项的无障碍支持
        function setupTaskListAccessibility() {
            // 设置待办事项列表
            const taskLists = document.querySelectorAll('.ProseMirror ul[data-type="taskList"]');
            taskLists.forEach(list => {
                list.setAttribute('role', 'list');
                list.setAttribute('aria-label', '待办事项列表');
            });

            // 设置待办事项
            const taskItems = document.querySelectorAll('.ProseMirror li[data-type="taskItem"]');
            taskItems.forEach(item => {
                item.setAttribute('role', 'listitem');
                item.setAttribute('aria-roledescription', '待办事项');

                // 获取复选框状态
                const isChecked = item.getAttribute('data-checked') === 'true';

                // 查找或创建复选框
                let checkbox = item.querySelector('input[type="checkbox"]');
                if (!checkbox) {
                    // 如果没有复选框，创建一个
                    checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.className = 'task-checkbox';

                    // 将复选框插入到待办事项的开头
                    if (item.firstChild) {
                        item.insertBefore(checkbox, item.firstChild);
                    } else {
                        item.appendChild(checkbox);
                    }
                }

                // 设置复选框状态
                checkbox.checked = isChecked;

                // 添加无障碍属性
                checkbox.setAttribute('role', 'checkbox');
                checkbox.setAttribute('aria-checked', isChecked ? 'true' : 'false');
                checkbox.setAttribute('tabindex', '0');
                checkbox.setAttribute('aria-label', isChecked ? '已完成的待办事项' : '未完成的待办事项');

                // 添加事件监听器
                if (!checkbox.hasAttribute('data-accessibility-initialized')) {
                    checkbox.setAttribute('data-accessibility-initialized', 'true');

                    // 点击事件
                    checkbox.addEventListener('click', (e) => {
                        // 防止事件冲突
                        e.stopPropagation();

                        // 切换待办事项状态
                        toggleTaskItemState(item, !isChecked);

                        // 播报状态变化
                        announceTaskStateChange(isChecked ? '待办事项标记为未完成' : '待办事项标记为已完成');
                    });

                    // 键盘事件
                    checkbox.addEventListener('keydown', (e) => {
                        if (e.key === ' ' || e.key === 'Enter') {
                            e.preventDefault();

                            // 切换待办事项状态
                            toggleTaskItemState(item, !isChecked);

                            // 播报状态变化
                            announceTaskStateChange(isChecked ? '待办事项标记为未完成' : '待办事项标记为已完成');
                        }
                    });
                }
            });
        }

        // 切换待办事项状态
        function toggleTaskItemState(item, checked) {
            // 更新待办事项的数据属性
            item.setAttribute('data-checked', checked ? 'true' : 'false');

            // 更新复选框状态
            const checkbox = item.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.checked = checked;
                checkbox.setAttribute('aria-checked', checked ? 'true' : 'false');
                checkbox.setAttribute('aria-label', checked ? '已完成的待办事项' : '未完成的待办事项');
            }

            // 通知编辑器状态变化
            if (editor) {
                // 找到节点在编辑器中的位置
                const { state } = editor.view;
                const { doc } = state;
                let nodePos = null;

                doc.descendants((node, pos) => {
                    if (nodePos !== null) return false; // 已找到，停止遍历

                    if (node.type.name === 'taskItem') {
                        // 比较DOM元素和编辑器节点
                        const domNode = editor.view.nodeDOM(pos);
                        if (domNode === item || domNode.contains(item)) {
                            nodePos = pos;
                            return false; // 停止遍历
                        }
                    }
                });

                if (nodePos !== null) {
                    // 更新编辑器状态
                    const { tr } = state;
                    tr.setNodeMarkup(nodePos, null, { checked });
                    editor.view.dispatch(tr);
                }
            }
        }

        // 播报状态变化（用于屏幕阅读器）
        function announceTaskStateChange(message) {
            // 创建或获取播报区域
            let announcer = document.getElementById('accessibility-announcer');
            if (!announcer) {
                announcer = document.createElement('div');
                announcer.id = 'accessibility-announcer';
                announcer.setAttribute('aria-live', 'polite');
                announcer.setAttribute('aria-atomic', 'true');
                announcer.style.position = 'absolute';
                announcer.style.width = '1px';
                announcer.style.height = '1px';
                announcer.style.overflow = 'hidden';
                announcer.style.clip = 'rect(0, 0, 0, 0)';
                document.body.appendChild(announcer);
            }

            // 设置消息
            announcer.textContent = message;

            // 如果有Android接口，也通知Android端
            if (typeof Android !== 'undefined' && Android.announceForAccessibility) {
                Android.announceForAccessibility(message);
            }
        }

        // 清除已完成的待办事项
        function clearCompletedTodos() {
            if (!editor) {
                alert('编辑器未初始化');
                return;
            }
            const { state, dispatch } = editor.view;
            const { doc, tr } = state;
            const completedTasks = [];

            // 查找所有已完成的待办事项
            doc.descendants((node, pos) => {
                if (node.type.name === 'taskItem' && node.attrs.checked) {
                    completedTasks.push({ node, pos });
                }
            });

            if (completedTasks.length === 0) {
                alert('没有已完成的待办事项');
                return;
            }

            // 从后向前删除，避免位置变化
            completedTasks.reverse().forEach(({ pos, node }) => {
                tr.delete(pos, pos + node.nodeSize);
            });

            dispatch(tr);

            // 播报清除结果
            const message = `已清除 ${completedTasks.length} 个已完成的待办事项`;
            alert(message);
            announceTaskStateChange(message);
        }
    </script>
</body>
</html>
