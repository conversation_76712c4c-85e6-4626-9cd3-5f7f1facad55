<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, viewport-fit=cover">
    <title>简单输入框示例</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            height: 100%;
            width: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, sans-serif;
        }

        body {
            display: flex;
            flex-direction: column;
            background-color: #f5f5f5;
            padding: 16px;
        }

        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .status {
            background-color: #4285f4;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            margin-bottom: 16px;
            text-align: center;
            font-weight: bold;
        }

        .input-area {
            display: flex;
            flex-direction: column;
            flex: 1;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: padding-bottom 0.3s ease;
        }

        textarea {
            flex: 1;
            padding: 16px;
            border: none;
            outline: none;
            font-size: 16px;
            line-height: 1.5;
            resize: none;
            min-height: 300px;
            -webkit-user-select: text;
            user-select: text;
        }

        .bottom-content {
            padding: 16px;
            background-color: #f9f9f9;
            border-top: 1px solid #eee;
        }

        .bottom-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            margin-bottom: 16px;
            -webkit-user-select: text;
            user-select: text;
        }

        .submit-btn {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
        }

        .keyboard-fix {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }

        /* 软键盘适配样式 */
        .keyboard-open .input-area {
            padding-bottom: 0; /* 会被动态设置 */
        }
    </style>
</head>
<body>
    <h1>软键盘遮挡测试</h1>

    <div class="status" id="keyboard-status">键盘状态: 隐藏</div>

    <div class="input-area" id="input-area">
        <label for="main-textarea"></label><textarea id="main-textarea" placeholder="请在这里输入大量文本，然后滚动到底部..." contenteditable="true">这是一个测试软键盘遮挡问题的简单示例。

请尝试输入大量文本，然后滚动到底部。

当你点击底部的输入框时，观察软键盘是否会遮挡输入内容。

以下是一些填充内容，帮助你快速测试：

1. 这是填充文本
2. 这是填充文本
3. 这是填充文本
4. 这是填充文本
5. 这是填充文本
6. 这是填充文本
7. 这是填充文本
8. 这是填充文本
9. 这是填充文本
10. 这是填充文本
11. 这是填充文本
12. 这是填充文本
13. 这是填充文本
14. 这是填充文本
15. 这是填充文本
16. 这是填充文本
17. 这是填充文本
18. 这是填充文本
19. 这是填充文本
20. 这是填充文本

请滚动到底部，然后点击下方的输入框。</textarea>

    </div>

    <div class="keyboard-fix" id="keyboard-fix">
        键盘修复: <span id="fix-status">关闭</span>
    </div>

    <script>
        // 获取DOM元素
        const inputArea = document.getElementById('input-area');
        const bottomInput = document.getElementById('main-textarea');
<!--        const bottomInput = document.getElementById('bottom-input');-->
        const keyboardStatus = document.getElementById('keyboard-status');
        const keyboardFix = document.getElementById('keyboard-fix');
        const fixStatus = document.getElementById('fix-status');
        const submitBtn = document.getElementById('submit-btn');

        // 检查软键盘修复是否启用
        function isKeyboardFixEnabled() {
            if (typeof Android !== 'undefined' && Android.isKeyboardFixEnabled) {
                return Android.isKeyboardFixEnabled();
            }
            return false;
        }

        // 更新键盘修复状态
        function updateKeyboardFixStatus() {
            const isEnabled = isKeyboardFixEnabled();
            fixStatus.textContent = isEnabled ? '开启' : '关闭';
            keyboardFix.style.backgroundColor = isEnabled ? 'rgba(76, 175, 80, 0.8)' : 'rgba(244, 67, 54, 0.8)';
        }

        // 处理软键盘适配
        function adjustForKeyboard(keyboardHeight) {
            const isEnabled = isKeyboardFixEnabled();

            if (keyboardHeight > 0) {
                // 软键盘打开
                keyboardStatus.textContent = `键盘状态: 显示 (${keyboardHeight}px)`;
                keyboardStatus.style.backgroundColor = '#f44336';

                if (isEnabled) {
                    // 启用修复
                    document.body.classList.add('keyboard-open');
                    inputArea.style.paddingBottom = `${keyboardHeight}px`;

                    // 如果底部输入框有焦点，确保它可见
                    if (document.activeElement === mainTextarea) {
                        setTimeout(() => {
                            mainTextarea.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 300);
                    }
                }
            } else {
                // 软键盘关闭
                keyboardStatus.textContent = '键盘状态: 隐藏';
                keyboardStatus.style.backgroundColor = '#4285f4';
                document.body.classList.remove('keyboard-open');
                inputArea.style.paddingBottom = '0';
            }
        }

        // 监听视口大小变化，自动检测软键盘
        const originalHeight = window.innerHeight;
        window.addEventListener('resize', function() {
            // 如果高度明显减小，可能是软键盘打开
            const heightDiff = originalHeight - window.innerHeight;
            if (heightDiff > 100) { // 通常软键盘高度大于100px
                adjustForKeyboard(heightDiff);
            } else {
                adjustForKeyboard(0);
            }

            // 更新键盘修复状态
            updateKeyboardFixStatus();
        });

        // 监听输入框焦点
        bottomInput.addEventListener('focus', function() {
            // 延迟执行，确保软键盘已经打开
            setTimeout(() => {
                const heightDiff = originalHeight - window.innerHeight;
                if (heightDiff > 100) {
                    adjustForKeyboard(heightDiff);
                }
            }, 300);
        });

        // 监听主文本区域焦点
        mainTextarea.addEventListener('focus', function() {
            // 延迟执行，确保软键盘已经打开
            setTimeout(() => {
                const heightDiff = originalHeight - window.innerHeight;
                if (heightDiff > 100) {
                    adjustForKeyboard(heightDiff);
                }
            }, 300);
        });

        // 提交按钮点击事件
        submitBtn.addEventListener('click', function() {
            alert('提交内容: ' + bottomInput.value);
            bottomInput.value = '';
        });

        // 从Android调用的方法
        window.adjustEditorForKeyboard = function(keyboardHeight) {
            adjustForKeyboard(keyboardHeight);
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateKeyboardFixStatus();

            // 通知Android页面已加载
            if (typeof Android !== 'undefined' && Android.logMessage) {
                Android.logMessage('页面已加载完成');
            }

            // 滚动到底部
            mainTextarea.scrollTop = mainTextarea.scrollHeight;

            // 确保输入框可编辑
            mainTextarea.readOnly = false;
            bottomInput.readOnly = false;
        });

        // 定期检查键盘修复状态
        setInterval(updateKeyboardFixStatus, 1000);
    </script>
</body>
</html>
