// Slate.js 和 Slate-React 的简化本地备份
// 这是一个最小化实现，仅提供基本功能

// 定义全局命名空间
window.Slate = window.Slate || {};
window.SlateReact = window.SlateReact || {};

// Slate 核心功能
(function(Slate) {
  // 创建编辑器
  Slate.createEditor = function() {
    return {
      children: [],
      selection: null,
      operations: [],
      marks: {},
      isInline: () => false,
      isVoid: () => false,
      normalizeNode: () => {},
      onChange: () => {},
      apply: () => {}
    };
  };
  
  // 编辑器操作
  Slate.Editor = {
    nodes: function(editor, options) {
      return [null, []];
    },
    marks: function(editor) {
      return editor.marks || {};
    },
    addMark: function(editor, key, value) {
      editor.marks = editor.marks || {};
      editor.marks[key] = value;
    },
    removeMark: function(editor, key) {
      editor.marks = editor.marks || {};
      delete editor.marks[key];
    },
    isEditor: function(value) {
      return value && typeof value.apply === 'function';
    }
  };
  
  // 转换操作
  Slate.Transforms = {
    select: function(editor, target) {},
    setNodes: function(editor, props, options) {},
    insertNodes: function(editor, nodes, options) {},
    removeNodes: function(editor, options) {},
    unwrapNodes: function(editor, options) {},
    wrapNodes: function(editor, element, options) {}
  };
  
  // 元素操作
  Slate.Element = {
    isElement: function(value) {
      return value && value.type && Array.isArray(value.children);
    }
  };
  
  // 文本操作
  Slate.Text = {
    isText: function(value) {
      return value && typeof value.text === 'string';
    }
  };
  
  // 节点操作
  Slate.Node = {
    string: function(node) {
      if (Slate.Text.isText(node)) {
        return node.text;
      } else {
        return node.children.map(Slate.Node.string).join('');
      }
    }
  };
})(window.Slate);

// Slate-React 功能
(function(SlateReact, Slate) {
  // React 集成
  SlateReact.withReact = function(editor) {
    return editor;
  };
  
  // Slate 上下文
  SlateReact.Slate = function(props) {
    return props.children;
  };
  
  // 可编辑组件
  SlateReact.Editable = function(props) {
    return React.createElement('div', {
      contentEditable: true,
      className: props.className,
      style: { minHeight: '200px', padding: '16px' }
    });
  };
  
  // 使用 Slate 上下文
  SlateReact.useSlate = function() {
    return window.slateEditor || Slate.createEditor();
  };
  
  // React 编辑器
  SlateReact.ReactEditor = {
    findPath: function(editor, element) {
      return [0];
    },
    focus: function(editor) {
      const el = document.querySelector('[contenteditable=true]');
      if (el) el.focus();
    }
  };
})(window.SlateReact, window.Slate);

// 添加一个初始化完成的标志
window.slateLoaded = true;
console.log('Slate本地库已加载');
