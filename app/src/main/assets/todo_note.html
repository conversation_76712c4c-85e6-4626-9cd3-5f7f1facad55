<!DOCTYPE html>
<html lang="en" encoding="utf-8">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <style>
        html, body {
            font-family: system-ui, -apple-system, sans-serif;
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }
        body {
            display: flex;
            flex-direction: column;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        .note-container {
            display: flex;
            flex-direction: column;
            flex: 1;
            height: 100%;
            background-color: white;
            overflow: hidden;
        }
        .note-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }
        .note-title {
            font-size: 20px;
            font-weight: 500;
            color: #333;
        }
        .note-editor {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            -webkit-user-select: text;
            user-select: text;
            line-height: 1.6;
            font-size: 18px;
            height: auto;
            min-height: 300px;
        }
        .note-actions {
            display: flex;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-top: 1px solid #e0e0e0;
            flex-shrink: 0;
        }
        .note-btn {
            background-color: #4285f4;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            margin-right: 12px;
            font-weight: 500;
            color: white;
            font-size: 16px;
        }
        .note-btn:active {
            background-color: #3367d6;
        }
        .checkbox-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        .checkbox {
            margin-right: 12px;
            margin-top: 4px;
            width: 20px;
            height: 20px;
        }
        .checkbox-label {
            flex: 1;
            font-size: 18px;
            padding: 4px 0;
        }
    </style>
    <title></title>
</head>
<body>
    <div class="note-container">
        <div class="note-header">
            <div class="note-title">待办事项清单</div>
        </div>

        <div id="note-editor" class="note-editor" contenteditable="true">
            <div class="checkbox-item">
                <input type="checkbox" class="checkbox" id="item1">
                <label class="checkbox-label" for="item1">完成项目报告</label>
            </div>
            <div class="checkbox-item">
                <input type="checkbox" class="checkbox" id="item2">
                <label class="checkbox-label" for="item2">准备会议材料</label>
            </div>
            <div class="checkbox-item">
                <input type="checkbox" class="checkbox" id="item3">
                <label class="checkbox-label" for="item3">回复重要邮件</label>
            </div>
            <div class="checkbox-item">
                <input type="checkbox" class="checkbox" id="item4">
                <label class="checkbox-label" for="item4">安排团队建设活动</label>
            </div>
            <div class="checkbox-item">
                <input type="checkbox" class="checkbox" id="item5">
                <label class="checkbox-label" for="item5">复习本周学习内容</label>
            </div>
            <div class="checkbox-item">
                <input type="checkbox" class="checkbox" id="item6">
                <label class="checkbox-label" for="item6">预约下周医生咨询</label>
            </div>
        </div>

        <div class="note-actions">
            <button class="note-btn" onclick="selectAllText()">全选</button>
            <button class="note-btn" onclick="copyText()">复制</button>
        </div>
    </div>

    <script>
        // 全选文本
        function selectAllText() {
            const editor = document.getElementById('note-editor');
            const range = document.createRange();
            range.selectNodeContents(editor);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        }

        // 复制文本
        function copyText() {
            selectAllText();
            document.execCommand('copy');
            Android.notifySelectionComplete('已复制全部内容');
        }

        // 初始化页面
        function initPage() {
            // 为复选框添加点击事件
            document.querySelectorAll('.checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const label = this.nextElementSibling;
                    if (this.checked) {
                        label.style.textDecoration = 'line-through';
                        label.style.color = '#888';
                    } else {
                        label.style.textDecoration = 'none';
                        label.style.color = '#333';
                    }
                });
            });

            // 添加新建待办事项的功能
            document.getElementById('note-editor').addEventListener('keydown', function(e) {
                // 如果在最后一个待办事项上按回车键
                if (e.key === 'Enter' && !e.shiftKey) {
                    const selection = window.getSelection();
                    const range = selection.getRangeAt(0);
                    const node = range.startContainer;

                    // 检查是否在标签内
                    let isInLabel = false;
                    let currentNode = node;
                    while (currentNode && currentNode !== document.body) {
                        if (currentNode.classList && currentNode.classList.contains('checkbox-label')) {
                            isInLabel = true;
                            break;
                        }
                        currentNode = currentNode.parentNode;
                    }

                    if (isInLabel) {
                        e.preventDefault(); // 阻止默认行为

                        // 创建新的待办事项
                        const newId = 'item' + (document.querySelectorAll('.checkbox-item').length + 1);
                        const newItem = document.createElement('div');
                        newItem.className = 'checkbox-item';
                        newItem.innerHTML = `
                            <input type="checkbox" class="checkbox" id="${newId}">
                            <label class="checkbox-label" for="${newId}">新待办事项</label>
                        `;

                        // 将新项目添加到编辑器
                        document.getElementById('note-editor').appendChild(newItem);

                        // 为新的复选框添加事件
                        const newCheckbox = newItem.querySelector('.checkbox');
                        newCheckbox.addEventListener('change', function() {
                            const label = this.nextElementSibling;
                            if (this.checked) {
                                label.style.textDecoration = 'line-through';
                                label.style.color = '#888';
                            } else {
                                label.style.textDecoration = 'none';
                                label.style.color = '#333';
                            }
                        });

                        // 将光标移动到新标签
                        const newLabel = newItem.querySelector('.checkbox-label');
                        const range = document.createRange();
                        range.setStart(newLabel, 0);
                        range.collapse(true);
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                }
            });
        }

        // 页面加载完成后初始化
        window.onload = function() {
            initPage();

            // 调整编辑区域高度
            function adjustEditorHeight() {
                const container = document.querySelector('.note-container');
                const header = document.querySelector('.note-header');
                const actions = document.querySelector('.note-actions');
                const editor = document.querySelector('.note-editor');

                if (container && header && actions && editor) {
                    const containerHeight = container.offsetHeight;
                    const headerHeight = header.offsetHeight;
                    const actionsHeight = actions.offsetHeight;
                    const editorHeight = containerHeight - headerHeight - actionsHeight;

                    editor.style.height = editorHeight + 'px';
                    editor.style.minHeight = editorHeight + 'px';
                    editor.style.maxHeight = editorHeight + 'px';
                }
            }

            // 初始调整高度
            adjustEditorHeight();

            // 当窗口大小变化时重新调整
            window.addEventListener('resize', adjustEditorHeight);
        };
    </script>
</body>
</html>
