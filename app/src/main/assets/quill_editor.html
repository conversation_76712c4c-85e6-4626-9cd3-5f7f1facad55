<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Quill.js 富文本编辑器</title>
    
    <!-- 引入 Quill.js 样式 -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    
    <style>
        html, body {
            font-family: system-ui, -apple-system, sans-serif;
            padding: 0;
            margin: 0;
            background-color: #f00;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }
        
        body {
            display: flex;
            flex-direction: column;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        
        .editor-container {
            display: flex;
            flex-direction: column;
            flex: 1;
            height: 100%;
            background-color: white;
            overflow: hidden;
        }
        
        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }
        
        .editor-title {
            font-size: 20px;
            font-weight: 500;
            color: #333;
        }
        
        #editor {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            height: auto;
            min-height: 300px;
            font-size: 16px;
            line-height: 1.5;
            background:#f00;
        }
        
        .ql-toolbar.ql-snow {
            border-top: none;
            border-left: none;
            border-right: none;
            border-bottom: 1px solid #e0e0e0;
            padding: 8px;
            background-color: #f9f9f9;
        }
        
        .ql-container.ql-snow {
            border: none;
            font-size: 16px;
        }
        
        .editor-actions {
            display: flex;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-top: 1px solid #e0e0e0;
            flex-shrink: 0;
        }
        
        .editor-btn {
            background-color: #4285f4;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            margin-right: 12px;
            font-weight: 500;
            color: white;
            font-size: 16px;
        }
        
        .editor-btn:active {
            background-color: #3367d6;
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <div class="editor-header">
            <div class="editor-title">Quill.js 富文本编辑器</div>
        </div>
        
        <!-- 创建工具栏容器 -->
        <div id="toolbar">
            <!-- 字体格式 -->
            <span class="ql-formats">
                <select class="ql-font"></select>
                <select class="ql-size"></select>
            </span>
            
            <!-- 文本格式 -->
            <span class="ql-formats">
                <button class="ql-bold"></button>
                <button class="ql-italic"></button>
                <button class="ql-underline"></button>
                <button class="ql-strike"></button>
            </span>
            
            <!-- 颜色选择 -->
            <span class="ql-formats">
                <select class="ql-color"></select>
                <select class="ql-background"></select>
            </span>
            
            <!-- 列表 -->
            <span class="ql-formats">
                <button class="ql-list" value="ordered"></button>
                <button class="ql-list" value="bullet"></button>
            </span>
            
            <!-- 对齐方式 -->
            <span class="ql-formats">
                <button class="ql-align" value=""></button>
                <button class="ql-align" value="center"></button>
                <button class="ql-align" value="right"></button>
                <button class="ql-align" value="justify"></button>
            </span>
            
            <!-- 链接和图片 -->
            <span class="ql-formats">
                <button class="ql-link"></button>
                <button class="ql-image"></button>
            </span>
            
            <!-- 清除格式 -->
            <span class="ql-formats">
                <button class="ql-clean"></button>
            </span>
        </div>
        
        <!-- 创建编辑器容器 -->
        <div id="editor"></div>
        
        <div class="editor-actions">
            <button class="editor-btn" onclick="saveContent()">保存内容</button>
            <button class="editor-btn" onclick="getHtml()">获取HTML</button>
            <button class="editor-btn" onclick="getText()">获取纯文本</button>
        </div>
    </div>
    
    <!-- 引入 Quill.js 脚本 -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    
    <script>
        // 初始化 Quill 编辑器
        var quill = new Quill('#editor', {
            theme: 'snow',
            modules: {
                toolbar: '#toolbar'
            },
            placeholder: '开始编辑内容...'
        });
        
        // 设置初始内容
        quill.setContents([
            { insert: 'Quill.js 富文本编辑器示例\n', attributes: { bold: true, size: 'large' } },
            { insert: '这是一个使用 Quill.js 实现的富文本编辑器示例。\n\n' },
            { insert: '主要功能：\n', attributes: { bold: true } },
            { insert: '• 文本格式化（粗体、斜体、下划线等）\n' },
            { insert: '• 字体和字号选择\n' },
            { insert: '• 文本颜色和背景色\n' },
            { insert: '• 列表（有序和无序）\n' },
            { insert: '• 文本对齐\n' },
            { insert: '• 插入链接和图片\n\n' },
            { insert: '试试看各种编辑功能吧！\n', attributes: { color: '#2196f3', italic: true } }
        ]);
        
        // 保存内容到 Android
        function saveContent() {
            var content = JSON.stringify(quill.getContents());
            // 调用 Android 的接口保存内容
            if (typeof Android !== 'undefined') {
                Android.saveContent(content);
            }
        }
        
        // 获取 HTML 内容
        function getHtml() {
            var html = quill.root.innerHTML;
            if (typeof Android !== 'undefined') {
                Android.getHtmlContent(html);
            }
        }
        
        // 获取纯文本内容
        function getText() {
            var text = quill.getText();
            if (typeof Android !== 'undefined') {
                Android.getTextContent(text);
            }
        }
        
        // 调整编辑器高度
        function adjustEditorHeight() {
            const container = document.querySelector('.editor-container');
            const header = document.querySelector('.editor-header');
            const toolbar = document.querySelector('.ql-toolbar');
            const actions = document.querySelector('.editor-actions');
            const editor = document.querySelector('.ql-container');
            
            if (container && header && toolbar && actions && editor) {
                const containerHeight = container.offsetHeight;
                const headerHeight = header.offsetHeight;
                const toolbarHeight = toolbar.offsetHeight;
                const actionsHeight = actions.offsetHeight;
                const editorHeight = containerHeight - headerHeight - toolbarHeight - actionsHeight;
                
                editor.style.height = editorHeight + 'px';
            }
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            // 初始调整高度
            adjustEditorHeight();
            
            // 当窗口大小变化时重新调整
            window.addEventListener('resize', adjustEditorHeight);
        };
    </script>
</body>
</html>
