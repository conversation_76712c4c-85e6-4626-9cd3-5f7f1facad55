<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TinyMCE 富文本编辑器</title>

    <style>
        html, body {
            font-family: system-ui, -apple-system, sans-serif;
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        body {
            display: flex;
            flex-direction: column;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .editor-container {
            display: flex;
            flex-direction: column;
            flex: 1;
            height: 100%;
            background-color: white;
            overflow: hidden;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .editor-title {
            font-size: 20px;
            font-weight: 500;
            color: #333;
        }

        .editor-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 0;
            margin: 0;
        }

        .editor-actions {
            display: flex;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-top: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .editor-btn {
            background-color: #4285f4;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            margin-right: 12px;
            font-weight: 500;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }

        .editor-btn:active {
            background-color: #3367d6;
        }

        /* TinyMCE 自定义样式 */
        .tox-tinymce {
            border: none !important;
            border-radius: 0 !important;
        }

        .tox-statusbar {
            display: none !important;
        }
    </style>

    <!-- 引入 TinyMCE 脚本 - 使用多个免费CDN源，增加可靠性 -->
    <script>
        // 尝试加载 TinyMCE
        function loadTinyMCE() {
            // 检查是否已经加载
            if (typeof tinymce !== 'undefined') {
                return;
            }

            // CDN源列表
            var sources = [
                'https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.7.0/tinymce.min.js',
                'https://cdn.jsdelivr.net/npm/tinymce@6.7.0/tinymce.min.js',
                'https://unpkg.com/tinymce@6.7.0/tinymce.min.js'
            ];

            // 加载脚本
            loadScript(sources, 0);
        }

        // 递归尝试加载脚本
        function loadScript(sources, index) {
            if (index >= sources.length) {
                console.error('所有 TinyMCE 源加载失败');
                document.getElementById('editor-content').innerHTML = '<div style="padding: 20px; color: red;">加载编辑器失败。请检查网络连接并刷新页面。</div>';
                return;
            }

            var script = document.createElement('script');
            script.src = sources[index];
            script.referrerPolicy = 'no-referrer';
            script.onload = function() {
                console.log('TinyMCE 加载成功: ' + sources[index]);
                initTinyMCE(); // 初始化编辑器
            };
            script.onerror = function() {
                console.warn('TinyMCE 加载失败: ' + sources[index]);
                loadScript(sources, index + 1); // 尝试下一个源
            };
            document.head.appendChild(script);
        }

        // 页面加载完成后加载 TinyMCE
        window.onload = function() {
            loadTinyMCE();
        };
    </script>
</head>
<body>
    <div class="editor-container">
        <div class="editor-header">
            <div class="editor-title">TinyMCE 富文本编辑器</div>
        </div>

        <div class="editor-content" id="editor-content">
            <!-- TinyMCE 编辑器容器 -->
            <textarea id="editor"></textarea>
        </div>

<!--        <div class="editor-actions">-->
<!--            <button class="editor-btn" onclick="saveContent()">保存内容</button>-->
<!--            <button class="editor-btn" onclick="getHtml()">获取HTML</button>-->
<!--            <button class="editor-btn" onclick="getText()">获取纯文本</button>-->
<!--            <button class="editor-btn" onclick="clearCompletedTodos()" style="background-color: #ff5722;">清除已完成事项</button>-->
<!--        </div>-->
    </div>

    <script>
        // 初始化 TinyMCE 编辑器函数 - 简化版
        function initTinyMCE() {
            tinymce.init({
                selector: '#editor',
                height: '100%',
                menubar: false,
                branding: false,  // 移除品牌标识
                promotion: false, // 移除升级提示
                plugins: [
                    'autolink', 'lists', 'link', 'image', 'charmap',
                    'searchreplace', 'code', 'fullscreen',
                    'table', 'wordcount', 'checklist'
                ],
                toolbar: 'undo redo | formatselect | ' +
                    'bold italic | alignleft aligncenter ' +
                    'alignright alignjustify | bullist numlist checklist | ' +
                    'removeformat',
                content_style: `
                    body { font-family:Helvetica,Arial,sans-serif; font-size:16px }

                    /* 待办事项样式 */
                    .tox-checklist > li:not(.tox-checklist--hidden) {
                        list-style: none;
                        margin: 0.25em 0;
                        position: relative;
                        text-indent: 0;
                    }

                    .tox-checklist > li:not(.tox-checklist--hidden)::before {
                        content: '';
                        background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cg%20id%3D%22checklist-unchecked%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Crect%20id%3D%22Rectangle%22%20width%3D%2215%22%20height%3D%2215%22%20x%3D%22.5%22%20y%3D%22.5%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%234C4C4C%22%20rx%3D%222%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%0A");
                        background-size: 100%;
                        height: 16px;
                        width: 16px;
                        position: absolute;
                        left: -24px;
                        top: 2px;
                    }

                    .tox-checklist li.tox-checklist--checked::before {
                        background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cg%20id%3D%22checklist-checked%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Crect%20id%3D%22Rectangle%22%20width%3D%2215%22%20height%3D%2215%22%20x%3D%22.5%22%20y%3D%22.5%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%234C4C4C%22%20rx%3D%222%22%2F%3E%3Cpath%20id%3D%22Path%22%20fill%3D%22%234C4C4C%22%20fill-rule%3D%22nonzero%22%20d%3D%22M11.9915%204.1865l.0085%200%20.0214%200c.0839%200%20.1638.0337.2224.0935.0585.0599.0912.1402.0912.2241%200%20.0856-.035.1654-.0972.2244l-5.6712%205.6705c-.0585.0586-.1384.0923-.2224.0923-.0839%200-.1638-.0337-.2223-.0923L3.092%207.284c-.0624-.0585-.0972-.1383-.0972-.2239%200-.0856.0348-.1654.0972-.224.0585-.0586.1384-.0923.2224-.0923.0839%200%20.1638.0337.2223.0923l2.7224%202.7217%205.1727-5.1727c.0585-.0586.1384-.0923.2224-.0923z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%0A");
                    }

                    .tox-checklist li.tox-checklist--checked {
                        text-decoration: line-through;
                        color: #777;
                    }
                `,
                setup: function(editor) {
                    // 编辑器设置完成后的回调
                    editor.on('init', function() {
                        // 设置初始内容
                        editor.setContent(`
                            <h1>TinyMCE 富文本编辑器示例</h1>
                            <p>这是一个使用 TinyMCE 实现的富文本编辑器示例。</p>
                            <h2>主要功能：</h2>
                            <ul>
                                <li>文本格式化（粗体、斜体、下划线等）</li>
                                <li>文本对齐</li>
                                <li>列表（有序和无序）</li>
                                <li>待办事项列表</li>
                                <li>插入链接和图片</li>
                                <li>表格编辑</li>
                            </ul>
                            <p><em>试试看各种编辑功能吧！</em></p>

                            <h2>待办事项示例：</h2>
                            <ul class="tox-checklist">
                                <li>这是一个待办事项</li>
                                <li class="tox-checklist--checked">这是一个已完成的待办事项</li>
                                <li>点击工具栏中的待办事项按钮添加新的待办事项</li>
                            </ul>
                        `);

                        // 调整编辑器高度
                        adjustEditorHeight();
                    });
                }
            });
        }

        // 保存内容到 Android
        function saveContent() {
            var content = tinymce.activeEditor.getContent();
            // 调用 Android 的接口保存内容
            if (typeof Android !== 'undefined') {
                Android.saveContent(content);
            } else {
                alert('内容已保存：\n' + content.substring(0, 100) + '...');
            }
        }

        // 获取 HTML 内容
        function getHtml() {
            var html = tinymce.activeEditor.getContent();
            if (typeof Android !== 'undefined') {
                Android.getHtmlContent(html);
            } else {
                alert('HTML内容：\n' + html.substring(0, 100) + '...');
            }
        }

        // 获取纯文本内容
        function getText() {
            var text = tinymce.activeEditor.getContent({ format: 'text' });
            if (typeof Android !== 'undefined') {
                Android.getTextContent(text);
            } else {
                alert('纯文本内容：\n' + text.substring(0, 100) + '...');
            }
        }

        // 调整编辑器高度
        function adjustEditorHeight() {
            const container = document.querySelector('.editor-container');
            const header = document.querySelector('.editor-header');
            const actions = document.querySelector('.editor-actions');
            const editorContent = document.querySelector('.editor-content');

            if (container && header && actions && editorContent) {
                const containerHeight = container.offsetHeight;
                const headerHeight = header.offsetHeight;
                const actionsHeight = actions.offsetHeight;
                const editorHeight = containerHeight - headerHeight - actionsHeight;

                editorContent.style.height = editorHeight + 'px';
            }
        }

        // 页面加载完成后的其他初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 当窗口大小变化时重新调整
            window.addEventListener('resize', adjustEditorHeight);
        });
    </script>
</body>
</html>
